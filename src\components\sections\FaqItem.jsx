import React, { useState } from 'react';
import { MdAdd, MdRemove } from 'react-icons/md';
// import './FaqItem.css';

const FaqItem = ({ faq }) => {
    const [isOpen, setIsOpen] = useState(false);

    const toggleOpen = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={`faq-item ${isOpen ? 'open' : ''}`}>
            <div className="faq-question" onClick={toggleOpen}>
                <h4>{faq.question}</h4>
                <div className="faq-icon">
                    {isOpen ? <MdRemove /> : <MdAdd />}
                </div>
            </div>
            <div className="faq-answer">
                <p>{faq.answer}</p>
            </div>
        </div>
    );
};

export default FaqItem; 