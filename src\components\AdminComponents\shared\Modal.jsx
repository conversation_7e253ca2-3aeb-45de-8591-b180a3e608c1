import React, { useState } from 'react';
// import './Modal.css';
import { FaCalendarPlus } from 'react-icons/fa';
import { FiHelpCircle } from 'react-icons/fi';

const Modal = ({ fields, data, onClose, onSave, onRenew, renewalMode }) => {
  const [formData, setFormData] = useState(
    data || fields.reduce((acc, field) => ({ ...acc, [field.name]: '' }), {})
  );

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  // Renewal popup logic
  if (renewalMode && data) {
    return (
      <div className="modal-backdrop" onClick={onClose}>
        <div className="modal" onClick={e => e.stopPropagation()}>
          <h3>Renew Contract: {data.customer || data.name || data.contractId}</h3>
          <div style={{marginBottom: 16, color: '#444'}}>Set the new end date and value for this renewal.</div>
          <form onSubmit={handleSubmit}>
            <div className="modal-field">
              <label htmlFor="endDate">New End Date</label>
              <input
                type="date"
                id="endDate"
                name="endDate"
                value={formData.endDate || ''}
                onChange={handleChange}
                required
              />
            </div>
            <div className="modal-field">
              <label htmlFor="value">New Value</label>
              <input
                type="text"
                id="value"
                name="value"
                value={formData.value || ''}
                onChange={handleChange}
                required
              />
            </div>
            <div className="modal-actions">
              <button type="button" className="btn-secondary" onClick={onClose}>Cancel</button>
              <button type="submit" className="btn-primary">Confirm Renewal</button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  const title = data && data.id ? 'Edit' : 'Add';

  const isMobile = window.innerWidth <= 700;

  return (
    <div className="modal-backdrop" onClick={onClose}>
      <div className="modal" onClick={e => e.stopPropagation()}>
        <h3>{title}</h3>
        <form onSubmit={handleSubmit}>
          <div className="modal-grid">
            {fields.filter(field => {
              if (!field.dependsOn) return true;
              const dep = field.dependsOn;
              return formData[dep.field] === dep.value;
            }).map(field => (
              <div key={field.name} className={`modal-field${field.type === 'select' ? ' modal-select-field' : ''}`}>
                <label htmlFor={field.name} className={`modal-label${field.type === 'select' ? ' modal-label-select' : ''}`}>
                  {field.icon && <span className="modal-label-icon">{field.icon}</span>}
                  {field.label}
                </label>
                {field.type === 'select' ? (
                  <div className="modal-select-options">
                    {(field.options || []).map(opt => {
                      const value = typeof opt === 'object' ? opt.value : opt;
                      const label = typeof opt === 'object' ? opt.label : opt;
                      return (
                        <label key={value} className="modal-select-option">
                          <input
                            type="radio"
                            name={field.name}
                            value={value}
                            checked={formData[field.name] === value}
                            onChange={handleChange}
                            className="modal-select-radio"
                          />
                          <span>{label}</span>
                        </label>
                      );
                    })}
                  </div>
                ) : (
                  <input
                    type={field.type || 'text'}
                    id={field.name}
                    name={field.name}
                    value={formData[field.name]}
                    onChange={handleChange}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="modal-actions">
            {onRenew && data && (
                <button type="button" className="btn-renew" onClick={() => onRenew(data.id)}>
                    <FaCalendarPlus /> Renew
                </button>
            )}
            <button type="button" className="btn-secondary" onClick={onClose}>Cancel</button>
            <button type="submit" className="btn-primary">{title}</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Modal; 