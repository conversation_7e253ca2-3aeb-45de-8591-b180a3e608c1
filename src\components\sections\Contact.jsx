import React, { useState } from 'react';
import { Md<PERSON>hone, MdArrowForward } from 'react-icons/md';
// import './Contact.css';

const initialForm = {
  firstName: '',
  lastName: '',
  email: '',
  company: '',
  jobTitle: '',
  phone: '',
  country: 'India',
  notUSGov: false,
  agree: false,
};

const phoneNumbers = [
  { label: 'US', number: '******.980.2199' },
  { label: 'IN', number: '+91.************' },
];

const Contact = () => {
  const [form, setForm] = useState(initialForm);
  const [touched, setTouched] = useState({});
  const [submitted, setSubmitted] = useState(false);

  const handleChange = e => {
    const { name, value, type, checked } = e.target;
    setForm(f => ({ ...f, [name]: type === 'checkbox' ? checked : value }));
  };
  const handleBlur = e => {
    setTouched(t => ({ ...t, [e.target.name]: true }));
  };
  const validate = () => {
    const errors = {};
    if (!form.firstName) errors.firstName = 'First name required';
    if (!form.lastName) errors.lastName = 'Last name required';
    if (!form.email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(form.email)) errors.email = 'Valid email required';
    if (!form.company) errors.company = 'Company required';
    if (!form.jobTitle) errors.jobTitle = 'Job title required';
    if (!form.phone) errors.phone = 'Phone required';
    if (!form.notUSGov) errors.notUSGov = 'Required';
    if (!form.agree) errors.agree = 'Required';
    return errors;
  };
  const errors = validate();
  const isValid = Object.keys(errors).length === 0;

  const handleSubmit = e => {
    e.preventDefault();
    setTouched({
      firstName: true, lastName: true, email: true, company: true, jobTitle: true, phone: true, notUSGov: true, agree: true
    });
    setSubmitted(true);
    if (isValid) {
      alert('Thank you! We will contact you soon.');
      setForm(initialForm);
      setTouched({});
      setSubmitted(false);
    }
  };

  return (
    <section id="contact" className="contact-section contact-advanced-bg">
      <div className="contact-advanced-grid container">
        {/* Left Column */}
        <div className="contact-info-col">
          <h2 className="contact-title">Contact HCL Aftermarket Cloud</h2>
          <p className="contact-desc">
            Thank you for your interest in HCL Aftermarket Cloud. We welcome the opportunity to showcase our solutions and help you explore how we can transform your aftermarket operations. Whether you're looking to <b>schedule a personalized demo</b> or require expert guidance through a <b>free consultation</b>, our team is here to assist you every step of the way.<br /><br />
            Please fill out the form or <b>call us at</b>:
          </p>
          <div className="contact-phones">
            {phoneNumbers.map(p => (
              <a key={p.number} href={`tel:${p.number.replace(/\D/g, '')}`} className="contact-phone-link">
                <MdPhone className="contact-phone-icon" /> {p.number}
              </a>
            ))}
          </div>
          <p className="contact-desc contact-desc-bottom">
            One of our dedicated representatives will get in touch with you shortly to discuss your specific needs and set up a convenient time for your demo or consultation. We look forward to partnering with you to drive excellence in your aftermarket business.
          </p>
        </div>
        {/* Right Column: Form */}
        <div className="contact-form-col">
          <form className="contact-form-advanced" onSubmit={handleSubmit} noValidate>
            <div className="form-row">
              <div className="form-floating-group">
                <input type="text" name="firstName" id="firstName" value={form.firstName} onChange={handleChange} onBlur={handleBlur} className={errors.firstName && (touched.firstName || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="firstName">First Name*</label>
                {errors.firstName && (touched.firstName || submitted) && <span className="form-error">{errors.firstName}</span>}
              </div>
              <div className="form-floating-group">
                <input type="text" name="lastName" id="lastName" value={form.lastName} onChange={handleChange} onBlur={handleBlur} className={errors.lastName && (touched.lastName || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="lastName">Last Name*</label>
                {errors.lastName && (touched.lastName || submitted) && <span className="form-error">{errors.lastName}</span>}
              </div>
            </div>
            <div className="form-row">
              <div className="form-floating-group">
                <input type="email" name="email" id="email" value={form.email} onChange={handleChange} onBlur={handleBlur} className={errors.email && (touched.email || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="email">Business Email Address*</label>
                {errors.email && (touched.email || submitted) && <span className="form-error">{errors.email}</span>}
              </div>
              <div className="form-floating-group">
                <input type="text" name="company" id="company" value={form.company} onChange={handleChange} onBlur={handleBlur} className={errors.company && (touched.company || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="company">Company*</label>
                {errors.company && (touched.company || submitted) && <span className="form-error">{errors.company}</span>}
              </div>
            </div>
            <div className="form-row">
              <div className="form-floating-group">
                <input type="text" name="jobTitle" id="jobTitle" value={form.jobTitle} onChange={handleChange} onBlur={handleBlur} className={errors.jobTitle && (touched.jobTitle || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="jobTitle">Job Title*</label>
                {errors.jobTitle && (touched.jobTitle || submitted) && <span className="form-error">{errors.jobTitle}</span>}
              </div>
              <div className="form-floating-group">
                <input type="text" name="phone" id="phone" value={form.phone} onChange={handleChange} onBlur={handleBlur} className={errors.phone && (touched.phone || submitted) ? 'invalid' : ''} required placeholder=" " />
                <label htmlFor="phone">Phone/Mobile*</label>
                {errors.phone && (touched.phone || submitted) && <span className="form-error">{errors.phone}</span>}
              </div>
            </div>
            <div className="form-row">
              <div className="form-floating-group full-width">
                <select name="country" id="country" value={form.country} onChange={handleChange}>
                  <option value="India">India</option>
                  <option value="United States">United States</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Germany">Germany</option>
                  <option value="France">France</option>
                  <option value="Other">Other</option>
                </select>
                <label htmlFor="country">Country</label>
              </div>
            </div>
            <div className="form-row checkbox-row">
              <label className={`custom-checkbox-label${errors.notUSGov && (touched.notUSGov || submitted) ? ' invalid' : ''}`}>
                <input type="checkbox" name="notUSGov" checked={form.notUSGov} onChange={handleChange} />
                <span className="custom-checkbox" />
                <span className="checkbox-main-label">I am not a U.S. Federal Government employee or agency, nor am I submitting on behalf of one.</span>
              </label>
              {errors.notUSGov && (touched.notUSGov || submitted) && <span className="form-error">{errors.notUSGov}</span>}
              <div className="checkbox-desc">
                HCLSoftware provides software and services to the U.S. Federal Government through a dedicated team. <a href="#">You can learn more and reach the team here.</a>
              </div>
            </div>
            <div className="form-row checkbox-row">
              <label className={`custom-checkbox-label${errors.agree && (touched.agree || submitted) ? ' invalid' : ''}`}>
                <input type="checkbox" name="agree" checked={form.agree} onChange={handleChange} />
                <span className="custom-checkbox" />
                <span className="checkbox-main-label">I agree to HCL's <a href="#">Privacy Statement</a>.</span>
              </label>
              {errors.agree && (touched.agree || submitted) && <span className="form-error">{errors.agree}</span>}
              <div className="checkbox-desc small">
                HCL is collecting this information for its legitimate interests and you acknowledge that we may contact you about products or services that could be of interest to you. You may unsubscribe from our emails at any time.
              </div>
            </div>
            <button type="submit" className="contact-submit-btn" disabled={!isValid && submitted}>
              Submit <MdArrowForward className="submit-arrow" />
            </button>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Contact; 