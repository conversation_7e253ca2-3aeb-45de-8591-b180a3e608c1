import React from 'react';
import {
    MdPointOfSale, MdRequestQuote, MdStore, MdLocalShipping, MdImportContacts,
    MdShoppingCart, MdDirectionsCar, MdEventAvailable, MdBuildCircle, MdRecycling,
    MdInventory2, MdWarehouse, MdGarage, MdDomain, MdHomeWork, MdFactCheck,
    MdNotifications, MdDynamicFeed, MdTranslate, MdManageAccounts, MdAssignmentInd,
    MdLogin, MdHub, MdDashboard, MdBusiness, MdBadge, MdPinDrop, MdSchedule,
    MdSupportAgent, MdWidgets, MdSettings, MdConstruction, MdBuild, MdVerifiedUser,
    MdRequestPage, MdChecklist, MdDeviceHub, MdSensors, MdEmail, MdSms, MdPayment,
    MdAttachMoney, MdRule
} from 'react-icons/md';

const iconMap = {
    point_of_sale: <MdPointOfSale />,
    request_quote: <MdRequestQuote />,
    store: <MdStore />,
    local_shipping: <MdLocalShipping />,
    import_contacts: <MdImportContacts />,
    shopping_cart: <MdShoppingCart />,
    directions_car: <MdDirectionsCar />,
    event_available: <MdEventAvailable />,
    build_circle: <MdBuildCircle />,
    recycling: <MdRecycling />,
    inventory_2: <MdInventory2 />,
    warehouse: <MdWarehouse />,
    garage: <MdGarage />,
    domain: <MdDomain />,
    home_work: <MdHomeWork />,
    fact_check: <MdFactCheck />,
    notifications: <MdNotifications />,
    dynamic_feed: <MdDynamicFeed />,
    translate: <MdTranslate />,
    manage_accounts: <MdManageAccounts />,
    assignment_ind: <MdAssignmentInd />,
    login: <MdLogin />,
    hub: <MdHub />,
    monitoring: <MdDashboard />,
    business: <MdBusiness />,
    badge: <MdBadge />,
    pin_drop: <MdPinDrop />,
    schedule: <MdSchedule />,
    support_agent: <MdSupportAgent />,
    widgets: <MdWidgets />,
    settings: <MdSettings />,
    construction: <MdConstruction />,
    build: <MdBuild />,
    verified_user: <MdVerifiedUser />,
    request_page: <MdRequestPage />,
    checklist: <MdChecklist />,
    device_hub: <MdDeviceHub />,
    sensors: <MdSensors />,
    email: <MdEmail />,
    sms: <MdSms />,
    payment: <MdPayment />,
    attach_money: <MdAttachMoney />,
    rule: <MdRule />,
};

const DynamicIcon = ({ name }) => {
    const icon = iconMap[name];
    return icon || null;
};

export default DynamicIcon; 