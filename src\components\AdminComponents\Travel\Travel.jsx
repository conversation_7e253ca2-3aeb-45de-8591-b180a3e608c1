import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer as MuiTableContainer, CardContent, Divider, Dialog, DialogTitle, DialogContent,
    DialogActions, Grid, RadioGroup, Radio, FormLabel,
    AppBar, Toolbar
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, <PERSON><PERSON>, <PERSON><PERSON>ist,
    <PERSON><PERSON><PERSON>, Schedule, People, CheckCircle, Cancel, MoreV<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ravel, FlightTakeoff, Hotel, Train, DirectionsCar,
    Description, Home, Business, AttachMoney, PriceCheck
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';

// --- DATA ---
const initialTravels = [
    { id: 101, customerId: 1, customerName: 'Reman', travelerName: 'Alice Johnson', purpose: 'Q3 Business Review', projectName: 'Project Phoenix', destinationCity: 'New York', startDate: '2025-08-15', endDate: '2025-08-20', responsibility: 'HCL', billingMethod: '', modeOfTravel: 'Flight', totalAmount: 2500, amountPaid: 2500, paymentStatus: 'Paid', status: 'Approved' },
    { id: 102, customerId: 3, customerName: 'Wirtgen-FSM', travelerName: 'Peter Jones', purpose: 'On-site Training', projectName: 'Quantum Leap Initiative', destinationCity: 'London', startDate: '2025-09-05', endDate: '2025-09-10', responsibility: 'Customer', billingMethod: 'Per Diem', modeOfTravel: 'Flight', totalAmount: 3500, amountPaid: 1000, paymentStatus: 'Partially Paid', status: 'Pending' },
    { id: 103, customerId: 3, customerName: 'Wirtgen-FSM', travelerName: 'Emily White', purpose: 'Sales Pitch', projectName: 'New Financial Product', destinationCity: 'Frankfurt', startDate: '2025-10-01', endDate: '2025-10-03', responsibility: 'Customer', billingMethod: 'Actuals', modeOfTravel: 'Train', totalAmount: 1800, amountPaid: 0, paymentStatus: 'Unpaid', status: 'Completed' },
    { id: 104, customerId: 2, customerName: 'Prevost-DMS', travelerName: 'Bob Brown', purpose: 'System Integration', projectName: 'HealthNet Rollout', destinationCity: 'Chicago', startDate: '2025-11-10', endDate: '2025-11-15', responsibility: 'HCL', billingMethod: '', modeOfTravel: 'Car', totalAmount: 1500, amountPaid: 0, paymentStatus: 'Unpaid', status: 'Cancelled' },
];

const TRAVEL_COLUMNS = [
    { key: 'travelerName', label: 'Traveler', groupable: true },
    { key: 'customerName', label: 'Customer', groupable: true },
    { key: 'purpose', label: 'Purpose', groupable: false },
    { key: 'destinationCity', label: 'Destination', groupable: true },
    { key: 'startDate', label: 'Start Date', groupable: false },
    { key: 'totalAmount', label: 'Total Amount', groupable: false },
    { key: 'amountPaid', label: 'Amount Paid', groupable: false },
    { key: 'paymentStatus', label: 'Payment Status', groupable: true },
    { key: 'status', label: 'Trip Status', groupable: true },
    { key: 'responsibility', label: 'Responsibility', groupable: true },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];
const PAYMENT_STATUSES = ['Unpaid', 'Partially Paid', 'Paid'];
const TRIP_STATUSES = ['Pending', 'Approved', 'Completed', 'Cancelled'];

// --- TRAVEL PAGE COMPONENTS ---
const TravelActionButtons = ({ travel, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(travel)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(travel)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([travel.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

// Card footer details for TravelCard
const TravelCard = ({ travel, onClick, isSelected, onSelect, isChecked, ...props }) => {
    return (
        <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
            <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(travel.id)} />
            <AdminComponents.CardActionContainer><TravelActionButtons travel={travel} {...props} /></AdminComponents.CardActionContainer>
            <AdminComponents.PaddedCardContent>
                <Box>
                    <Typography variant="h6" component="div" fontWeight="bold" noWrap>{travel.customerName}</Typography>
                    <Typography color="text.secondary" noWrap gutterBottom>{travel.purpose}</Typography>
                    <Box display="flex" alignItems="center" gap={1}>
                        <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(travel.status) }} label={travel.status} size="small" />
                        <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(travel.paymentStatus) }} label={travel.paymentStatus} size="small" />
                    </Box>
                </Box>
                <AdminComponents.CardDivider />
                <Box>
                    <Typography variant="body2"><b>Traveler:</b> {travel.travelerName}</Typography>
                    <Typography variant="body2"><b>Destination:</b> {travel.destinationCity}</Typography>
                    <Typography variant="body2"><b>Start:</b> {travel.startDate} <b>End:</b> {travel.endDate}</Typography>
                    <Typography variant="body2"><b>Mode:</b> {travel.modeOfTravel}</Typography>
                    <Typography variant="body2"><b>Amount:</b> ${travel.totalAmount.toLocaleString()}</Typography>
                    <Typography variant="body2"><b>Project:</b> {travel.projectName}</Typography>
                    <Typography variant="body2"><b>Responsibility:</b> {travel.responsibility}</Typography>
                </Box>
            </AdminComponents.PaddedCardContent>
        </AdminComponents.CardBase>
    );
};

const TravelCompactCard = ({ travel, onClick, isSelected, onSelect, isChecked, ...props }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(travel.id)} />
        <AdminComponents.CardActionContainer><TravelActionButtons travel={travel} {...props} /></AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <Box>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{travel.purpose}</Typography>
                <Typography variant="caption" color="text.secondary" noWrap>{travel.travelerName} to {travel.destinationCity}</Typography>
                <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                    <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(travel.status) }} label={travel.status} size="small" />
                    <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(travel.paymentStatus) }} label={travel.paymentStatus} size="small" />
                </Box>
            </Box>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{travel.customerName}</Typography>
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const TravelListItem = ({ travel, onClick, isSelected, onSelect, isChecked, ...props }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(travel.id)} />
            <Box><Typography fontWeight="bold">{travel.purpose}</Typography><Typography variant="body2" color="text.secondary">{travel.customerName}</Typography></Box>
            <Typography variant="body2">{travel.travelerName}</Typography>
            <Typography variant="body2">{travel.destinationCity}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(travel.status) }} label={travel.status} />
            <Box justifySelf="end"><TravelActionButtons travel={travel} {...props} /></Box>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const TravelTable = ({ travels, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, ...props }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (travel, colKey) => {
        const value = travel[colKey];
        switch (colKey) {
            case 'status': case 'paymentStatus': return <AdminComponents.StatusBadge ownerState={{ status: getTravelStatusColor(value) }} label={value} size="small" />;
            case 'travelerName': return <AdminComponents.ClickableTypography component="span" onClick={() => onRowClick(travel)}>{value}</AdminComponents.ClickableTypography>;
            case 'totalAmount': case 'amountPaid': return `$${Number(value).toLocaleString()}`;
            default: return value || '-';
        }
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        if (addLog) addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    // No grouping for now, but can add similar to Customers.jsx if needed
    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < travels.length} checked={travels.length > 0 && selectedIds.length === travels.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {TRAVEL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {travels.map(travel => (
                        <TableRow key={travel.id} hover selected={selectedId === travel.id} onClick={() => onRowClick(travel)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(travel.id)} onChange={() => onSelectOne(travel.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => <AdminComponents.ContentTableCell key={colKey}>{renderCellContent(travel, colKey)}</AdminComponents.ContentTableCell>)}
                            <AdminComponents.ActionTableCell align="center"><TravelActionButtons travel={travel} {...props} /></AdminComponents.ActionTableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

const TravelDetailsForm = ({ initialData, customers, onSubmit, onCancel, isViewOnly }) => {
    const [formData, setFormData] = useState(initialData);
    useEffect(() => { setFormData(initialData); }, [initialData]);
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => {
            const updated = { ...prev, [name]: value };
            if (name === 'customerId') updated.customerName = customers.find(c => c.id === value)?.name || '';
            if (name === 'totalAmount' || name === 'amountPaid') {
                const total = name === 'totalAmount' ? parseFloat(value) : parseFloat(prev.totalAmount);
                const paid = name === 'amountPaid' ? parseFloat(value) : parseFloat(prev.amountPaid);
                if (paid >= total) updated.paymentStatus = 'Paid';
                else if (paid > 0) updated.paymentStatus = 'Partially Paid';
                else updated.paymentStatus = 'Unpaid';
            }
            return updated;
        });
    };
    const handleSubmit = (e) => { e.preventDefault(); onSubmit(formData); };
    return (
        <Dialog open fullWidth maxWidth="md" onClose={onCancel}>
            <DialogTitle>{isViewOnly ? 'View Travel Details' : formData.id ? 'Edit Travel Details' : 'Add New Travel Request'}<IconButton onClick={onCancel} style={{ position: 'absolute', right: 8, top: 8 }}><Close /></IconButton></DialogTitle>
            <DialogContent dividers>
                <Box component="form" id="travel-form" onSubmit={handleSubmit} className="travel-form-root">
                    <Grid container spacing={3}>
                        <Grid item xs={12}><Typography variant="subtitle2" color="primary">Trip Details</Typography><Divider /></Grid>
                        <Grid item xs={12} sm={6}><TextField name="purpose" label="Purpose of Travel" value={formData.purpose} onChange={handleChange} fullWidth required disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={6}><TextField name="projectName" label="Project / Event Name" value={formData.projectName} onChange={handleChange} fullWidth disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={6}><FormControl fullWidth required disabled={isViewOnly}><InputLabel>Customer</InputLabel><Select name="customerId" label="Customer" value={formData.customerId} onChange={handleChange}>{customers.map(c => <MenuItem key={c.id} value={c.id}>{c.name}</MenuItem>)}</Select></FormControl></Grid>
                        <Grid item xs={12} sm={6}><TextField name="travelerName" label="Traveler's Name" value={formData.travelerName} onChange={handleChange} fullWidth required disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={6}><TextField name="destinationCity" label="Destination City" value={formData.destinationCity} onChange={handleChange} fullWidth required disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={6}><FormControl fullWidth disabled={isViewOnly}><InputLabel>Trip Status</InputLabel><Select name="status" label="Trip Status" value={formData.status} onChange={handleChange}>{TRIP_STATUSES.map(s => <MenuItem key={s} value={s}>{s}</MenuItem>)}</Select></FormControl></Grid>
                        <Grid item xs={12} sm={6}><TextField name="startDate" label="Start Date" type="date" value={formData.startDate} onChange={handleChange} fullWidth required disabled={isViewOnly} InputLabelProps={{ shrink: true }} /></Grid>
                        <Grid item xs={12} sm={6}><TextField name="endDate" label="End Date" type="date" value={formData.endDate} onChange={handleChange} fullWidth required disabled={isViewOnly} InputLabelProps={{ shrink: true }} /></Grid>
                        <Grid item xs={12} mt={2}><Typography variant="subtitle2" color="primary">Logistics & Billing</Typography><Divider /></Grid>
                        <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly}><InputLabel>Mode of Travel</InputLabel><Select name="modeOfTravel" label="Mode of Travel" value={formData.modeOfTravel} onChange={handleChange}><MenuItem value="Flight">Flight</MenuItem><MenuItem value="Train">Train</MenuItem><MenuItem value="Car">Car</MenuItem></Select></FormControl></Grid>
                        <Grid item xs={12} sm={8}><TextField name="accommodationDetails" label="Accommodation Details" value={formData.accommodationDetails} onChange={handleChange} fullWidth disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={4}><FormControl component="fieldset" disabled={isViewOnly}><FormLabel component="legend">Responsibility</FormLabel><RadioGroup row name="responsibility" value={formData.responsibility} onChange={handleChange}><FormControlLabel value="HCL" control={<Radio />} label="HCL" /><FormControlLabel value="Customer" control={<Radio />} label="Customer's" /></RadioGroup></FormControl></Grid>
                        <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly || formData.responsibility !== 'Customer'}><InputLabel>Billing Method</InputLabel><Select name="billingMethod" label="Billing Method" value={formData.billingMethod} onChange={handleChange} required={formData.responsibility === 'Customer'}><MenuItem value=""><em>None</em></MenuItem><MenuItem value="Per Diem">Per Diem</MenuItem><MenuItem value="Actuals">Actuals</MenuItem></Select></FormControl></Grid>
                        <Grid item xs={12} sm={4}><FormControl fullWidth disabled={isViewOnly}><InputLabel>Payment Status</InputLabel><Select name="paymentStatus" label="Payment Status" value={formData.paymentStatus} onChange={handleChange}>{PAYMENT_STATUSES.map(s => <MenuItem key={s} value={s}>{s}</MenuItem>)}</Select></FormControl></Grid>
                        <Grid item xs={12} sm={6}><TextField name="totalAmount" label="Total Amount (USD)" type="number" value={formData.totalAmount} onChange={handleChange} fullWidth disabled={isViewOnly} /></Grid>
                        <Grid item xs={12} sm={6}><TextField name="amountPaid" label="Amount Paid (USD)" type="number" value={formData.amountPaid} onChange={handleChange} fullWidth disabled={isViewOnly} /></Grid>
                    </Grid>
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={onCancel}>{isViewOnly ? 'Close' : 'Cancel'}</Button>
                {!isViewOnly && <Button type="submit" form="travel-form" variant="contained">Save Details</Button>}
            </DialogActions>
        </Dialog>
    );
};

const getTravelStatusColor = (status) => {
    // Map travel/payment statuses to active/inactive/warning colors
    if (["Approved", "Completed", "Paid"].includes(status)) return "Active";
    if (["Cancelled", "Unpaid"].includes(status)) return "Inactive";
    if (["Pending", "Partially Paid"].includes(status)) return "Expiring";
    return status;
};

const TravelGraph = ({ travel, chartType, onChartTypeChange }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);
    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && travel) {
            const ctx = chartRef.current.getContext('2d');
            const remainingAmount = travel.totalAmount - travel.amountPaid;
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Amount Paid', 'Amount Remaining'],
                    datasets: [{
                        label: 'Financials ($)',
                        data: [travel.amountPaid, remainingAmount > 0 ? remainingAmount : 0],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                        borderWidth: chartType === 'line' ? 2 : 1,
                        pointBackgroundColor: theme.palette.primary.main,
                        tension: 0.1
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false, plugins: { title: { display: true, text: `${travel.purpose} - Financials` } } }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [travel, chartType]);
    return (
        <Box height="100%" display="flex" flexDirection="column" gap={2}>
            <AdminComponents.ChartTypeSelectorContainer>
                <AdminComponents.StyledToggleButtonGroup
                    value={chartType}
                    exclusive
                    onChange={(e, newType) => newType && onChartTypeChange(newType)}
                    size="small"
                    fullWidth
                >
                    <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                    <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                    <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                    <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                </AdminComponents.StyledToggleButtonGroup>
            </AdminComponents.ChartTypeSelectorContainer>
            {travel ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <BarChart className="travel-graph-icon" />
                    <Typography>Select a trip to see financial graph</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </Box>
    );
};

// --- MAIN APP COMPONENT ---
const Travel = () => {
    const [travels, setTravels] = useState(initialTravels);
    const [selectedTravel, setSelectedTravel] = useState(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [formMode, setFormMode] = useState('edit'); // 'edit' or 'view'
    const [editingTravel, setEditingTravel] = useState(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('startDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [columnOrder, setColumnOrder] = useState(TRAVEL_COLUMNS.map(c => c.key));
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [chartType, setChartType] = useState('bar');

    // Quick, staged, and active filter state
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    // Dummy customers for form dropdown
    const customers = [
        { id: 1, name: 'Reman' },
        { id: 2, name: 'Prevost-DMS' },
        { id: 3, name: 'Quantum Solutions' },
    ];

    // Quick filter options: unique statuses and destination cities
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(travels.map(t => t.status))];
        const destinations = [...new Set(travels.map(t => t.destinationCity))];
        return [...statuses, ...destinations];
    }, [travels]);

    const addLog = (logEntry) => { /* Optionally implement logging */ };

    const processedTravels = useMemo(() => {
        let current = [...travels];
        // Filter by summary card selection
        if (summaryFilter) {
            if (["Paid", "Unpaid", "Partially Paid"].includes(summaryFilter)) {
                current = current.filter(t => t.paymentStatus === summaryFilter);
            } else {
                current = current.filter(t => t.status === summaryFilter);
            }
        }
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(t => Object.values(t).some(val => String(val).toLowerCase().includes(term)));
        }
        if (activeFilters.length > 0) {
            current = current.filter(travel => activeFilters.every(filter => {
                const { field, operator, value } = filter;
                const travelValue = String(travel[field]).toLowerCase();
                const filterValue = String(value).toLowerCase();
                switch (operator) {
                    case 'Equals': return travelValue === filterValue;
                    case 'Not Equals': return travelValue !== filterValue;
                    case 'Contains': return travelValue.includes(filterValue);
                    case 'Starts With': return travelValue.startsWith(filterValue);
                    case 'Ends With': return travelValue.endsWith(filterValue);
                    default: return true;
                }
            }));
        }
        return [...current].sort((a, b) => {
            const valA = a[sortColumn]; const valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (new Date(valA) - new Date(valB)) : (new Date(valB) - new Date(valA));
        });
    }, [travels, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter]);
    
    const displayTravel = useMemo(() => {
        const isSelectedVisible = processedTravels.some(c => c.id === selectedTravel?.id);
        if (isSelectedVisible) return selectedTravel;
        return processedTravels.length > 0 ? processedTravels[0] : null;
    }, [processedTravels, selectedTravel]);

    // --- Filter Handlers ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleAddQuickFilter = (value) => {
        // If value is a status, field is 'status', else 'destinationCity'
        const statusSet = new Set(travels.map(t => t.status));
        const field = statusSet.has(value) ? 'status' : 'destinationCity';
        setStagedFilters([...stagedFilters, { field, operator: 'Equals', value, id: Date.now() }]);
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null);
    };
    const handleDeleteStagedFilter = (id) => setStagedFilters(stagedFilters.filter(f => f.id !== id));
    const handleDeleteActiveFilter = (id) => setActiveFilters(activeFilters.filter(f => f.id !== id));

    const handleOpenForm = (travel, mode = 'edit') => {
        setFormMode(mode);
        setEditingTravel(travel || {
            id: null, customerId: '', customerName: '', travelerName: '', purpose: '', projectName: '', destinationCity: '',
            startDate: '', endDate: '', responsibility: 'HCL', billingMethod: '', modeOfTravel: 'Flight',
            totalAmount: 0, amountPaid: 0, paymentStatus: 'Unpaid', status: 'Pending',
        });
        setIsFormOpen(true);
    };
    
    const handleFormSubmit = (formData) => {
        if (formData.id) {
            setTravels(travels.map(t => t.id === formData.id ? formData : t));
            addLog({ user: 'Admin', action: 'updated a travel record for', target: formData.customerName });
        } else {
            const newTravel = { ...formData, id: Date.now() };
            setTravels([newTravel, ...travels]);
            addLog({ user: 'Admin', action: 'added a new travel record for', target: formData.customerName });
        }
        setIsFormOpen(false);
    };

    const handleDeleteRequest = (ids) => {
        setTravels(travels.filter(t => !ids.includes(t.id)));
        addLog({ user: 'Admin', action: `Deleted ${ids.length} travel record(s).` });
        setSelectedIds([]);
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedTravels.map(t => t.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) setIsGraphVisible(false);
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleGraphToggle = () => { setIsGraphVisible(prev => !prev); setIsSidebarOpen(false); };
    const handleSummaryCardClick = (statusOrPayment) => {
        setSummaryFilter(prev => (prev === statusOrPayment ? null : statusOrPayment));
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };
    // Update summaryStats to include payment status counts
    const summaryStats = useMemo(() => ({
        Total: travels.length,
        Pending: travels.filter(t => t.status === 'Pending').length,
        Approved: travels.filter(t => t.status === 'Approved').length,
        Paid: travels.filter(t => t.paymentStatus === 'Paid').length,
        Unpaid: travels.filter(t => t.paymentStatus === 'Unpaid').length,
        PartiallyPaid: travels.filter(t => t.paymentStatus === 'Partially Paid').length,
    }), [travels]);
    const handleColumnVisibilityChange = (columnKey) => { setColumnOrder(prev => prev.includes(columnKey) ? prev.filter(key => key !== columnKey && prev.length > 1) : [...prev, columnKey]); };
    const handleGroupByChange = (key) => { setGroupByKeys(prev => prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]); };
    // Grid settings: select all/deselect all
    const handleSelectAllColumns = () => setColumnOrder(TRAVEL_COLUMNS.map(c => c.key));
    const handleDeselectAllColumns = () => setColumnOrder([TRAVEL_COLUMNS[0].key]); // Always keep at least one column

    const renderCurrentView = () => {
        const commonProps = { onDelete: handleDeleteRequest, onEdit: (travel) => handleOpenForm(travel, 'edit'), onView: (travel) => handleOpenForm(travel, 'view') };
        if (processedTravels.length === 0) return <AdminComponents.CenteredMessage component={Paper}><AdminComponents.LargeIcon color="disabled" /><Typography variant="h6">No Matching Travel Records</Typography></AdminComponents.CenteredMessage>;
        switch (viewMode) {
            case 'table': return <TravelTable travels={processedTravels} onRowClick={setSelectedTravel} onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }} sortColumn={sortColumn} sortDirection={sortDirection} selectedId={selectedTravel?.id} selectedIds={selectedIds} onSelectAll={handleSelectAll} onSelectOne={handleSelectOne} columnOrder={columnOrder} setColumnOrder={setColumnOrder} addLog={addLog} groupByKeys={groupByKeys} {...commonProps} />;
            case 'compact': return <AdminComponents.CompactView>{processedTravels.map(t => <TravelCompactCard key={t.id} travel={t} onClick={() => setSelectedTravel(t)} isSelected={selectedTravel?.id === t.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(t.id)} {...commonProps} />)}</AdminComponents.CompactView>;
            case 'list': return <AdminComponents.ListView>{processedTravels.map(t => <TravelListItem key={t.id} travel={t} onClick={() => setSelectedTravel(t)} isSelected={selectedTravel?.id === t.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(t.id)} {...commonProps} />)}</AdminComponents.ListView>;
            case 'cards': default: return <AdminComponents.GridView>{processedTravels.map(t => <TravelCard key={t.id} travel={t} onClick={() => setSelectedTravel(t)} isSelected={selectedTravel?.id === t.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(t.id)} {...commonProps} />)}</AdminComponents.GridView>;
        }
    };
    
    const sidebarContent = sidebarMode === 'search' ? (
        <>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                <AdminComponents.QuickFilterContainer>
                    {quickFilterOptions.map(opt => (
                        <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                    ))}
                </AdminComponents.QuickFilterContainer>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                <FormControl fullWidth size="small">
                    <InputLabel>Field</InputLabel>
                    <Select
                        value={filterBuilder.field}
                        label="Field"
                        onChange={e => setFilterBuilder(p => ({ ...p, field: e.target.value }))}
                        MenuProps={{ disablePortal: true }}
                    >
                        {TRAVEL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                    </Select>
                </FormControl>
                <FormControl fullWidth size="small">
                    <InputLabel>Operator</InputLabel>
                    <Select
                        value={filterBuilder.operator}
                        label="Operator"
                        onChange={e => setFilterBuilder(p => ({ ...p, operator: e.target.value }))}
                        MenuProps={{ disablePortal: true }}
                    >
                        {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                    </Select>
                </FormControl>
                <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(p => ({ ...p, value: e.target.value }))}/>
                <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                <AdminComponents.FilterChipContainer>
                    {stagedFilters.length > 0 ? stagedFilters.map(f => (
                        <Chip key={f.id} label={`${TRAVEL_COLUMNS.find(c=>c.key === f.field)?.label || f.field} ${f.operator} "${f.value}"`} onDelete={() => handleDeleteStagedFilter(f.id)} />
                    )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                </AdminComponents.FilterChipContainer>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                <AdminComponents.FilterChipContainer>
                    {activeFilters.length > 0 ? activeFilters.map(f => (
                        <Chip key={f.id} label={`${TRAVEL_COLUMNS.find(c=>c.key === f.field)?.label || f.field} ${f.operator} "${f.value}"`} onDelete={() => handleDeleteActiveFilter(f.id)} />
                    )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                </AdminComponents.FilterChipContainer>
            </AdminComponents.SidebarSection>
        </>
    ) : (
        <>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                <AdminComponents.ColumnActionContainer>
                    <Button size="small" onClick={handleSelectAllColumns}>Select All</Button>
                    <Button size="small" onClick={handleDeselectAllColumns}>Deselect All</Button>
                </AdminComponents.ColumnActionContainer>
                <AdminComponents.ColumnVisibilityContainer>
                    {TRAVEL_COLUMNS.map(col => (
                        <FormControlLabel
                            key={col.key}
                            control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                            label={col.label}
                        />
                    ))}
                </AdminComponents.ColumnVisibilityContainer>
            </AdminComponents.SidebarSection>
            <AdminComponents.SidebarSection>
                <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                <AdminComponents.FilterChipContainer>
                    {groupByKeys.length > 0 ? groupByKeys.map(key => (
                        <Chip
                            key={key}
                            label={TRAVEL_COLUMNS.find(c => c.key === key)?.label}
                            onDelete={() => handleGroupByChange(key)}
                        />
                    )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                </AdminComponents.FilterChipContainer>
                <AdminComponents.ColumnVisibilityContainer>
                    {TRAVEL_COLUMNS.filter(c => c.groupable).map(col => (
                        <FormControlLabel
                            key={col.key}
                            control={
                                <Checkbox
                                    checked={groupByKeys.includes(col.key)}
                                    onChange={() => handleGroupByChange(col.key)}
                                />
                            }
                            label={col.label}
                        />
                    ))}
                </AdminComponents.ColumnVisibilityContainer>
            </AdminComponents.SidebarSection>
        </>
    );
    const sidebarFooter = sidebarMode === 'search' ? (
        <>
            <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
            <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
        </>
    ) : <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>;

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <Box display="flex" width="100%" flexGrow={1}>
                    <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.MainContentArea>
                            <AdminComponents.TopSectionWrapper>
                                <AdminComponents.TopSectionContent>
                                    <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                            <AdminComponents.SummaryAvatar variant="total"><CardTravel /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.Total}</Typography><Typography variant="body2">Total Travels</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryCard isActive={summaryFilter === 'Pending'} onClick={() => handleSummaryCardClick('Pending')}>
                                            <AdminComponents.SummaryAvatar variant="pending"><Schedule /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.Pending}</Typography><Typography variant="body2">Pending</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryCard isActive={summaryFilter === 'Approved'} onClick={() => handleSummaryCardClick('Approved')}>
                                            <AdminComponents.SummaryAvatar variant="approved"><PriceCheck /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.Approved}</Typography><Typography variant="body2">Approved</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryCard isActive={summaryFilter === 'Paid'} onClick={() => handleSummaryCardClick('Paid')}>
                                            <AdminComponents.SummaryAvatar variant="paid"><AttachMoney /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.Paid}</Typography><Typography variant="body2">Paid</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryCard isActive={summaryFilter === 'Unpaid'} onClick={() => handleSummaryCardClick('Unpaid')}>
                                            <AdminComponents.SummaryAvatar variant="unpaid"><Cancel /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.Unpaid}</Typography><Typography variant="body2">Unpaid</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                        <AdminComponents.SummaryCard isActive={summaryFilter === 'Partially Paid'} onClick={() => handleSummaryCardClick('Partially Paid')}>
                                            <AdminComponents.SummaryAvatar variant="partially-paid"><AttachMoney /></AdminComponents.SummaryAvatar>
                                            <Box><Typography variant="h6">{summaryStats.PartiallyPaid}</Typography><Typography variant="body2">Partially Paid</Typography></Box>
                                        </AdminComponents.SummaryCard>
                                    </AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.TopSectionActions>
                                        <Button variant="contained" startIcon={<Add />} onClick={() => handleOpenForm(null, 'edit')}>Add Travel Request</Button>
                                        <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>Graphs</Button>
                                    </AdminComponents.TopSectionActions>
                                </AdminComponents.TopSectionContent>
                            </AdminComponents.TopSectionWrapper>
                            <AdminComponents.ControlsSection>
                                <AdminComponents.ControlsGroup>
                                    <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                    <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedTravels.length > 0 && selectedIds.length === processedTravels.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedTravels.length} />} label="Select All" />
                                    {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                                </AdminComponents.ControlsGroup>
                                <AdminComponents.ControlsGroup>
                                    <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Search</Button>
                                    <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Settings</Button>
                                    <AdminComponents.StyledToggleButtonGroup size="small" value={viewMode} exclusive onChange={(e, v) => v && setViewMode(v)}>
                                        <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                        <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                        <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                        <ToggleButton value="table" title="Table View"><GridView />Table</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ControlsGroup>
                            </AdminComponents.ControlsSection>
                            <AdminComponents.ContentBody>
                                <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                                <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                    <TravelGraph travel={displayTravel} chartType={chartType} onChartTypeChange={setChartType} />
                                </AdminComponents.DetailsPane>
                            </AdminComponents.ContentBody>
                        </AdminComponents.MainContentArea>
                    </AdminComponents.AppBody>
                    <Drawer variant="persistent" anchor="right" open={isSidebarOpen}>
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader><Typography variant="h6">{sidebarMode === 'search' ? 'Advanced Search' : 'Grid Settings'}</Typography><IconButton onClick={() => setIsSidebarOpen(false)}><Close /></IconButton></AdminComponents.SidebarHeader>
                            <AdminComponents.SidebarContent>{sidebarContent}</AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>{sidebarFooter}</AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </Box>
                {isFormOpen && <TravelDetailsForm initialData={editingTravel} customers={customers} onSubmit={handleFormSubmit} onCancel={() => setIsFormOpen(false)} isViewOnly={formMode === 'view'} />}
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Travel;
