import React, { useState, useMemo, useEffect, useRef } from 'react';
import Chart from 'chart.js/auto';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, ToggleButton, Avatar, List, ListItem, ListItemText, FormControlLabel, Menu, MenuItem,
    Select, InputLabel, FormControl, Chip, Card, ListItemIcon, ToggleButtonGroup,
    TableContainer as MuiTableContainer, CardContent, Divider, Dialog, DialogTitle, DialogContent, DialogActions,
    Grid
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, SearchOff,
    <PERSON><PERSON>hart, <PERSON><PERSON>hart, DonutLarge, Settings, Security, AttachMoney, Business
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import { modulesData } from '../../../data/modules';

// --- MOCK DATA ---
const initialLicenses = [
    {
        id: 1,
        moduleName: 'Sales',
        moduleId: 1,
        status: 'Active',
        licenseType: 'SaaS',
        planType: 'Gold',
        category: 'Sales & Service',
        totalUsers: 150,
        pricing: {
            read: { range: '0-100', price: 100 },
            readWrite: { range: '0-50', price: 200 },
            admin: { range: '0-10', price: 500 }
        },
        createdDate: '2024-01-15',
        expiryDate: '2024-12-31'
    },
    {
        id: 2,
        moduleName: 'CPQ',
        moduleId: 2,
        status: 'Active',
        licenseType: 'Perpetual',
        planType: 'Platinum',
        category: 'Sales & Service',
        totalUsers: 50,
        pricing: { oneTimeCost: 50000, amcCost: 10000 },
        createdDate: '2024-02-20',
        expiryDate: 'Perpetual'
    },
    {
        id: 3,
        moduleName: 'Inventory',
        moduleId: 11,
        status: 'Inactive',
        licenseType: 'Term',
        planType: 'Silver',
        category: 'Operations',
        totalUsers: 25,
        pricing: { oneTimeCost: 15000, termMonths: 24 },
        createdDate: '2024-03-10',
        expiryDate: '2026-03-10'
    },
];

const ALL_COLUMNS = [
    { key: 'moduleName', label: 'Module Name', type: 'string', groupable: true },
    { key: 'status', label: 'Status', type: 'string', groupable: true },
    { key: 'licenseType', label: 'License Type', type: 'string', groupable: true },
    { key: 'planType', label: 'Plan Type', type: 'string', groupable: true },
    { key: 'category', label: 'Category', type: 'string', groupable: true },
    { key: 'totalUsers', label: 'Total Users', type: 'number', groupable: false },
    { key: 'expiryDate', label: 'Expiry Date', type: 'string', groupable: false },
];

const LICENSE_TYPE_LABELS = {
    'SaaS': 'Software as a Service',
    'Perpetual': 'Perpetual License',
    'Term': 'Term License'
};

const PLAN_TYPES = ['Free', 'Silver', 'Gold', 'Platinum'];
const LICENSE_TYPES = ['SaaS', 'Perpetual', 'Term'];
const ACCESS_LEVELS = ['Read', 'Read Write', 'Admin'];
const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

// --- UI COMPONENTS ---

const ActionButtons = ({ license, onView, onEdit, onDelete, isCondensed }) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = (event) => {
        event.stopPropagation();
        setAnchorEl(null);
    };

    const handleAction = (action, event) => {
        handleClose(event);
        action();
    }

    if (isCondensed) {
        return (
            <Box onClick={e => e.stopPropagation()}>
                <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={open ? 'long-menu' : undefined}
                    aria-expanded={open ? 'true' : undefined}
                    aria-haspopup="true"
                    onClick={handleClick}
                    size="small"
                >
                    <MoreVert />
                </IconButton>
                <Menu
                    id="long-menu"
                    MenuListProps={{
                        'aria-labelledby': 'long-button',
                    }}
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                >
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onView(license, false), e)}>
                        <Visibility fontSize="small" /> View
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ActionMenuItem onClick={(e) => handleAction(() => onEdit(license, true), e)}>
                        <Edit fontSize="small" /> Edit
                    </AdminComponents.ActionMenuItem>
                    <AdminComponents.ErrorMenuItem onClick={(e) => handleAction(() => onDelete([license.id]), e)}>
                        <Delete fontSize="small" /> Delete
                    </AdminComponents.ErrorMenuItem>
                </Menu>
            </Box>
        );
    }

    return (
        <Box onClick={e => e.stopPropagation()}>
            <IconButton size="small" onClick={() => onView(license, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
            <IconButton size="small" onClick={() => onEdit(license, true)} title="Edit"><Edit fontSize="small" /></IconButton>
            <IconButton size="small" color="error" onClick={() => onDelete([license.id])} title="Delete"><Delete fontSize="small" /></IconButton>
        </Box>
    );
};

const LicenseCard = ({ license, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(license.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ license, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{license.moduleName}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{license.category}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: license.status }} label={license.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Type:</strong> {license.licenseType}</Typography>
            <Typography variant="body2"><strong>Plan:</strong> {license.planType}</Typography>
            <AdminComponents.ContactTypography variant="body2" noWrap><strong>Users:</strong> {license.totalUsers}</AdminComponents.ContactTypography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const LicenseCompactCard = ({ license, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(license.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons {...{ license, onView, onEdit, onDelete, isCondensed }} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{license.moduleName}</Typography>
                <Typography variant="caption" color="text.secondary">{license.category}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{license.planType}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: license.status }} label={license.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const LicenseListItem = ({ license, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView, isCondensed }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(license.id)} />
            <Box>
                <Typography fontWeight="bold">{license.moduleName}</Typography>
                <Typography variant="body2" color="text.secondary">{license.category}</Typography>
            </Box>
            <Typography variant="body2">{license.licenseType}</Typography>
            <Typography variant="body2">{license.planType}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: license.status }} label={license.status} size="small" />
            <AdminComponents.ListItemActions>
                 <ActionButtons {...{ license, onView, onEdit, onDelete, isCondensed }} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const LicenseTable = ({ licenses, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, addLog, groupByKeys, onDelete, onEdit, onView, isCondensed }) => {
    const dragItemIndex = useRef(null);
    const dragOverItemIndex = useRef(null);

    const renderCellContent = (license, colKey) => {
        if (colKey === 'status') return <AdminComponents.StatusBadge ownerState={{ status: license.status }} label={license.status} size="small" />;

        const value = colKey.includes('.') ? colKey.split('.').reduce((o, i) => o?.[i], license) : license[colKey];

        if (colKey === 'moduleName') {
            return (
                <AdminComponents.ClickableTypography component="span" onClick={() => onRowClick(license)}>
                    {value}
                </AdminComponents.ClickableTypography>
            );
        }
        return value || '-';
    };

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        addLog({ user: 'Admin', action: 'reordered columns' });
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    const renderGroupedRows = (data, keys, level = 0) => {
        if (!keys.length || !data.length) {
            return data.map(license => (
                <TableRow key={license.id} hover selected={selectedId === license.id} onClick={() => onRowClick(license)}>
                    <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(license.id)} onChange={() => onSelectOne(license.id)} onClick={e => e.stopPropagation()} /></TableCell>
                    {columnOrder.map(colKey => (
                        <AdminComponents.ContentTableCell key={colKey}>
                            {renderCellContent(license, colKey)}
                        </AdminComponents.ContentTableCell>
                    ))}
                    <AdminComponents.ActionTableCell align="center">
                        <ActionButtons {...{ license, onView, onEdit, onDelete, isCondensed }} />
                    </AdminComponents.ActionTableCell>
                </TableRow>
            ));
        }

        const currentKey = keys[0];
        const remainingKeys = keys.slice(1);
        const groupLabel = ALL_COLUMNS.find(c => c.key === currentKey)?.label;

        const grouped = data.reduce((acc, item) => {
            const groupValue = String(item[currentKey] || 'N/A');
            if (!acc[groupValue]) acc[groupValue] = [];
            acc[groupValue].push(item);
            return acc;
        }, {});

        return Object.entries(grouped).map(([groupValue, items]) => (
            <React.Fragment key={`${level}-${groupValue}`}>
                <AdminComponents.GroupHeaderRow>
                    <AdminComponents.GroupHeaderCell
                        colSpan={columnOrder.length + 2}
                        style={{ paddingLeft: theme.spacing(2 + level * 2) }}
                    >
                        <strong>{groupLabel}:</strong> {groupValue} ({items.length})
                    </AdminComponents.GroupHeaderCell>
                </AdminComponents.GroupHeaderRow>
                {renderGroupedRows(items, remainingKeys, level + 1)}
            </React.Fragment>
        ));
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <AdminComponents.ResponsiveTable stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < licenses.length} checked={licenses.length > 0 && selectedIds.length === licenses.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, index) => (
                            <AdminComponents.DraggableHeaderCell
                                key={colKey}
                                draggable
                                onDragStart={() => (dragItemIndex.current = index)}
                                onDragEnter={() => (dragOverItemIndex.current = index)}
                                onDrop={handleDrop}
                                onDragOver={(e) => e.preventDefault()}
                                sortDirection={sortColumn === colKey ? sortDirection : false}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </AdminComponents.DraggableHeaderCell>
                        ))}
                        <AdminComponents.ActionTableCell align="center">Actions</AdminComponents.ActionTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renderGroupedRows(licenses, groupByKeys)}
                </TableBody>
            </AdminComponents.ResponsiveTable>
        </AdminComponents.TableViewContainer>
    );
};

const LicenseGraph = ({ license, chartType }) => {
    const chartRef = useRef(null);
    const chartInstance = useRef(null);

    useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && license) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['SaaS', 'Perpetual', 'Term'],
                    datasets: [{
                        label: 'License Type Distribution (Mock)',
                        data: [Math.random() * 100, Math.random() * 100, Math.random() * 100],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${license.moduleName} - License Analysis` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [license, chartType]);

    return (
        <>
            {license ? (
                <AdminComponents.GraphCanvasContainer>
                    <canvas ref={chartRef}></canvas>
                </AdminComponents.GraphCanvasContainer>
            ) : (
                <AdminComponents.CenteredMessage>
                    <Typography>Select a license to see graph</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <List disablePadding>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <ListItemText
                            primary={
                                <AdminComponents.ActivityLogTextContainer>
                                    <Typography variant="body2" component="span" color="text.secondary">
                                        <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                        {' '}{log.action}{' '}
                                        {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {log.timestamp}
                                    </Typography>
                                </AdminComponents.ActivityLogTextContainer>
                            }
                        />
                    </AdminComponents.ActivityLogListItem>
                ))}
            </List>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

// Module Selection Dialog Component
const ModuleSelectionDialog = ({ open, onClose, onSelect }) => {
    const [selectedModule, setSelectedModule] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');

    const filteredModules = modulesData.filter(module =>
        module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.category.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleSelect = () => {
        if (selectedModule) {
            onSelect(selectedModule);
            setSelectedModule(null);
            setSearchTerm('');
            onClose();
        }
    };

    return (
        <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle>
                Select Module for License Configuration
                <IconButton
                    onClick={onClose}
                    sx={{ position: 'absolute', right: 8, top: 8 }}
                >
                    <Close />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <TextField
                    autoFocus
                    margin="dense"
                    label="Search Modules"
                    fullWidth
                    variant="outlined"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{ mb: 3 }}
                    InputProps={{ startAdornment: <Search color="disabled" /> }}
                />

                <Grid container spacing={2} sx={{ maxHeight: 400, overflowY: 'auto' }}>
                    {filteredModules.map((module) => (
                        <Grid item xs={12} sm={6} md={4} key={module.id}>
                            <Card
                                sx={{
                                    cursor: 'pointer',
                                    border: selectedModule?.id === module.id ? 2 : 1,
                                    borderColor: selectedModule?.id === module.id ? 'primary.main' : 'divider',
                                    '&:hover': {
                                        borderColor: 'primary.main',
                                        transform: 'translateY(-2px)'
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                                onClick={() => setSelectedModule(module)}
                            >
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <Business color="primary" sx={{ mr: 1 }} />
                                        <Typography variant="h6" component="h3" noWrap>
                                            {module.name}
                                        </Typography>
                                    </Box>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        {module.category}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                        {module.description}
                                    </Typography>
                                    {module.tags && module.tags.length > 0 && (
                                        <Box sx={{ mt: 1 }}>
                                            {module.tags.slice(0, 2).map((tag, index) => (
                                                <Chip
                                                    key={index}
                                                    label={tag}
                                                    size="small"
                                                    sx={{ mr: 0.5, mb: 0.5 }}
                                                />
                                            ))}
                                        </Box>
                                    )}
                                </CardContent>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button
                    onClick={handleSelect}
                    variant="contained"
                    disabled={!selectedModule}
                >
                    Configure License Plans
                </Button>
            </DialogActions>
        </Dialog>
    );
};

// --- MAIN APP COMPONENT ---
const License = () => {
    const [licenses, setLicenses] = useState(initialLicenses);
    const [selectedLicense, setSelectedLicense] = useState(null);
    const [modalState, setModalState] = useState({ isOpen: false, license: null, isAdding: false });
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new license for', target: 'Sales Module', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'License Manager', action: 'Updated license configuration for', target: 'CPQ Module', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Admin', action: 'Deleted license for', target: 'Old Inventory Module', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('moduleName');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [groupByKeys, setGroupByKeys] = useState([]);
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [summaryFilter, setSummaryFilter] = useState(null);
    const [chartType, setChartType] = useState('bar');
    const [isModuleSelectionOpen, setIsModuleSelectionOpen] = useState(false);

    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    const quickFilterOptions = useMemo(() => {
        const categories = [...new Set(licenses.map(l => l.category))];
        const statuses = [...new Set(licenses.map(l => l.status))];
        return [...statuses, ...categories];
    }, [licenses]);

    const processedLicenses = useMemo(() => {
        let current = licenses.filter(l => !l.isDraft);

        // Filter by summary card selection
        if (summaryFilter) {
            current = current.filter(l => l.status === summaryFilter);
        }

        // Simple search term filter
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(l =>
                Object.values(l).some(val =>
                    String(val).toLowerCase().includes(term)
                )
            );
        }

        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(license => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const licenseValue = String(field.includes('.') ? field.split('.').reduce((o, i) => o?.[i], license) : license[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();

                    switch (operator) {
                        case 'Equals': return licenseValue === filterValue;
                        case 'Not Equals': return licenseValue !== filterValue;
                        case 'Contains': return licenseValue.includes(filterValue);
                        case 'Starts With': return licenseValue.startsWith(filterValue);
                        case 'Ends With': return licenseValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }

        // Sorting
        return [...current].sort((a, b) => {
            const valA = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], a) : a[sortColumn];
            const valB = sortColumn.includes('.') ? sortColumn.split('.').reduce((o, i) => o?.[i], b) : b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? (valA || 0) - (valB || 0) : (valB || 0) - (valA || 0);
        });
    }, [licenses, searchTerm, activeFilters, sortColumn, sortDirection, summaryFilter]);

    const displayLicense = useMemo(() => {
        const isSelectedVisible = processedLicenses.some(l => l.id === selectedLicense?.id);
        if (isSelectedVisible) return selectedLicense;
        return processedLicenses.length > 0 ? processedLicenses[0] : null;
    }, [processedLicenses, selectedLicense]);

    const addLog = (logEntry) => {
        const timestamp = new Date().toLocaleString();
        setActivityLog(prev => [{ ...logEntry, timestamp }, ...prev].slice(0, 10));
    };

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const confirmDelete = () => {
        addLog({ user: 'Admin', action: `Deleted ${deleteConfirmation.idsToDelete.length} license(s).` });
        setDeleteConfirmation({ isOpen: false, idsToDelete: [] });
    };
    const handleShowDetails = (license) => setModalState({ isOpen: true, license, isAdding: false });
    const handleShowAddModal = () => setIsModuleSelectionOpen(true);

    const handleModuleSelect = (module) => {
        // Create a new license entry for the selected module
        const newLicense = {
            id: Date.now(),
            moduleName: module.name,
            moduleId: module.id,
            status: 'Draft',
            licenseType: 'SaaS',
            planType: 'Free',
            category: module.category,
            totalUsers: 0,
            pricing: {},
            createdDate: new Date().toISOString().split('T')[0],
            expiryDate: 'Not Set'
        };

        setLicenses(prev => [newLicense, ...prev]);
        addLog({ user: 'Admin', action: 'created a new license for', target: module.name });
        setModalState({ isOpen: true, license: newLicense, isAdding: true });
    };

    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedLicenses.map(l => l.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) {
            setIsGraphVisible(false);
        }
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;

        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                console.warn("Cannot hide the last column.");
                return;
            }
        } else {
            // Add the column back in its original position
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        const colLabel = ALL_COLUMNS.find(c => c.key === columnKey)?.label || columnKey;
        addLog({ user: 'Admin', action: `toggled '${colLabel}' column visibility` });
    };

    const handleGraphToggle = () => {
        setIsGraphVisible(prev => !prev);
        setIsSidebarOpen(false);
    };

    const summaryStats = useMemo(() => ({
        total: licenses.length,
        active: licenses.filter(l => l.status === 'Active').length,
        inactive: licenses.filter(l => l.status === 'Inactive').length,
    }), [licenses]);

    // --- Advanced Search Handlers ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };

    const handleAddQuickFilter = (value) => {
        const field = ['Active', 'Inactive'].includes(value) ? 'status' : 'category';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };

    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
        setSummaryFilter(null); // Clear summary filter when applying advanced filters
    };

    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
        setSummaryFilter(null); // Clear summary filter on reset
    };

    const handleSummaryCardClick = (status) => {
        setSummaryFilter(prevStatus => (prevStatus === status ? null : status));
        // Clear other filters for a clean summary view
        setActiveFilters([]);
        setStagedFilters([]);
        setSearchTerm('');
    };

    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const renderCurrentView = () => {
        const isCondensed = isSidebarOpen || isGraphVisible;
        return (
            <AdminComponents.ViewContainer>
                {processedLicenses.length > 0 ? (
                    <>
                        {viewMode === 'cards' && <AdminComponents.GridView>{processedLicenses.map(license => <LicenseCard key={license.id} license={license} onClick={() => setSelectedLicense(license)} isSelected={displayLicense?.id === license.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(license.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={false} />)}</AdminComponents.GridView>}
                        {viewMode === 'grid' && (
                            <LicenseTable
                                licenses={processedLicenses}
                                onRowClick={setSelectedLicense}
                                onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                                sortColumn={sortColumn}
                                sortDirection={sortDirection}
                                selectedId={displayLicense?.id}
                                selectedIds={selectedIds}
                                onSelectAll={handleSelectAll}
                                onSelectOne={handleSelectOne}
                                columnOrder={columnOrder}
                                setColumnOrder={setColumnOrder}
                                addLog={addLog}
                                groupByKeys={groupByKeys}
                                onDelete={handleDeleteRequest}
                                onEdit={handleShowDetails}
                                onView={handleShowDetails}
                                isCondensed={isCondensed}
                            />
                        )}
                        {viewMode === 'compact' && <AdminComponents.CompactView>{processedLicenses.map(license => <LicenseCompactCard key={license.id} license={license} onClick={() => setSelectedLicense(license)} isSelected={displayLicense?.id === license.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(license.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={false} />)}</AdminComponents.CompactView>}
                        {viewMode === 'list' && <AdminComponents.ListView>{processedLicenses.map(license => <LicenseListItem key={license.id} license={license} onClick={() => setSelectedLicense(license)} isSelected={displayLicense?.id === license.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(license.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} isCondensed={isCondensed} />)}</AdminComponents.ListView>}
                    </>
                ) : (
                    <AdminComponents.CenteredMessage component={Paper}>
                        <AdminComponents.LargeIcon color="disabled" />
                        <Typography variant="h6">No Matching Licenses</Typography>
                        <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                    </AdminComponents.CenteredMessage>
                )}
            </AdminComponents.ViewContainer>
        );
    };

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === null} onClick={() => handleSummaryCardClick(null)}>
                                        <AdminComponents.SummaryAvatar variant="total"><Security /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Licenses</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Active'} onClick={() => handleSummaryCardClick('Active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={summaryFilter === 'Inactive'} onClick={() => handleSummaryCardClick('Inactive')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.inactive}</Typography><Typography variant="body2">Inactive</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />} onClick={handleShowAddModal}>Add License</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={handleGraphToggle}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>

                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedLicenses.length > 0 && selectedIds.length === processedLicenses.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedLicenses.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>

                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                     fullWidth>
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                    <LicenseGraph license={displayLicense} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>

                        <ActivityLog logs={activityLog} />

                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>

                {/* Module Selection Dialog */}
                <ModuleSelectionDialog
                    open={isModuleSelectionOpen}
                    onClose={() => setIsModuleSelectionOpen(false)}
                    onSelect={handleModuleSelect}
                />

                <Drawer
                    variant="persistent"
                    anchor="right"
                    open={isSidebarOpen}
                >
                    <AdminComponents.SidebarContainer>
                        <AdminComponents.SidebarHeader>
                            <Typography variant="h6">
                                {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                            </Typography>
                            <IconButton onClick={() => setIsSidebarOpen(false)}>
                                <Close />
                            </IconButton>
                        </AdminComponents.SidebarHeader>

                        <AdminComponents.SidebarContent>
                            {sidebarMode === 'search' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.QuickFilterContainer>
                                            {quickFilterOptions.map(opt => (
                                                <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                            ))}
                                        </AdminComponents.QuickFilterContainer>
                                    </AdminComponents.SidebarSection>

                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Field</InputLabel>
                                            <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <FormControl fullWidth size="small">
                                            <InputLabel>Operator</InputLabel>
                                            <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                            </Select>
                                        </FormControl>
                                        <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                        <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                    </AdminComponents.SidebarSection>

                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>

                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {activeFilters.length > 0 ? activeFilters.map(f => (
                                                <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                            )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}

                            {sidebarMode === 'grid' && (
                                <>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.ColumnActionContainer>
                                            <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                            <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                        </AdminComponents.ColumnActionContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                        <AdminComponents.FilterChipContainer>
                                            {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                <Chip
                                                    key={key}
                                                    label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                    onDelete={() => handleGroupByChange(key)}
                                                />
                                            )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                        </AdminComponents.FilterChipContainer>
                                        <AdminComponents.ColumnVisibilityContainer>
                                            {ALL_COLUMNS.filter(c => c.groupable).map(col => (
                                                <FormControlLabel
                                                    key={col.key}
                                                    control={
                                                        <Checkbox
                                                            checked={groupByKeys.includes(col.key)}
                                                            onChange={() => handleGroupByChange(col.key)}
                                                        />
                                                    }
                                                    label={col.label}
                                                />
                                            ))}
                                        </AdminComponents.ColumnVisibilityContainer>
                                    </AdminComponents.SidebarSection>
                                </>
                            )}
                        </AdminComponents.SidebarContent>

                        <AdminComponents.SidebarFooter>
                            {sidebarMode === 'search' && (
                                <>
                                    <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                    <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                </>
                            )}
                            {sidebarMode === 'grid' && (
                                <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                            )}
                        </AdminComponents.SidebarFooter>
                    </AdminComponents.SidebarContainer>
                </Drawer>

            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default License;
