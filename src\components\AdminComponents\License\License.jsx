import React, { useState } from 'react';
import {
    Box, Typography, Button, TextField, IconButton,
    Card, CardContent, Grid, Fab, Tabs, Tab, RadioGroup, Radio, Switch, Stepper, Step, StepLabel,
    Dialog, DialogTitle, DialogContent, DialogActions, Alert, Collapse,
    FormControl, InputLabel, Select, MenuItem, FormControlLabel, Chip, LinearProgress, Paper
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, CheckCircle, Cancel, Close, Save, Business, Settings, RemoveCircle
} from '@mui/icons-material';
import { theme } from '../../../styles/theme';

// Mock data for demonstration
const mockProjects = [
    {
        id: 1,
        name: 'Enterprise CRM',
        description: 'Customer relationship management system',
        plans: ['Free', 'Silver', 'Gold', 'Platinum'],
        createdDate: '2024-01-15',
        status: 'Active'
    },
    {
        id: 2,
        name: 'Analytics Platform',
        description: 'Business intelligence and analytics',
        plans: ['Basic', 'Professional', 'Enterprise'],
        createdDate: '2024-02-20',
        status: 'Active'
    },
    {
        id: 3,
        name: 'Mobile App Suite',
        description: 'Cross-platform mobile applications',
        plans: ['Starter', 'Growth', 'Scale'],
        createdDate: '2024-03-10',
        status: 'Draft'
    }
];

const licenseModels = [
    { value: 'perpetual', label: 'Perpetual License', description: 'One-time purchase with permanent usage rights' },
    { value: 'term', label: 'Term License', description: 'Fixed-term license with expiration date' },
    { value: 'saas', label: 'Software as a Service', description: 'Subscription-based cloud service' }
];

const billingCycles = [
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'annually', label: 'Annually' },
    { value: 'biannually', label: 'Bi-annually' }
];

const userLimitTypes = [
    { value: 'unlimited', label: 'Unlimited' },
    { value: 'fixed', label: 'Fixed Limit' },
    { value: 'variable', label: 'Variable Pricing' }
];

const saasBillingBasis = [
    { value: 'user_count', label: 'User Count Based' },
    { value: 'consumption', label: 'Consumption Based' },
    { value: 'outcome', label: 'Outcome Based' }
];

// Initial form state
const initialPlanConfig = {
    licenseModel: '',
    oneTimeCost: '',
    enableAMC: false,
    amcCost: '',
    billingBasis: '',
    costPerTransaction: '',
    userRoles: [
        { roleName: '', pricePerUser: '', billingCycle: 'monthly', userLimitType: 'unlimited', userLimit: '' }
    ],
    outcomeTiers: [
        { rangeFrom: '', rangeTo: '', percentageFee: '' }
    ]
};

const License = () => {
    const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard' or 'configure'
    const [selectedProject, setSelectedProject] = useState(null);
    const [projects, setProjects] = useState(mockProjects);
    const [isAddProjectOpen, setIsAddProjectOpen] = useState(false);
    const [newProjectName, setNewProjectName] = useState('');
    const [newProjectDescription, setNewProjectDescription] = useState('');
    const [activeTab, setActiveTab] = useState(0);
    const [planConfigs, setPlanConfigs] = useState({
        'Free': { ...initialPlanConfig },
        'Silver': { ...initialPlanConfig },
        'Gold': { ...initialPlanConfig },
        'Platinum': { ...initialPlanConfig }
    });
    const [currentStep, setCurrentStep] = useState(0);
    const [errors, setErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);

    const planNames = selectedProject ? selectedProject.plans : ['Free', 'Silver', 'Gold', 'Platinum'];
    const currentPlan = planNames[activeTab];
    const currentConfig = planConfigs[currentPlan] || { ...initialPlanConfig };

    // Validation functions
    const validateStep = (step, config) => {
        const newErrors = {};

        if (step === 0) {
            if (!config.licenseModel) {
                newErrors.licenseModel = 'Please select a license model';
            }
        }

        if (step === 1) {
            if (config.licenseModel === 'perpetual' || config.licenseModel === 'term') {
                if (!config.oneTimeCost || isNaN(config.oneTimeCost) || parseFloat(config.oneTimeCost) <= 0) {
                    newErrors.oneTimeCost = 'Please enter a valid one-time cost';
                }
                if (config.enableAMC && (!config.amcCost || isNaN(config.amcCost) || parseFloat(config.amcCost) <= 0)) {
                    newErrors.amcCost = 'Please enter a valid AMC cost';
                }
            }
            if (config.licenseModel === 'saas' && !config.billingBasis) {
                newErrors.billingBasis = 'Please select a billing basis';
            }
        }

        if (step === 2 && config.licenseModel === 'saas') {
            if (config.billingBasis === 'consumption') {
                if (!config.costPerTransaction || isNaN(config.costPerTransaction) || parseFloat(config.costPerTransaction) <= 0) {
                    newErrors.costPerTransaction = 'Please enter a valid cost per transaction';
                }
            }
            if (config.billingBasis === 'user_count') {
                config.userRoles.forEach((role, index) => {
                    if (!role.roleName.trim()) {
                        newErrors[`userRole_${index}_name`] = 'Role name is required';
                    }
                    if (!role.pricePerUser || isNaN(role.pricePerUser) || parseFloat(role.pricePerUser) <= 0) {
                        newErrors[`userRole_${index}_price`] = 'Valid price is required';
                    }
                    if (role.userLimitType === 'fixed' && (!role.userLimit || isNaN(role.userLimit) || parseInt(role.userLimit) <= 0)) {
                        newErrors[`userRole_${index}_limit`] = 'Valid user limit is required';
                    }
                });
            }
            if (config.billingBasis === 'outcome') {
                config.outcomeTiers.forEach((tier, index) => {
                    if (!tier.rangeFrom || isNaN(tier.rangeFrom) || parseFloat(tier.rangeFrom) < 0) {
                        newErrors[`outcomeTier_${index}_from`] = 'Valid range start is required';
                    }
                    if (!tier.rangeTo || isNaN(tier.rangeTo) || parseFloat(tier.rangeTo) <= parseFloat(tier.rangeFrom || 0)) {
                        newErrors[`outcomeTier_${index}_to`] = 'Range end must be greater than start';
                    }
                    if (!tier.percentageFee || isNaN(tier.percentageFee) || parseFloat(tier.percentageFee) <= 0 || parseFloat(tier.percentageFee) > 100) {
                        newErrors[`outcomeTier_${index}_fee`] = 'Valid percentage (1-100) is required';
                    }
                });
            }
        }

        return newErrors;
    };

    // Update plan configuration
    const updatePlanConfig = (planName, updates) => {
        setPlanConfigs(prev => ({
            ...prev,
            [planName]: {
                ...prev[planName],
                ...updates
            }
        }));
    };

    // Add new user role
    const addUserRole = () => {
        const newRole = { roleName: '', pricePerUser: '', billingCycle: 'monthly', userLimitType: 'unlimited', userLimit: '' };
        updatePlanConfig(currentPlan, {
            userRoles: [...currentConfig.userRoles, newRole]
        });
    };

    // Remove user role
    const removeUserRole = (index) => {
        const updatedRoles = currentConfig.userRoles.filter((_, i) => i !== index);
        updatePlanConfig(currentPlan, { userRoles: updatedRoles });
    };

    // Update user role
    const updateUserRole = (index, field, value) => {
        const updatedRoles = currentConfig.userRoles.map((role, i) =>
            i === index ? { ...role, [field]: value } : role
        );
        updatePlanConfig(currentPlan, { userRoles: updatedRoles });
    };

    // Add outcome tier
    const addOutcomeTier = () => {
        const newTier = { rangeFrom: '', rangeTo: '', percentageFee: '' };
        updatePlanConfig(currentPlan, {
            outcomeTiers: [...currentConfig.outcomeTiers, newTier]
        });
    };

    // Remove outcome tier
    const removeOutcomeTier = (index) => {
        const updatedTiers = currentConfig.outcomeTiers.filter((_, i) => i !== index);
        updatePlanConfig(currentPlan, { outcomeTiers: updatedTiers });
    };

    // Update outcome tier
    const updateOutcomeTier = (index, field, value) => {
        const updatedTiers = currentConfig.outcomeTiers.map((tier, i) =>
            i === index ? { ...tier, [field]: value } : tier
        );
        updatePlanConfig(currentPlan, { outcomeTiers: updatedTiers });
    };

    // Handle step navigation
    const handleNext = () => {
        const stepErrors = validateStep(currentStep, currentConfig);
        setErrors(stepErrors);

        if (Object.keys(stepErrors).length === 0) {
            setCurrentStep(prev => Math.min(prev + 1, getMaxSteps() - 1));
        }
    };

    const handleBack = () => {
        setCurrentStep(prev => Math.max(prev - 1, 0));
        setErrors({});
    };

    const getMaxSteps = () => {
        if (currentConfig.licenseModel === 'saas') {
            return 3;
        }
        return 2;
    };

    // Handle project creation
    const handleAddProject = () => {
        if (!newProjectName.trim()) return;

        const newProject = {
            id: Date.now(),
            name: newProjectName,
            description: newProjectDescription,
            plans: ['Free', 'Silver', 'Gold', 'Platinum'],
            createdDate: new Date().toISOString().split('T')[0],
            status: 'Draft'
        };

        setProjects(prev => [...prev, newProject]);
        setNewProjectName('');
        setNewProjectDescription('');
        setIsAddProjectOpen(false);
    };

    // Handle project configuration
    const handleConfigureProject = (project) => {
        setSelectedProject(project);
        setCurrentView('configure');
        setActiveTab(0);
        setCurrentStep(0);
        setErrors({});
    };

    // Handle save configuration
    const handleSaveConfiguration = async () => {
        setIsLoading(true);

        // Validate all plans
        let hasErrors = false;
        const allErrors = {};

        planNames.forEach(planName => {
            const config = planConfigs[planName];
            const maxSteps = config.licenseModel === 'saas' ? 3 : 2;

            for (let step = 0; step < maxSteps; step++) {
                const stepErrors = validateStep(step, config);
                if (Object.keys(stepErrors).length > 0) {
                    allErrors[planName] = stepErrors;
                    hasErrors = true;
                }
            }
        });

        if (hasErrors) {
            setErrors(allErrors);
            setIsLoading(false);
            return;
        }

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Update project status
        setProjects(prev => prev.map(p =>
            p.id === selectedProject.id
                ? { ...p, status: 'Active' }
                : p
        ));

        setIsLoading(false);
        setCurrentView('dashboard');
        setSelectedProject(null);
    };

    // Render project dashboard
    const renderProjectDashboard = () => (
        <Box sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
                    License Management
                </Typography>
                <Fab
                    color="primary"
                    variant="extended"
                    onClick={() => setIsAddProjectOpen(true)}
                    sx={{ gap: 1 }}
                >
                    <Add />
                    Add New Project
                </Fab>
            </Box>

            <Grid container spacing={3}>
                {projects.map((project) => (
                    <Grid item xs={12} sm={6} md={4} key={project.id}>
                        <Card
                            sx={{
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                    transform: 'translateY(-4px)',
                                    boxShadow: theme.shadows[4]
                                }
                            }}
                            onClick={() => handleConfigureProject(project)}
                        >
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                    <Business color="primary" sx={{ fontSize: 40 }} />
                                    <Chip
                                        label={project.status}
                                        color={project.status === 'Active' ? 'success' : 'default'}
                                        size="small"
                                    />
                                </Box>

                                <Typography variant="h6" component="h2" sx={{ mb: 1, fontWeight: 'bold' }}>
                                    {project.name}
                                </Typography>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                    {project.description}
                                </Typography>

                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    <strong>Plans:</strong> {project.plans.length} ({project.plans.join(', ')})
                                </Typography>

                                <Typography variant="caption" color="text.secondary">
                                    Created: {new Date(project.createdDate).toLocaleDateString()}
                                </Typography>
                            </CardContent>

                            <Box sx={{ p: 2, pt: 0 }}>
                                <Button
                                    fullWidth
                                    variant="outlined"
                                    startIcon={<Settings />}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleConfigureProject(project);
                                    }}
                                >
                                    Configure Plans
                                </Button>
                            </Box>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Add Project Dialog */}
            <Dialog
                open={isAddProjectOpen}
                onClose={() => setIsAddProjectOpen(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogTitle>
                    Add New Project
                    <IconButton
                        onClick={() => setIsAddProjectOpen(false)}
                        sx={{ position: 'absolute', right: 8, top: 8 }}
                    >
                        <Close />
                    </IconButton>
                </DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Project Name"
                        fullWidth
                        variant="outlined"
                        value={newProjectName}
                        onChange={(e) => setNewProjectName(e.target.value)}
                        sx={{ mb: 2 }}
                    />
                    <TextField
                        margin="dense"
                        label="Project Description"
                        fullWidth
                        multiline
                        rows={3}
                        variant="outlined"
                        value={newProjectDescription}
                        onChange={(e) => setNewProjectDescription(e.target.value)}
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setIsAddProjectOpen(false)}>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleAddProject}
                        variant="contained"
                        disabled={!newProjectName.trim()}
                    >
                        Create Project
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );

    // Render license model selection step
    const renderLicenseModelStep = () => (
        <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                Select License Model
            </Typography>

            <RadioGroup
                value={currentConfig.licenseModel}
                onChange={(e) => updatePlanConfig(currentPlan, { licenseModel: e.target.value })}
            >
                <Grid container spacing={2}>
                    {licenseModels.map((model) => (
                        <Grid item xs={12} md={4} key={model.value}>
                            <Card
                                sx={{
                                    p: 2,
                                    cursor: 'pointer',
                                    border: currentConfig.licenseModel === model.value ? 2 : 1,
                                    borderColor: currentConfig.licenseModel === model.value ? 'primary.main' : 'divider',
                                    '&:hover': {
                                        borderColor: 'primary.main'
                                    }
                                }}
                                onClick={() => updatePlanConfig(currentPlan, { licenseModel: model.value })}
                            >
                                <FormControlLabel
                                    value={model.value}
                                    control={<Radio />}
                                    label=""
                                    sx={{ m: 0, width: '100%' }}
                                />
                                <Box sx={{ mt: 1 }}>
                                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                                        {model.label}
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        {model.description}
                                    </Typography>
                                </Box>
                            </Card>
                        </Grid>
                    ))}
                </Grid>
            </RadioGroup>

            {errors.licenseModel && (
                <Alert severity="error" sx={{ mt: 2 }}>
                    {errors.licenseModel}
                </Alert>
            )}
        </Box>
    );

    // Render pricing configuration step
    const renderPricingConfigStep = () => (
        <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                Configure Pricing
            </Typography>

            {(currentConfig.licenseModel === 'perpetual' || currentConfig.licenseModel === 'term') && (
                <Box sx={{ mb: 4 }}>
                    <TextField
                        fullWidth
                        label="One-Time Cost ($)"
                        type="number"
                        value={currentConfig.oneTimeCost}
                        onChange={(e) => updatePlanConfig(currentPlan, { oneTimeCost: e.target.value })}
                        error={!!errors.oneTimeCost}
                        helperText={errors.oneTimeCost}
                        sx={{ mb: 3 }}
                    />

                    <FormControlLabel
                        control={
                            <Switch
                                checked={currentConfig.enableAMC}
                                onChange={(e) => updatePlanConfig(currentPlan, { enableAMC: e.target.checked })}
                            />
                        }
                        label="Enable Annual Maintenance Contract (AMC)"
                        sx={{ mb: 2 }}
                    />

                    <Collapse in={currentConfig.enableAMC}>
                        <TextField
                            fullWidth
                            label="Yearly AMC Cost ($)"
                            type="number"
                            value={currentConfig.amcCost}
                            onChange={(e) => updatePlanConfig(currentPlan, { amcCost: e.target.value })}
                            error={!!errors.amcCost}
                            helperText={errors.amcCost}
                        />
                    </Collapse>
                </Box>
            )}

            {currentConfig.licenseModel === 'saas' && (
                <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                        Select Billing Basis
                    </Typography>

                    <RadioGroup
                        value={currentConfig.billingBasis}
                        onChange={(e) => updatePlanConfig(currentPlan, { billingBasis: e.target.value })}
                    >
                        {saasBillingBasis.map((basis) => (
                            <FormControlLabel
                                key={basis.value}
                                value={basis.value}
                                control={<Radio />}
                                label={basis.label}
                            />
                        ))}
                    </RadioGroup>

                    {errors.billingBasis && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {errors.billingBasis}
                        </Alert>
                    )}
                </Box>
            )}
        </Box>
    );

    // Render SaaS billing configuration step
    const renderSaaSBillingStep = () => (
        <Box sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>
                Configure SaaS Billing Details
            </Typography>

            {currentConfig.billingBasis === 'consumption' && (
                <Box>
                    <TextField
                        fullWidth
                        label="Cost per Transaction ($)"
                        type="number"
                        value={currentConfig.costPerTransaction}
                        onChange={(e) => updatePlanConfig(currentPlan, { costPerTransaction: e.target.value })}
                        error={!!errors.costPerTransaction}
                        helperText={errors.costPerTransaction}
                        sx={{ mb: 2 }}
                    />
                </Box>
            )}

            {currentConfig.billingBasis === 'user_count' && (
                <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                            User Roles & Pricing
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={addUserRole}
                        >
                            Add Role
                        </Button>
                    </Box>

                    {currentConfig.userRoles.map((role, index) => (
                        <Card key={index} sx={{ p: 3, mb: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                    Role {index + 1}
                                </Typography>
                                {currentConfig.userRoles.length > 1 && (
                                    <IconButton
                                        size="small"
                                        onClick={() => removeUserRole(index)}
                                        color="error"
                                    >
                                        <RemoveCircle />
                                    </IconButton>
                                )}
                            </Box>

                            <Grid container spacing={2}>
                                <Grid item xs={12} md={6}>
                                    <TextField
                                        fullWidth
                                        label="Role Name"
                                        value={role.roleName}
                                        onChange={(e) => updateUserRole(index, 'roleName', e.target.value)}
                                        error={!!errors[`userRole_${index}_name`]}
                                        helperText={errors[`userRole_${index}_name`]}
                                    />
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <TextField
                                        fullWidth
                                        label="Price per User ($)"
                                        type="number"
                                        value={role.pricePerUser}
                                        onChange={(e) => updateUserRole(index, 'pricePerUser', e.target.value)}
                                        error={!!errors[`userRole_${index}_price`]}
                                        helperText={errors[`userRole_${index}_price`]}
                                    />
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>Billing Cycle</InputLabel>
                                        <Select
                                            value={role.billingCycle}
                                            label="Billing Cycle"
                                            onChange={(e) => updateUserRole(index, 'billingCycle', e.target.value)}
                                        >
                                            {billingCycles.map((cycle) => (
                                                <MenuItem key={cycle.value} value={cycle.value}>
                                                    {cycle.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                                <Grid item xs={12} md={6}>
                                    <FormControl fullWidth>
                                        <InputLabel>User Limit Type</InputLabel>
                                        <Select
                                            value={role.userLimitType}
                                            label="User Limit Type"
                                            onChange={(e) => updateUserRole(index, 'userLimitType', e.target.value)}
                                        >
                                            {userLimitTypes.map((type) => (
                                                <MenuItem key={type.value} value={type.value}>
                                                    {type.label}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                                {role.userLimitType === 'fixed' && (
                                    <Grid item xs={12} md={6}>
                                        <TextField
                                            fullWidth
                                            label="User Limit"
                                            type="number"
                                            value={role.userLimit}
                                            onChange={(e) => updateUserRole(index, 'userLimit', e.target.value)}
                                            error={!!errors[`userRole_${index}_limit`]}
                                            helperText={errors[`userRole_${index}_limit`]}
                                        />
                                    </Grid>
                                )}
                            </Grid>
                        </Card>
                    ))}
                </Box>
            )}

            {currentConfig.billingBasis === 'outcome' && (
                <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                            Outcome-Based Pricing Tiers
                        </Typography>
                        <Button
                            variant="outlined"
                            startIcon={<Add />}
                            onClick={addOutcomeTier}
                        >
                            Add Tier
                        </Button>
                    </Box>

                    {currentConfig.outcomeTiers.map((tier, index) => (
                        <Card key={index} sx={{ p: 3, mb: 2 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                                    Tier {index + 1}
                                </Typography>
                                {currentConfig.outcomeTiers.length > 1 && (
                                    <IconButton
                                        size="small"
                                        onClick={() => removeOutcomeTier(index)}
                                        color="error"
                                    >
                                        <RemoveCircle />
                                    </IconButton>
                                )}
                            </Box>

                            <Grid container spacing={2}>
                                <Grid item xs={12} md={4}>
                                    <TextField
                                        fullWidth
                                        label="Value Range From ($)"
                                        type="number"
                                        value={tier.rangeFrom}
                                        onChange={(e) => updateOutcomeTier(index, 'rangeFrom', e.target.value)}
                                        error={!!errors[`outcomeTier_${index}_from`]}
                                        helperText={errors[`outcomeTier_${index}_from`]}
                                    />
                                </Grid>
                                <Grid item xs={12} md={4}>
                                    <TextField
                                        fullWidth
                                        label="Value Range To ($)"
                                        type="number"
                                        value={tier.rangeTo}
                                        onChange={(e) => updateOutcomeTier(index, 'rangeTo', e.target.value)}
                                        error={!!errors[`outcomeTier_${index}_to`]}
                                        helperText={errors[`outcomeTier_${index}_to`]}
                                    />
                                </Grid>
                                <Grid item xs={12} md={4}>
                                    <TextField
                                        fullWidth
                                        label="Percentage Fee (%)"
                                        type="number"
                                        value={tier.percentageFee}
                                        onChange={(e) => updateOutcomeTier(index, 'percentageFee', e.target.value)}
                                        error={!!errors[`outcomeTier_${index}_fee`]}
                                        helperText={errors[`outcomeTier_${index}_fee`]}
                                    />
                                </Grid>
                            </Grid>
                        </Card>
                    ))}
                </Box>
            )}
        </Box>
    );

    // Render step content based on current step
    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return renderLicenseModelStep();
            case 1:
                return renderPricingConfigStep();
            case 2:
                return renderSaaSBillingStep();
            default:
                return null;
        }
    };

    // Render plan configuration interface
    const renderPlanConfiguration = () => (
        <Box sx={{ p: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
                <Box>
                    <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
                        Configure License Plans
                    </Typography>
                    <Typography variant="subtitle1" color="text.secondary">
                        {selectedProject?.name}
                    </Typography>
                </Box>
                <Button
                    variant="outlined"
                    startIcon={<Close />}
                    onClick={() => setCurrentView('dashboard')}
                >
                    Back to Dashboard
                </Button>
            </Box>

            <Paper sx={{ mb: 4 }}>
                <Tabs
                    value={activeTab}
                    onChange={(_, newValue) => {
                        setActiveTab(newValue);
                        setCurrentStep(0);
                        setErrors({});
                    }}
                    variant="scrollable"
                    scrollButtons="auto"
                >
                    {planNames.map((plan) => (
                        <Tab
                            key={plan}
                            label={plan}
                            icon={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    {planConfigs[plan]?.licenseModel && <CheckCircle color="success" fontSize="small" />}
                                </Box>
                            }
                            iconPosition="end"
                        />
                    ))}
                </Tabs>
            </Paper>

            <Paper sx={{ mb: 4 }}>
                <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
                        {currentPlan} Plan Configuration
                    </Typography>

                    <Stepper activeStep={currentStep} alternativeLabel>
                        <Step>
                            <StepLabel>License Model</StepLabel>
                        </Step>
                        <Step>
                            <StepLabel>Pricing Configuration</StepLabel>
                        </Step>
                        {currentConfig.licenseModel === 'saas' && (
                            <Step>
                                <StepLabel>Billing Details</StepLabel>
                            </Step>
                        )}
                    </Stepper>
                </Box>

                {renderStepContent()}

                <Box sx={{ p: 3, borderTop: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between' }}>
                    <Button
                        disabled={currentStep === 0}
                        onClick={handleBack}
                        variant="outlined"
                    >
                        Back
                    </Button>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                        {currentStep < getMaxSteps() - 1 ? (
                            <Button
                                onClick={handleNext}
                                variant="contained"
                                disabled={!currentConfig.licenseModel}
                            >
                                Next
                            </Button>
                        ) : (
                            <Button
                                onClick={() => {
                                    const stepErrors = validateStep(currentStep, currentConfig);
                                    setErrors(stepErrors);
                                    if (Object.keys(stepErrors).length === 0) {
                                        // Move to next plan or finish
                                        if (activeTab < planNames.length - 1) {
                                            setActiveTab(activeTab + 1);
                                            setCurrentStep(0);
                                            setErrors({});
                                        }
                                    }
                                }}
                                variant="contained"
                                color="success"
                            >
                                {activeTab < planNames.length - 1 ? 'Next Plan' : 'Complete Plan'}
                            </Button>
                        )}
                    </Box>
                </Box>
            </Paper>

            {/* Configuration Summary */}
            <Paper sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 3 }}>
                    Configuration Summary
                </Typography>

                <Grid container spacing={3}>
                    {planNames.map((plan) => {
                        const config = planConfigs[plan];
                        const isConfigured = config.licenseModel;

                        return (
                            <Grid item xs={12} md={6} lg={3} key={plan}>
                                <Card
                                    sx={{
                                        p: 2,
                                        border: 1,
                                        borderColor: isConfigured ? 'success.main' : 'divider',
                                        backgroundColor: isConfigured ? 'success.light' : 'background.paper'
                                    }}
                                >
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                                            {plan}
                                        </Typography>
                                        {isConfigured ? (
                                            <CheckCircle color="success" />
                                        ) : (
                                            <Cancel color="disabled" />
                                        )}
                                    </Box>

                                    {isConfigured ? (
                                        <Box>
                                            <Typography variant="body2" sx={{ mb: 1 }}>
                                                <strong>Model:</strong> {licenseModels.find(m => m.value === config.licenseModel)?.label}
                                            </Typography>
                                            {config.licenseModel === 'saas' && config.billingBasis && (
                                                <Typography variant="body2">
                                                    <strong>Billing:</strong> {saasBillingBasis.find(b => b.value === config.billingBasis)?.label}
                                                </Typography>
                                            )}
                                            {(config.licenseModel === 'perpetual' || config.licenseModel === 'term') && config.oneTimeCost && (
                                                <Typography variant="body2">
                                                    <strong>Cost:</strong> ${config.oneTimeCost}
                                                </Typography>
                                            )}
                                        </Box>
                                    ) : (
                                        <Typography variant="body2" color="text.secondary">
                                            Not configured
                                        </Typography>
                                    )}
                                </Card>
                            </Grid>
                        );
                    })}
                </Grid>

                <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
                    <Button
                        variant="contained"
                        size="large"
                        startIcon={isLoading ? null : <Save />}
                        onClick={handleSaveConfiguration}
                        disabled={isLoading || !planNames.every(plan => planConfigs[plan]?.licenseModel)}
                        sx={{ minWidth: 200 }}
                    >
                        {isLoading ? (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <LinearProgress sx={{ width: 100 }} />
                                Saving...
                            </Box>
                        ) : (
                            'Save All Configurations'
                        )}
                    </Button>
                </Box>
            </Paper>
        </Box>
    );

    return (
        <ThemeProvider theme={theme}>
            <Box sx={{ minHeight: '100vh', backgroundColor: 'background.default' }}>
                {currentView === 'dashboard' ? renderProjectDashboard() : renderPlanConfiguration()}
            </Box>
        </ThemeProvider>
    );
};

export default License;