
// src/contexts/ThemeContext.jsx (or ThemeContext.tsx)
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

const ThemeContext = createContext(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// --- Theme Definitions for each Brand ---
// Define CSS variables and MUI palette properties for each brand here.
// IMPORTANT: Palette colors must be concrete hex/rgb values for MUI's internal calculations.
// Other CSS variables can be defined as strings to be injected.
const BRAND_THEMES_DATA = {
  // --- My Company (Default) ---
  myCompany: {
    name: 'My Company',
    cssVariables: `
      /* My Company's Primary Palette */
      --color-primary-brand: #0F5FDC; /* Dark Blue */
      --color-secondary-brand: #2ECCCB; /* Software Teal */
      --color-accent-brand: #3C91FF; /* Software Blue */

      /* Text Colors for My Company */
      --color-text-default-brand: #000032; /* Midnight Blue */
      --color-text-secondary-brand: #17707F; /* Dark Teal */
      --color-text-muted-brand: #8CC8FA; /* Mid Blue */
      --color-text-link-brand: #0F5FDC;
      --color-text-link-hover-brand: #3C91FF;

      /* Backgrounds for My Company */
      --theme-background-primary-brand: #ECF3F8; /* Tech Grey */
      --theme-background-secondary-brand: #FFFFFF; /* White */
      --theme-border-color-brand: #DCE6F0; /* Light Blue */

      /* Buttons for My Company */
      --button-primary-bg-brand: #2ECCCB; /* Software Teal */
      --button-primary-hover-bg-brand: #23A3AD; /* Software Teal AA */
      --button-secondary-bg-brand: #0F5FDC; /* Dark Blue */
      --button-color-brand: #FFFFFF;



       --button-add:rgb(212, 69, 17);
       --button-add-hover: #23A3AD;
      --button-ok:#007a87;
      --button-ok-hover: #23A3AD;
      --button-cancel:  #007a87;
      --button-cancel-hover: #23A3AD;
      --button-edit: #007a87;
      --button-edit-hover: #23A3AD;
      --button-approve: #007a87;
      --button-approve-hover: #23A3AD;
      --button-reject: #007a87;
      --button-reject-hover: #23A3AD;
      --button-delete: #007a87;
      --button-delete-hover: #23A3AD;

      /* Rating*/ 
       --color-rating-star :  #007a87;

      /* CHECK box */
      --checkbox: #007a87;
  /Slider/
    --SliderWidth:300px;
    
    /speeddail/
    speeddial-container {
    height: var(--speeddial-container-height);
    position: relative;
    z-index: 0;
    margin-bottom: var(--spacing-lg);
  }


      /* Cards for My Company */
      --card-shadow-brand: 0 4px 6px rgba(0, 0, 0, 0.1);
      --card-border-radius-brand: 8px;

      /* Navbar/Footer */
      --navbar-bg-brand: #0F5FDC; /* Dark Blue */
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #ECF3F8; /* Tech Grey */
      --footer-text-color-brand: #17707F; /* Dark Teal */

      /* Mapping generic variables to brand-specific ones */
      --color-software-teal: var(--color-secondary-brand);
      --color-software-teal-aa: #23A3AD; /* Keeping universal if not overridden by brand */
      --color-dark-blue: var(--color-primary-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;
      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9; /* Universal if not overridden */
      --color-light-teal: #A4F4FF; /* Universal if not overridden */
      --color-software-blue: var(--color-accent-brand);
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000032;
      --alert-success-bg: #000000;
      --alert-success-color: #ffffff;
      /* Status Colors (universal in this example, but can be brand-specific) */
      --color-status-success: #28a745;
      --color-status-info: #17a2b8;
      --color-status-warning: #ffc107;
      --color-status-error: #dc3545;

      --color-status-success-background: #d4edda;
      --color-status-info-background: #d1ecf1;
      --color-status-warning-background: #fff3cd;
      --color-status-error-background: #f8d7da;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-padding: 1rem;
      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-height: 60px;
      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      /* Typography */
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-bold: 700;
      --font-family-primary: 'HCLTech Roobert', Roboto, sans-serif; /* Your company's specific font */
      --font-size-base: 1rem;
      --line-height-body: 1.5rem; /* 24px */

      /* Spacing */
      --spacing-xs: 0.25rem;
      --spacing-sm: 0.5rem;
      --spacing-md: 1rem;
      --spacing-lg: 1.5rem;
      --spacing-xl: 2rem;

      /* Radius */
      --radius-sm: calc(var(--card-border-radius-brand) / 2);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.5);

      /* Input */
      --input-padding: 0.5rem;
      --input-border-thickness: 1px;
      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      /* Transitions */
      --transition-fast: 0.2s ease-in-out;
      --transition-medium: 0.4s ease-in-out;
      --transition-ease-in-out: all 0.3s ease-in-out;
    `,
    // MUI Palette configuration (must use concrete color values)
    muiPalette: {
      primary: { main: '#0F5FDC', light: '#3C91FF', dark: '#000032', contrastText: '#FFFFFF' },
      secondary: { main: '#2ECCCB', light: '#A4F4FF', dark: '#17707F', contrastText: '#000000' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#ECF3F8', paper: '#FFFFFF' },
      text: { primary: '#000032', secondary: '#17707F', disabled: '#8CC8FA' },
    },
    // Fonts specific to this brand (if different from default HCLTech Roobert)
    fonts: `
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/Roobert-Medium.woff2') format('woff2');
        font-weight: 500;
        font-style: normal;
      }
      @font-face {
        font-family: 'HCLTech Roobert';
        src: url('/fonts/Roobert-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  // --- Brand A ---
  brandA: {
    name: 'Brand A',
    cssVariables: `
      --color-primary-brand: #B22222; /* Firebrick */
      --color-secondary-brand: #FFD700; /* Gold */
      --color-accent-brand: #4169E1; /* Royal Blue */

      --color-text-default-brand: #333333; /* Dark Grey */
      --color-text-secondary-brand: #666666; /* Mid Grey */
      --color-text-muted-brand: #AAAAAA; /* Light Grey */
      --color-text-link-brand: #B22222;
      --color-text-link-hover-brand: #8B0000;

      --theme-background-primary-brand: #F8F8F8; /* Off-white */
      --theme-background-secondary-brand: #FFFFFF; /* White */
      --theme-border-color-brand: #DDDDDD;

      --button-primary-bg-brand: #B22222;
      --button-primary-hover-bg-brand: #8B0000;
      --button-secondary-bg-brand: #FFD700;
      --button-color-brand: #FFFFFF;

      --card-shadow-brand: 0 6px 12px rgba(0, 0, 0, 0.1);
      --card-border-radius-brand: 4px;

      --navbar-bg-brand: #B22222;
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #333333;
      --footer-text-color-brand: #DDDDDD;

      /* Mapping generic variables to brand-specific ones */
      --color-dark-blue: var(--color-primary-brand); /* Example mapping */
      --color-software-teal: var(--color-secondary-brand); /* Example mapping */
      --color-software-blue: var(--color-accent-brand); /* Example mapping */
      --color-white: #FFFFFF;
      --color-black: #000000;

      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9; /* Keeps universal if not overridden */
      --color-light-teal: #A4F4FF; /* Keeps universal if not overridden */
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000000;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      --radius-sm: calc(var(--card-border-radius-brand) / 2);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.5);

      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      /* Font Families - If Brand A uses its own font */
      --font-family-primary: 'BrandAFont', sans-serif;
    `,
    muiPalette: {
      primary: { main: '#B22222', light: '#D82B2B', dark: '#8B0000', contrastText: '#FFFFFF' },
      secondary: { main: '#FFD700', light: '#FFFFE0', dark: '#B8860B', contrastText: '#000000' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#F8F8F8', paper: '#FFFFFF' },
      text: { primary: '#333333', secondary: '#666666', disabled: '#AAAAAA' },
    },
    fonts: `
      @font-face {
        font-family: 'BrandAFont';
        src: url('/fonts/BrandAFont-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'BrandAFont';
        src: url('/fonts/BrandAFont-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  // --- Brand B ---
  brandB: {
    name: 'Brand B',
    cssVariables: `
      --color-primary-brand: #006400; /* Dark Green */
      --color-secondary-brand: #6A5ACD; /* Slate Blue */
      --color-accent-brand: #FF69B4; /* Hot Pink */

      --color-text-default-brand: #1A1A1A; /* Very Dark Grey */
      --color-text-secondary-brand: #4A4A4A; /* Dark Grey */
      --color-text-muted-brand: #7A7A7A;
      --color-text-link-brand: #006400;
      --color-text-link-hover-brand: #004D00;

      --theme-background-primary-brand: #E0FFE0; /* Light Green */
      --theme-background-secondary-brand: #FFFFFF; /* White */
      --theme-border-color-brand: #C0ECC0;

      --button-primary-bg-brand: #006400;
      --button-primary-hover-bg-brand: #004D00;
      --button-secondary-bg-brand: #6A5ACD;
      --button-color-brand: #FFFFFF;

      --card-shadow-brand: 0 2px 4px rgba(0, 0, 0, 0.08);
      --card-border-radius-brand: 12px; /* More rounded */

      --navbar-bg-brand: #006400;
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #1A1A1A;
      --footer-text-color-brand: #FFFFFF;

      /* Mapping generic variables */
      --color-dark-blue: var(--color-primary-brand);
      --color-software-teal: var(--color-secondary-brand);
      --color-software-blue: var(--color-accent-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;

      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9;
      --color-light-teal: #A4F4FF;
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000000;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      --radius-sm: calc(var(--card-border-radius-brand) / 3);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.25);

      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      /* Font Families - If Brand B uses its own font */
      --font-family-primary: 'BrandBFont', sans-serif;
    `,
    muiPalette: {
      primary: { main: '#006400', light: '#339933', dark: '#003300', contrastText: '#FFFFFF' },
      secondary: { main: '#6A5ACD', light: '#8F81D6', dark: '#4E3A9C', contrastText: '#FFFFFF' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#E0FFE0', paper: '#FFFFFF' },
      text: { primary: '#1A1A1A', secondary: '#4A4A4A', disabled: '#7A7A7A' },
    },
    fonts: `
      @font-face {
        font-family: 'BrandBFont';
        src: url('/fonts/BrandBFont-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'BrandBFont';
        src: url('/fonts/BrandBFont-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  // --- Brand C ---
  brandC: {
    name: 'Brand C',
    cssVariables: `
      --color-primary-brand: #4B0082; /* Indigo */
      --color-secondary-brand: #FFC0CB; /* Pink */
      --color-accent-brand: #8A2BE2; /* Blue Violet */

      --color-text-default-brand: #2C3E50; /* Dark Navy */
      --color-text-secondary-brand: #7F8C8D; /* Asbestos */
      --color-text-muted-brand: #BDC3C7; /* Silver */
      --color-text-link-brand: #4B0082;
      --color-text-link-hover-brand: #6A0DAD;

      --theme-background-primary-brand: #F2F4F7; /* Very Light Grey */
      --theme-background-secondary-brand: #FFFFFF;
      --theme-border-color-brand: #DDE2E7;

      --button-primary-bg-brand: #4B0082;
      --button-primary-hover-bg-brand: #6A0DAD;
      --button-secondary-bg-brand: #FFC0CB;
      --button-color-brand: #FFFFFF;

      --card-shadow-brand: 0 1px 3px rgba(0, 0, 0, 0.08);
      --card-border-radius-brand: 6px;

      --navbar-bg-brand: #4B0082;
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #2C3E50;
      --footer-text-color-brand: #F2F4F7;

      /* Mapping generic variables */
      --color-dark-blue: var(--color-primary-brand);
      --color-software-teal: var(--color-secondary-brand);
      --color-software-blue: var(--color-accent-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;

      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9;
      --color-light-teal: #A4F4FF;
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000000;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      --radius-sm: calc(var(--card-border-radius-brand) / 2);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.5);

      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      --font-family-primary: 'BrandCFont', sans-serif;
    `,
    muiPalette: {
      primary: { main: '#4B0082', light: '#6A0DAD', dark: '#300055', contrastText: '#FFFFFF' },
      secondary: { main: '#FFC0CB', light: '#FFE5ED', dark: '#E099A6', contrastText: '#000000' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#F2F4F7', paper: '#FFFFFF' },
      text: { primary: '#2C3E50', secondary: '#7F8C8D', disabled: '#BDC3C7' },
    },
    fonts: `
      @font-face {
        font-family: 'BrandCFont';
        src: url('/fonts/BrandCFont-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'BrandCFont';
        src: url('/fonts/BrandCFont-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  // --- Brand D ---
  brandD: {
    name: 'Brand D',
    cssVariables: `
      --color-primary-brand: #008B8B; /* Dark Cyan */
      --color-secondary-brand: #FF8C00; /* Dark Orange */
      --color-accent-brand: #20B2AA; /* Light Sea Green */

      --color-text-default-brand: #2F4F4F; /* Dark Slate Grey */
      --color-text-secondary-brand: #696969; /* Dim Grey */
      --color-text-muted-brand: #A9A9A9; /* Dark Grey */
      --color-text-link-brand: #008B8B;
      --color-text-link-hover-brand: #005F5F;

      --theme-background-primary-brand: #F0FFFF; /* Azure */
      --theme-background-secondary-brand: #FFFFFF;
      --theme-border-color-brand: #ADD8E6; /* Light Blue */

      --button-primary-bg-brand: #008B8B;
      --button-primary-hover-bg-brand: #005F5F;
      --button-secondary-bg-brand: #FF8C00;
      --button-color-brand: #FFFFFF;

      --card-shadow-brand: 0 5px 10px rgba(0, 0, 0, 0.12);
      --card-border-radius-brand: 0px; /* Sharp corners */

      --navbar-bg-brand: #008B8B;
      --navbar-text-color-brand: #FFFFFF;
      --footer-bg-brand: #2F4F4F;
      --footer-text-color-brand: #F0FFFF;

      /* Mapping generic variables */
      --color-dark-blue: var(--color-primary-brand);
      --color-software-teal: var(--color-secondary-brand);
      --color-software-blue: var(--color-accent-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;

      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9;
      --color-light-teal: #A4F4FF;
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #FFFFFF;
      --color-text-on-light: #000000;

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      --radius-sm: var(--card-border-radius-brand); /* For Brand D, all radii are 0 */
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: var(--card-border-radius-brand);

      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      --font-family-primary: 'BrandDFont', sans-serif;
    `,
    muiPalette: {
      primary: { main: '#008B8B', light: '#20B2AA', dark: '#005F5F', contrastText: '#FFFFFF' },
      secondary: { main: '#FF8C00', light: '#FFA500', dark: '#CC7000', contrastText: '#FFFFFF' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#F0FFFF', paper: '#FFFFFF' },
      text: { primary: '#2F4F4F', secondary: '#696969', disabled: '#A9A9A9' },
    },
    fonts: `
      @font-face {
        font-family: 'BrandDFont';
        src: url('/fonts/BrandDFont-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'BrandDFont';
        src: url('/fonts/BrandDFont-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  },
  // --- Brand E (Dark-Themed by Default) ---
  brandE: {
    name: 'Brand E (Dark)',
    cssVariables: `
      --color-primary-brand: #9370DB; /* Medium Purple */
      --color-secondary-brand: #3CB371; /* Medium Sea Green */
      --color-accent-brand: #FFA07A; /* Light Salmon */

      --color-text-default-brand: #F8F8F8; /* White smoke */
      --color-text-secondary-brand: #C0C0C0; /* Silver */
      --color-text-muted-brand: #808080; /* Grey */
      --color-text-link-brand: #9370DB;
      --color-text-link-hover-brand: #BA55D3; /* Medium Orchid */

      --theme-background-primary-brand: #2C3E50; /* Dark Navy */
      --theme-background-secondary-brand: #34495E; /* Wet Asphalt */
      --theme-border-color-brand: #4A607C;

      --button-primary-bg-brand: #9370DB;
      --button-primary-hover-bg-brand: #BA55D3;
      --button-secondary-bg-brand: #3CB371;
      --button-color-brand: #FFFFFF;

      --card-shadow-brand: 0 8px 16px rgba(0, 0, 0, 0.25);
      --card-border-radius-brand: 10px;

      --navbar-bg-brand: #2C3E50;
      --navbar-text-color-brand: #F8F8F8;
      --footer-bg-brand: #1A242F;
      --footer-text-color-brand: #C0C0C0;

      /* Mapping generic variables */
      --color-dark-blue: var(--color-primary-brand);
      --color-software-teal: var(--color-secondary-brand);
      --color-software-blue: var(--color-accent-brand);
      --color-white: #FFFFFF;
      --color-black: #000000;

      --color-dark-teal: var(--color-text-secondary-brand);
      --color-mid-teal: #36D6D9;
      --color-light-teal: #A4F4FF;
      --color-mid-blue: var(--color-text-muted-brand);
      --color-light-blue: var(--theme-border-color-brand);
      --color-midnight-blue: var(--color-text-default-brand);
      --color-tech-grey: var(--theme-background-primary-brand);

      --theme-background-primary: var(--theme-background-primary-brand);
      --theme-background-secondary: var(--theme-background-secondary-brand);
      --theme-border-color: var(--theme-border-color-brand);

      --color-text-default: var(--color-text-default-brand);
      --color-text-secondary: var(--color-text-secondary-brand);
      --color-text-muted: var(--color-text-muted-brand);
      --color-text-link: var(--color-text-link-brand);
      --color-text-link-hover: var(--color-text-link-hover-brand);
      --color-text-heading: var(--color-text-default-brand);
      --color-text-on-dark: #000000; /* Text on very dark elements in dark theme */
      --color-text-on-light: #F8F8F8; /* Text on light elements in dark theme */

      --button-primary-bg: var(--button-primary-bg-brand);
      --button-primary-hover-bg: var(--button-primary-hover-bg-brand);
      --button-secondary-bg: var(--button-secondary-bg-brand);
      --button-color: var(--button-color-brand);

      --card-shadow: var(--card-shadow-brand);
      --card-border-radius: var(--card-border-radius-brand);
      --card-bg: var(--theme-background-secondary-brand);
      --card-header-color: var(--color-text-default-brand);

      --navbar-bg: var(--navbar-bg-brand);
      --navbar-text-color: var(--navbar-text-color-brand);
      --footer-bg: var(--footer-bg-brand);
      --footer-text-color: var(--footer-text-color-brand);

      --radius-sm: calc(var(--card-border-radius-brand) / 2.5);
      --radius-md: var(--card-border-radius-brand);
      --radius-lg: calc(var(--card-border-radius-brand) * 1.5);

      --input-border-color: var(--theme-border-color-brand);
      --input-focus-border: var(--color-primary-brand);
      --input-placeholder-color: var(--color-text-muted-brand);
      --input-text-color: var(--color-text-default-brand);

      --font-family-primary: 'BrandEFont', sans-serif;
    `,
    muiPalette: {
      primary: { main: '#9370DB', light: '#BA55D3', dark: '#6A5ACD', contrastText: '#FFFFFF' },
      secondary: { main: '#3CB371', light: '#6EE09E', dark: '#2C8E5A', contrastText: '#FFFFFF' },
      error: { main: '#dc3545' },
      warning: { main: '#ffc107' },
      info: { main: '#17a2b8' },
      success: { main: '#28a745' },
      background: { default: '#2C3E50', paper: '#34495E' },
      text: { primary: '#F8F8F8', secondary: '#C0C0C0', disabled: '#808080' },
    },
    fonts: `
      @font-face {
        font-family: 'BrandEFont';
        src: url('/fonts/BrandEFont-Regular.woff2') format('woff2');
        font-weight: 400;
        font-style: normal;
      }
      @font-face {
        font-family: 'BrandEFont';
        src: url('/fonts/BrandEFont-Bold.woff2') format('woff2');
        font-weight: 700;
        font-style: normal;
      }
    `
  }
};

export const ThemeProvider = ({ children }) => {
  const [currentBrandKey, setCurrentBrandKey] = useState(() => {
    // Initialize brand from localStorage or default to 'myCompany'
    if (typeof window !== 'undefined') {
      return localStorage.getItem('currentBrand') || 'myCompany';
    }
    return 'myCompany';
  });

  const injectThemeStyles = useCallback((brandKey) => {
    const brandData = BRAND_THEMES_DATA[brandKey];
    if (!brandData) {
      console.warn(`Brand "${brandKey}" data not found.`);
      return;
    }

    let styleTag = document.getElementById('brand-theme-styles');
    if (!styleTag) {
      styleTag = document.createElement('style');
      styleTag.id = 'brand-theme-styles';
      document.head.appendChild(styleTag);
    }

    let fontStyleTag = document.getElementById('brand-theme-fonts');
    if (!fontStyleTag) {
      fontStyleTag = document.createElement('style');
      fontStyleTag.id = 'brand-theme-fonts';
      document.head.appendChild(fontStyleTag);
    }

    // Inject CSS variables for the current brand
    styleTag.textContent = `:root { ${brandData.cssVariables} }`;

    // Inject @font-face rules for the current brand
    fontStyleTag.textContent = brandData.fonts || ''; // Use empty string if no fonts defined
    
    // Set data-theme attribute on body if you still want to leverage it for very specific overrides
    // document.body.setAttribute('data-theme', brandKey); // Optional, if you still want to use it
  }, []);

  useEffect(() => {
    injectThemeStyles(currentBrandKey);
    localStorage.setItem('currentBrand', currentBrandKey);
  }, [currentBrandKey, injectThemeStyles]);

  const setBrand = useCallback((brandKey) => {
    if (BRAND_THEMES_DATA[brandKey]) {
      setCurrentBrandKey(brandKey);
    } else {
      console.warn(`Brand "${brandKey}" not found. Falling back to default.`);
      setCurrentBrandKey('myCompany');
    }
  }, []);

  const getMuiPaletteConfig = useCallback(() => {
    return BRAND_THEMES_DATA[currentBrandKey]?.muiPalette;
  }, [currentBrandKey]);

  // Expose brand names for the selector
  const brandOptions = Object.keys(BRAND_THEMES_DATA).map(key => ({
    key: key,
    name: BRAND_THEMES_DATA[key].name
  }));

  return (
    <ThemeContext.Provider value={{ currentBrandKey, setBrand, brandOptions, getMuiPaletteConfig }}>
      {children}
    </ThemeContext.Provider>
  );
};
