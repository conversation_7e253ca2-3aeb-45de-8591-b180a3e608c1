import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, Shield, RemoveRedEye, Edit as EditI<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DonutLarge
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialUsers = [
    { id: 1, userId: 'USR-001', name: '<PERSON>', email: '<EMAIL>', status: 'Active', access: 'Admin', joined: '2023-01-01' },
    { id: 2, userId: 'USR-002', name: '<PERSON>', email: '<EMAIL>', status: 'Active', access: 'Read', joined: '2023-02-15' },
    { id: 3, userId: 'USR-003', name: 'Priya Patel', email: '<EMAIL>', status: 'Inactive', access: 'ReadWrite', joined: '2022-12-10' },
];

const ALL_COLUMNS = [
    { key: 'userId', label: 'User ID', type: 'string' },
    { key: 'name', label: 'Name', type: 'string' },
    { key: 'email', label: 'Email', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'access', label: 'Access', type: 'string' },
    { key: 'joined', label: 'Joined', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ user, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(user, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(user, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([user.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const UserTable = ({ users, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < users.length} checked={users.length > 0 && selectedIds.length === users.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {users.map(u => (
                        <TableRow key={u.id} hover selected={selectedId === u.id} onClick={() => onRowClick(u)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(u.id)} onChange={() => onSelectOne(u.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{u[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons user={u} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const UserCard = ({ user, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(user.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons user={user} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{user.name}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{user.email}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: user.status }} label={user.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Access:</strong> {user.access}</Typography>
            <Typography variant="body2"><strong>Joined:</strong> {user.joined}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const UserCompactCard = ({ user, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(user.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons user={user} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{user.name}</Typography>
                <Typography variant="caption" color="text.secondary">{user.email}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{user.access}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: user.status }} label={user.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const UserListItem = ({ user, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(user.id)} />
            <Box>
                <Typography fontWeight="bold">{user.name}</Typography>
                <Typography variant="body2" color="text.secondary">{user.email}</Typography>
            </Box>
            <Typography variant="body2">{user.access}</Typography>
            <Typography variant="body2">{user.status}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: user.status }} label={user.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons user={user} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const UserGraph = ({ user, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && user) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Days Since Joined'],
                    datasets: [{
                        label: 'Days Since Joined',
                        data: [
                            Math.max(0, Math.floor((new Date() - new Date(user.joined)) / (1000 * 60 * 60 * 24)))
                        ],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${user.name} - Days Since Joined` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [user, chartType]);

    return (
        <>
            {user ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a user to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const User = () => {
    const [users, setUsers] = useState(initialUsers);
    const [selectedUser, setSelectedUser] = useState(null);
    const [sortColumn, setSortColumn] = useState('userId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new user', target: 'USR-001', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Activated user', target: 'USR-002', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'IT', action: 'Deactivated user', target: 'USR-003', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [chartType, setChartType] = useState('bar');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Quick Filter options (status and access)
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(users.map(u => u.status))];
        const accesses = [...new Set(users.map(u => u.access))];
        return [...statuses, ...accesses];
    }, [users]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Active', 'Inactive'];
        const accessValues = ['Admin', 'Read', 'ReadWrite'];
        let field = '';
        if (statusValues.includes(value)) field = 'status';
        else if (accessValues.includes(value)) field = 'access';
        else field = 'name';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const processedUsers = useMemo(() => {
        let current = users;
        if (filter !== 'all') current = current.filter(u => u.status.toLowerCase() === filter || u.access.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(u => u.userId.toLowerCase().includes(term) || u.name.toLowerCase().includes(term) || u.email.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(user => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const userValue = String(user[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return userValue === filterValue;
                        case 'Not Equals': return userValue !== filterValue;
                        case 'Contains': return userValue.includes(filterValue);
                        case 'Starts With': return userValue.startsWith(filterValue);
                        case 'Ends With': return userValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [users, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const summaryStats = useMemo(() => ({
        total: users.length,
        active: users.filter(u => u.status === 'Active').length,
        inactive: users.filter(u => u.status === 'Inactive').length,
        admin: users.filter(u => u.access === 'Admin').length,
        read: users.filter(u => u.access === 'Read').length,
        readwrite: users.filter(u => u.access === 'ReadWrite').length,
    }), [users]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (user) => setSelectedUser(user);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedUsers.map(u => u.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displayUser = useMemo(() => {
        const isSelectedVisible = processedUsers.some(u => u.id === selectedUser?.id);
        if (isSelectedVisible) return selectedUser;
        return processedUsers.length > 0 ? processedUsers[0] : null;
    }, [processedUsers, selectedUser]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedUsers.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedUsers.map(u => <UserCard key={u.id} user={u} onClick={() => setSelectedUser(u)} isSelected={displayUser?.id === u.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(u.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <UserTable
                            users={processedUsers}
                            onRowClick={setSelectedUser}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displayUser?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedUsers.map(u => <UserCompactCard key={u.id} user={u} onClick={() => setSelectedUser(u)} isSelected={displayUser?.id === u.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(u.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedUsers.map(u => <UserListItem key={u.id} user={u} onClick={() => setSelectedUser(u)} isSelected={displayUser?.id === u.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(u.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Users</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Users</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'active'} onClick={() => setFilter('active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'inactive'} onClick={() => setFilter('inactive')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.inactive}</Typography><Typography variant="body2">Inactive</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'admin'} onClick={() => setFilter('admin')}>
                                        <AdminComponents.SummaryAvatar variant="active"><Shield /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.admin}</Typography><Typography variant="body2">Admin</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'read'} onClick={() => setFilter('read')}>
                                        <AdminComponents.SummaryAvatar variant="active"><RemoveRedEye /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.read}</Typography><Typography variant="body2">Read</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'readwrite'} onClick={() => setFilter('readwrite')}>
                                        <AdminComponents.SummaryAvatar variant="active"><EditIcon /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.readwrite}</Typography><Typography variant="body2">ReadWrite</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add User</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedUsers.length > 0 && selectedIds.length === processedUsers.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedUsers.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <UserGraph user={displayUser} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                    {/* Sidebar Drawer for Advanced Search and Table Settings */}
                    <Drawer
                        variant="persistent"
                        anchor="right"
                        open={isSidebarOpen}
                    >
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader>
                                <Typography variant="h6">
                                    {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                                </Typography>
                                <IconButton onClick={() => setIsSidebarOpen(false)}>
                                    <Cancel />
                                </IconButton>
                            </AdminComponents.SidebarHeader>
                            <AdminComponents.SidebarContent>
                                {sidebarMode === 'search' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default User;
