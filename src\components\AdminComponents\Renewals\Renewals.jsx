import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Drawer, Chip, FormControl, InputLabel, Select, MenuItem
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, WarningAmber, ShowChart, PieChart, DonutLarge
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialRenewals = [
    { id: 1, renewalId: 'RN-2024-001', customer: 'Prevost-<PERSON><PERSON>', name: 'License Renewal', status: 'Upcoming', type: 'LICENSE', value: '$850,000', startDate: '2025-01-01', endDate: '2026-01-01', owner: '<PERSON>' },
    { id: 2, renewalId: 'RN-2024-002', customer: 'Reman', name: 'Support Renewal', status: 'At Risk', type: 'SUPPORT', value: '$450,000', startDate: '2023-06-01', endDate: '2024-06-01', owner: 'IT Department' },
    { id: 3, renewalId: 'RN-2024-003', customer: 'Wirtgen-FSM', name: 'Service Renewal', status: 'Expired', type: 'SERVICE', value: '$320,000', startDate: '2024-03-15', endDate: '2025-03-15', owner: 'Legal Team' },
];

const ALL_COLUMNS = [
    { key: 'renewalId', label: 'Renewal ID', type: 'string' },
    { key: 'customer', label: 'Customer', type: 'string' },
    { key: 'name', label: 'Name', type: 'string' },
    { key: 'type', label: 'Type', type: 'string' },
    { key: 'value', label: 'Value', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'startDate', label: 'Start Date', type: 'string' },
    { key: 'endDate', label: 'End Date', type: 'string' },
    { key: 'owner', label: 'Owner', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ renewal, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(renewal, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(renewal, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([renewal.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const RenewalTable = ({ renewals, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < renewals.length} checked={renewals.length > 0 && selectedIds.length === renewals.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renewals.map(r => (
                        <TableRow key={r.id} hover selected={selectedId === r.id} onClick={() => onRowClick(r)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(r.id)} onChange={() => onSelectOne(r.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{r[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons renewal={r} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const RenewalCard = ({ renewal, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(renewal.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons renewal={renewal} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{renewal.name}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{renewal.customer}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: renewal.status }} label={renewal.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Type:</strong> {renewal.type}</Typography>
            <Typography variant="body2"><strong>Value:</strong> {renewal.value}</Typography>
            <Typography variant="body2"><strong>Owner:</strong> {renewal.owner}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const RenewalCompactCard = ({ renewal, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(renewal.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons renewal={renewal} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{renewal.name}</Typography>
                <Typography variant="caption" color="text.secondary">{renewal.customer}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{renewal.type}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: renewal.status }} label={renewal.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const RenewalListItem = ({ renewal, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(renewal.id)} />
            <Box>
                <Typography fontWeight="bold">{renewal.name}</Typography>
                <Typography variant="body2" color="text.secondary">{renewal.customer}</Typography>
            </Box>
            <Typography variant="body2">{renewal.owner}</Typography>
            <Typography variant="body2">{renewal.type}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: renewal.status }} label={renewal.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons renewal={renewal} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const RenewalGraph = ({ renewal, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && renewal) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Value'],
                    datasets: [{
                        label: 'Renewal Value',
                        data: [parseInt(renewal.value.replace(/[^0-9]/g, ''))],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${renewal.name} - Value` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [renewal, chartType]);

    return (
        <>
            {renewal ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a renewal to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Renewals = () => {
    const [renewals, setRenewals] = useState(initialRenewals);
    const [selectedRenewal, setSelectedRenewal] = useState(null);
    const [sortColumn, setSortColumn] = useState('renewalId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new renewal', target: 'RN-2024-001', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Approved renewal', target: 'RN-2024-002', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Finance', action: 'Marked as expired', target: 'RN-2024-003', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [chartType, setChartType] = useState('bar');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Quick Filter options (status and type)
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(renewals.map(r => r.status))];
        const types = [...new Set(renewals.map(r => r.type))];
        return [...statuses, ...types];
    }, [renewals]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Upcoming', 'At Risk', 'Expired'];
        const field = statusValues.includes(value) ? 'status' : 'type';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const processedRenewals = useMemo(() => {
        let current = renewals;
        if (filter !== 'all') current = current.filter(r => r.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(r => r.renewalId.toLowerCase().includes(term) || r.customer.toLowerCase().includes(term) || r.name.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(renewal => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const renewalValue = String(renewal[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return renewalValue === filterValue;
                        case 'Not Equals': return renewalValue !== filterValue;
                        case 'Contains': return renewalValue.includes(filterValue);
                        case 'Starts With': return renewalValue.startsWith(filterValue);
                        case 'Ends With': return renewalValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [renewals, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const summaryStats = useMemo(() => ({
        total: renewals.length,
        upcoming: renewals.filter(r => r.status === 'Upcoming').length,
        atRisk: renewals.filter(r => r.status === 'At Risk').length,
        expired: renewals.filter(r => r.status === 'Expired').length,
    }), [renewals]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (renewal) => setSelectedRenewal(renewal);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedRenewals.map(r => r.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displayRenewal = useMemo(() => {
        const isSelectedVisible = processedRenewals.some(r => r.id === selectedRenewal?.id);
        if (isSelectedVisible) return selectedRenewal;
        return processedRenewals.length > 0 ? processedRenewals[0] : null;
    }, [processedRenewals, selectedRenewal]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedRenewals.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedRenewals.map(r => <RenewalCard key={r.id} renewal={r} onClick={() => setSelectedRenewal(r)} isSelected={displayRenewal?.id === r.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(r.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <RenewalTable
                            renewals={processedRenewals}
                            onRowClick={setSelectedRenewal}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displayRenewal?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedRenewals.map(r => <RenewalCompactCard key={r.id} renewal={r} onClick={() => setSelectedRenewal(r)} isSelected={displayRenewal?.id === r.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(r.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedRenewals.map(r => <RenewalListItem key={r.id} renewal={r} onClick={() => setSelectedRenewal(r)} isSelected={displayRenewal?.id === r.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(r.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Renewals</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Renewals</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'upcoming'} onClick={() => setFilter('upcoming')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.upcoming}</Typography><Typography variant="body2">Upcoming</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'at risk'} onClick={() => setFilter('at risk')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><WarningAmber /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.atRisk}</Typography><Typography variant="body2">At Risk</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expired'} onClick={() => setFilter('expired')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expired}</Typography><Typography variant="body2">Expired</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add Renewal</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedRenewals.length > 0 && selectedIds.length === processedRenewals.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedRenewals.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <RenewalGraph renewal={displayRenewal} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                    {/* Sidebar Drawer for Advanced Search and Table Settings */}
                    <Drawer
                        variant="persistent"
                        anchor="right"
                        open={isSidebarOpen}
                    >
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader>
                                <Typography variant="h6">
                                    {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                                </Typography>
                                <IconButton onClick={() => setIsSidebarOpen(false)}>
                                    <Cancel />
                                </IconButton>
                            </AdminComponents.SidebarHeader>
                            <AdminComponents.SidebarContent>
                                {sidebarMode === 'search' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Renewals; 