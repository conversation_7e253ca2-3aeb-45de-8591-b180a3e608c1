import React, { useState, useMemo } from 'react';
import { suitesData } from '../../data/suites';
import { industriesData } from '../../data/modules';
import DynamicIcon from '../common/DynamicIcon';
import AdvancedFilter from './FilterSidebar';
// import './Suites.css';

const allFeatures = Array.from(new Set(suitesData.map(s => s.name)));

const Suites = () => {
  const [search, setSearch] = useState('');
  const [selectedFeatures, setSelectedFeatures] = useState([]);
  const [selectedIndustries, setSelectedIndustries] = useState([]);
  const [showAll, setShowAll] = useState(false);

  // Filtering logic (simulate features/industries for demo)
  const filteredSuites = useMemo(() => {
    return [...suitesData].sort((a, b) => a.name.localeCompare(b.name)).filter(suite => {
      // Search
      if (search && !suite.name.toLowerCase().includes(search.toLowerCase()) && !suite.description.toLowerCase().includes(search.toLowerCase())) {
        return false;
      }
      // Features (simulate: show all if none selected)
      // Industries (simulate: show all if none selected)
      return true;
    });
  }, [search, selectedFeatures, selectedIndustries]);

  return (
    <section id="suites" className="suites-section">
      <div className="suites-main">
        <div className="suites-header-row">
          <div className="section-header">
            <h2 className="section-title">
              <span role="img" aria-label="package">📦</span> Module Suites
            </h2>
            <p className="section-subtitle">
              Get the best value by choosing a package of modules tailored for your business.
            </p>
          </div>
        </div>
        <AdvancedFilter
          search={search}
          setSearch={setSearch}
          features={allFeatures}
          selectedFeatures={selectedFeatures}
          setSelectedFeatures={setSelectedFeatures}
          industries={industriesData}
          selectedIndustries={selectedIndustries}
          setSelectedIndustries={setSelectedIndustries}
        />
        <div className="grid-center-wrapper">
          <div className={`suites-grid${filteredSuites.length === 1 ? ' single-card' : ''} ${showAll ? 'expanded' : ''}`}>
            {filteredSuites.map((suite, idx) => (
              <div className="suite-card" key={suite.name}>
                {suite.tags && suite.tags.length > 0 && (
                  <div className={`module-tag-badge tag-${suite.tags[0].toLowerCase().replace(/\s+/g, '-')}`}>{suite.tags[0]}</div>
                )}
                <div className="suite-icon">
                  <DynamicIcon name={suite.icon} />
                </div>
                <h3 className="suite-name">{suite.name}</h3>
                <p className="suite-desc">{suite.description}</p>
                <ul className="suite-modules">
                  {suite.modules.map((mod) => (
                    <li key={mod}>
                      <span className="suite-module-icon">•</span> {mod}
                    </li>
                  ))}
                </ul>
                <div className="suite-actions">
                  <button className="suite-cart-btn">Add to Cart</button>
                  <button className="suite-try-btn">Request For Demo</button>
                </div>
              </div>
            ))}
          </div>
        </div>
        {filteredSuites.length > 0 && (
          <div className="view-more-row">
            <button className="view-more-btn" onClick={() => setShowAll(v => !v)}>
              {showAll ? 'View Less' : 'View More'}
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default Suites; 