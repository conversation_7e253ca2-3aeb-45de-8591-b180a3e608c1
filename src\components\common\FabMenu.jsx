import React, { useState } from 'react';
import { FaArrowUp, FaArrowDown, FaVideo, FaEnvelope, FaPlus, FaTimes, FaBell } from 'react-icons/fa';
// import './FabMenu.css';

const FabMenu = ({ isAdmin }) => {
  const [open, setOpen] = useState(false);

  // Default actions
  let actions = [
    {
      label: 'Go Up',
      icon: <FaArrowUp />,
      onClick: () => window.scrollTo({ top: 0, behavior: 'smooth' }),
    },
    {
      label: 'Go Down',
      icon: <FaArrowDown />,
      onClick: () => window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' }),
    },
    {
      label: 'Watch Video',
      icon: <FaVideo />,
      onClick: () => window.open('https://www.youtube.com/', '_blank'),
    },
    {
      label: 'Contact Us',
      icon: <FaEnvelope />,
      onClick: () => {
        const el = document.getElementById('contact');
        if (el) el.scrollIntoView({ behavior: 'smooth' });
      },
    },
  ];

  // If admin, replace last two actions with Add and Notification
  if (isAdmin) {
    actions = [
      actions[0],
      actions[1],
      {
        label: 'Add',
        icon: <FaPlus />,
        onClick: () => alert('Add action (admin only)!'),
      },
      {
        label: 'Notification',
        icon: <FaBell />,
        onClick: () => alert('Notification action (admin only)!'),
      },
    ];
  }

  return (
    <div className={`fab-menu${open ? ' open' : ''}`}>
      <button
        className="fab-main"
        aria-label={open ? 'Close menu' : 'Open menu'}
        onClick={() => setOpen((o) => !o)}
      >
        {open ? <FaTimes /> : <FaPlus />}
      </button>
      <div className="fab-actions">
        {actions.map((action, idx) => (
          <button
            key={action.label}
            className="fab-action"
            aria-label={action.label}
            title={action.label}
            style={{ '--fab-delay': open ? `${idx * 60}ms` : '0ms' }}
            onClick={() => {
              setOpen(false);
              setTimeout(action.onClick, 200);
            }}
          >
            {action.icon}
            <span className="fab-tooltip">{action.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default FabMenu; 