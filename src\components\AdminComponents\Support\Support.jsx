import React, { useEffect, useState } from "react";
import Modal from "../shared/Modal";
import Toast from "../shared/Toast";
import { FiPlus, FiEdit, FiTrash2, FiGrid, FiList, FiCalendar, FiClock, FiHeadphones, FiSave } from 'react-icons/fi';
import Card from '../shared/Card';
import { FiCheckSquare, FiCheckCircle, FiStar } from 'react-icons/fi';
import { FaTicketAlt } from 'react-icons/fa';
import { FiSettings, FiDownload, FiBookOpen } from 'react-icons/fi';
import { FaCogs } from 'react-icons/fa';

const PAGE_SIZE = 5;
const FIELDS = [
  { name: "ticketId", label: "Ticket ID" },
  { name: "customer", label: "Customer" },
  { name: "category", label: "Category" },
  { name: "priority", label: "Priority" },
  { name: "status", label: "Status" },
  { name: "created", label: "Created" },
  { name: "assignedTo", label: "Assigned To" },
  { name: "product", label: "Product" },
  { name: "supportLevel", label: "Support Level" },
  { name: "contact", label: "Contact" },
];

const sortData = (data, sortBy, asc) => {
  if (!sortBy) return data;
  return [...data].sort((a, b) => {
    if (a[sortBy] < b[sortBy]) return asc ? -1 : 1;
    if (a[sortBy] > b[sortBy]) return asc ? 1 : -1;
    return 0;
  });
};

const kpiCards = [
  {
    key: 'open',
    title: 'OPEN TICKETS',
    value: 24,
    subtitle: 'Active support requests',
    icon: <FaTicketAlt style={{color:'#fbbf24',fontSize:32}} />,
    color: '#fbbf24',
  },
  {
    key: 'resolved',
    title: 'RESOLVED TODAY',
    value: 8,
    subtitle: <span><span style={{color:'#22c55e',fontWeight:600}}>+3</span> from yesterday</span>,
    icon: <FiCheckCircle style={{color:'#2dd4bf',fontSize:32}} />,
    color: '#2dd4bf',
  },
  {
    key: 'avgResponse',
    title: 'AVG. RESPONSE TIME',
    value: '2.5',
    subtitle: <span>hours</span>,
    icon: <FiClock style={{color:'#67e8f9',fontSize:32}} />,
    color: '#67e8f9',
  },
  {
    key: 'satisfaction',
    title: 'CUSTOMER SATISFACTION',
    value: '4.8',
    subtitle: <span>out of 5.0</span>,
    icon: <FiStar style={{color:'#2dd4bf',fontSize:32}} />,
    color: '#2dd4bf',
  },
];

const supportCategories = [
  {
    key: 'technical',
    title: 'Technical Issues',
    desc: 'Software bugs and technical problems',
    value: 12,
    icon: <FaCogs style={{color:'#2563eb',fontSize:32}} />,
    color: '#2563eb',
  },
  {
    key: 'install',
    title: 'Installation Support',
    desc: 'Help with software installation',
    value: 6,
    icon: <FiDownload style={{color:'#10b981',fontSize:32}} />,
    color: '#10b981',
  },
  {
    key: 'config',
    title: 'Configuration',
    desc: 'System setup and configuration',
    value: 4,
    icon: <FiSettings style={{color:'#f59e42',fontSize:32}} />,
    color: '#f59e42',
  },
  {
    key: 'training',
    title: 'Training & Documentation',
    desc: 'User training and documentation requests',
    value: 2,
    icon: <FiBookOpen style={{color:'#8b5cf6',fontSize:32}} />,
    color: '#8b5cf6',
  },
];

const WEEK_DAYS = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];
const DEFAULT_DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
const DEFAULT_CHANNELS = ['Email Support', 'Live Chat', 'Phone Support', 'Microsoft Teams'];
const ALL_CHANNELS = [
  'Email Support',
  'Live Chat',
  'Phone Support',
  'Microsoft Teams',
  'Google Meet'
];
const TIMEZONES = [
  'India Standard Time (IST)',
  'Eastern Standard Time (EST)',
  'Central European Time (CET)',
  'Pacific Standard Time (PST)'
];

function formatTime(val) {
  // Format as HH:MM AM/PM
  if (!val) return '';
  const [h, m] = val.split(':');
  let hour = parseInt(h, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12 || 12;
  return `${hour.toString().padStart(2, '0')}:${m} ${ampm}`;
}

const SupportConfiguration = () => {
  const [days, setDays] = React.useState(DEFAULT_DAYS);
  const [startTime, setStartTime] = React.useState('09:00');
  const [endTime, setEndTime] = React.useState('17:00');
  const [timezone, setTimezone] = React.useState(TIMEZONES[0]);
  const [channels, setChannels] = React.useState(DEFAULT_CHANNELS);
  const handleDayToggle = (day) => {
    setDays(prev => prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day]);
  };
  const handleChannelToggle = (ch) => {
    setChannels(prev => prev.includes(ch) ? prev.filter(c => c !== ch) : [...prev, ch]);
  };
  const handleSave = (e) => {
    e.preventDefault();
    // You can add save logic here
    alert('Configuration saved!');
  };
  return (
    <div className="support-config-section">
      <h2 className="support-config-title">Support Configuration</h2>
      <form onSubmit={handleSave} className="support-config-form">
        <div className="support-config-cards">
          {/* Days Support Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiCalendar style={{fontSize:22,color:'#0ea5e9'}} />
              <span className="support-config-card-title">Days Support</span>
            </div>
            <div className="support-config-card-desc">Select Support Days:</div>
            <div className="support-config-days-list">
              {WEEK_DAYS.map(day => (
                <label key={day} className="support-config-day-item">
                  <input type="checkbox" checked={days.includes(day)} onChange={() => handleDayToggle(day)} /> {day}
                </label>
              ))}
            </div>
          </div>
          {/* Support Hours Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiClock style={{fontSize:22,color:'#f59e42'}} />
              <span className="support-config-card-title">Support Hours</span>
            </div>
            <div className="support-config-hours-list">
              <label className="support-config-hours-label">
                Start Time:
                <input type="time" value={startTime} onChange={e => setStartTime(e.target.value)} className="support-config-hours-input" />
                <span className="support-config-hours-time">{formatTime(startTime)}</span>
              </label>
              <label className="support-config-hours-label">
                End Time:
                <input type="time" value={endTime} onChange={e => setEndTime(e.target.value)} className="support-config-hours-input" />
                <span className="support-config-hours-time">{formatTime(endTime)}</span>
              </label>
              <label className="support-config-hours-label">
                Timezone:
                <select value={timezone} onChange={e => setTimezone(e.target.value)} className="support-config-hours-select">
                  {TIMEZONES.map(tz => <option key={tz} value={tz}>{tz}</option>)}
                </select>
              </label>
            </div>
          </div>
          {/* Support Mode Card */}
          <div className="support-config-card">
            <div className="support-config-card-header">
              <FiHeadphones style={{fontSize:22,color:'#6366f1'}} />
              <span className="support-config-card-title">Support Mode</span>
            </div>
            <div className="support-config-card-desc">Available Support Channels:</div>
            <div className="support-config-mode-list">
              {ALL_CHANNELS.map(ch => (
                <label key={ch} className="support-config-mode-item">
                  <input type="checkbox" checked={channels.includes(ch)} onChange={() => handleChannelToggle(ch)} /> {ch}
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="support-config-save-btn-wrapper">
          <button type="submit" className="support-config-save-btn">
            <FiSave style={{fontSize:20}} /> Save Configuration
          </button>
        </div>
      </form>
    </div>
  );
};

const Support = () => {
  const [data, setData] = useState([]);
  const [filtered, setFiltered] = useState([]);
  const [search, setSearch] = useState("");
  const [sortBy, setSortBy] = useState("product");
  const [asc, setAsc] = useState(true);
  const [page, setPage] = useState(1);
  const [selected, setSelected] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [toast, setToast] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('list'); // 'grid' or 'list'

  useEffect(() => {
    setLoading(true);
    fetch(import.meta.env.BASE_URL + 'data/support.json')
      .then((res) => res.json())
      .then((d) => {
        setData(d.supports);
        setLoading(false);
      })
      .catch(() => {
        setError("Failed to load support data.");
        setLoading(false);
      });
  }, []);

  useEffect(() => {
    let d = [...data];
    if (search) {
      d = d.filter(
        c =>
          c.product.toLowerCase().includes(search.toLowerCase())
      );
    }
    d = sortData(d, sortBy, asc);
    setFiltered(d);
    setPage(1);
  }, [data, search, sortBy, asc]);

  const paged = filtered.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);
  const totalPages = Math.ceil(filtered.length / PAGE_SIZE);

  const handleSort = (field) => {
    if (sortBy === field) {
      setAsc(!asc);
    } else {
      setSortBy(field);
      setAsc(true);
    }
  }

  const handleAdd = (item) => {
    setData(prev => [...prev, { ...item, id: Date.now() }]);
    setToast({ type: 'success', message: 'Support detail added!' });
    closeModal();
  };
  const handleEdit = (item) => {
    setData(prev => prev.map(c => c.id === item.id ? item : c));
    setToast({ type: 'success', message: 'Support detail updated!' });
    closeModal();
  };
  const handleDelete = (id) => {
    setData(prev => prev.filter(c => c.id !== id));
    setToast({ type: 'error', message: 'Support detail deleted!' });
  };

  const openModal = (item = null) => {
    setSelected(item);
    setShowModal(true);
  };
  const closeModal = () => {
    setSelected(null);
    setShowModal(false);
  };

  const closeToast = () => setToast(null);

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="error">{error}</div>;

  return (
    <>
      <div className="view-header">
        <h1>Support Details</h1>
        <button onClick={() => openModal()} className="add-button">
          <FiPlus /> Add Support Detail
        </button>
      </div>
      {/* KPI Cards Row */}
      <div className="support-kpi-grid">
        {kpiCards.map(card => (
          <Card
            key={card.key}
            title={<span className="support-kpi-card-title">{card.icon} {card.title}</span>}
            className="kpi-card"
            style={{borderTop:`4px solid ${card.color}`}}
          >
            <div className="support-kpi-card-value">{card.value}</div>
            <div className="support-kpi-card-subtitle">{card.subtitle}</div>
          </Card>
        ))}
      </div>
      {/* Support Categories Cards */}
      <div className="support-category-section">
        <h2 className="support-category-title"><span role="img" aria-label="tag">🏷️</span> Support Categories</h2>
        <div className="support-category-grid">
          {supportCategories.map(cat => (
            <Card
              key={cat.key}
              title={<span className="support-category-card-title">{cat.icon} {cat.title}</span>}
              className="category-card"
              style={{minHeight:180}}
            >
              <div className="support-category-card-desc">{cat.desc}</div>
              <div className="support-category-card-value">{cat.value}</div>
            </Card>
          ))}
        </div>
      </div>
      {/* Support Configuration Section */}
      <SupportConfiguration />
      <div className="content-card">
        <div className="support-toolbar-flex">
          <div className="view-toggle">
            <button onClick={() => setView('grid')} className={view === 'grid' ? 'active' : ''}><FiGrid /></button>
            <button onClick={() => setView('list')} className={view === 'list' ? 'active' : ''}><FiList /></button>
          </div>
        </div>
        {view === 'list' ? (
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th># Ticket ID</th>
                  <th><span role="img" aria-label="customer">🏢</span> Customer</th>
                  <th><span role="img" aria-label="category">��️</span> Category</th>
                  <th><span role="img" aria-label="priority">ℹ️</span> Priority</th>
                  <th><span role="img" aria-label="status">ℹ️</span> Status</th>
                  <th><span role="img" aria-label="created">📅</span> Created</th>
                  <th><span role="img" aria-label="assigned">👤</span> Assigned To</th>
                  <th><span role="img" aria-label="actions">⚙️</span> Actions</th>
                </tr>
              </thead>
              <tbody>
                {paged.length > 0 ? paged.map((c, idx) => (
                  <tr key={c.id}>
                    <td>{c.ticketId || `TKT-${c.id || idx+1}`}</td>
                    <td>{c.customer || 'Demo Customer'}</td>
                    <td>{c.category || 'General'}</td>
                    <td>{c.priority || 'Medium'}</td>
                    <td>{c.status || 'Open'}</td>
                    <td>{c.created || '2024-01-01'}</td>
                    <td>{c.assignedTo || 'Support Agent'}</td>
                    <td className="action-buttons">
                      <button onClick={() => openModal(c)} aria-label="Edit"><FiEdit /></button>
                      <button onClick={() => handleDelete(c.id)} aria-label="Delete"><FiTrash2 /></button>
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan="8" className="support-table-empty">No support details found.</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="support-grid-view">
            {paged.map((c) => (
              <Card
                key={c.id}
                title={c.product}
                subtitle={`Level: ${c.supportLevel}`}
                status={null}
                icon={null}
                actions={
                  <>
                    <button onClick={() => openModal(c)} aria-label="Edit"><FiEdit /></button>
                    <button onClick={() => handleDelete(c.id)} aria-label="Delete"><FiTrash2 /></button>
                  </>
                }
              >
                <div className="support-card-contact"><b>Contact:</b> {c.contact}</div>
              </Card>
            ))}
          </div>
        )}
        <div className="pagination">
            <button onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1}>Prev</button>
            <span>Page {page} of {totalPages}</span>
            <button onClick={() => setPage(p => Math.min(totalPages, p + 1))} disabled={page === totalPages}>Next</button>
        </div>
      </div>

      {showModal && (
        <Modal
          fields={FIELDS}
          data={selected}
          onClose={closeModal}
          onSave={selected ? handleEdit : handleAdd}
        />
      )}
      {toast && <Toast type={toast.type} message={toast.message} onClose={closeToast} />}
    </>
  );
};

export default Support; 