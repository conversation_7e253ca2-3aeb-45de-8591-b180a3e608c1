import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Header from '../layout/Header';
import Footer from '../layout/Footer';
import Contact from '../sections/Contact';
import { cartData } from "../../data/cart";
import { industryIconList } from '../../data/industryIconMap';
import DynamicIcon from '../common/DynamicIcon';
import { MdChevronLeft, MdChevronRight } from 'react-icons/md';
// import './Cart.css';
import LanguageIcon from '@mui/icons-material/Language';
import SmartphoneIcon from '@mui/icons-material/Smartphone';
import BusinessCenterIcon from '@mui/icons-material/BusinessCenter';
import ExtensionIcon from '@mui/icons-material/Extension';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import ShoppingCartCheckoutIcon from '@mui/icons-material/ShoppingCartCheckout';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import DateRangeIcon from '@mui/icons-material/DateRange';
import EventRepeatIcon from '@mui/icons-material/EventRepeat';
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';
import FabMenu from '../common/FabMenu';
import AssistantFab from '../common/AssistantFab';

// Vite: Import all PNGs in the industry-icons folder as a map
const iconImages = import.meta.glob('../../assets/industry-icons/*.png', { eager: true, as: 'url' });

// Vite: Import social icons
const socialIcons = import.meta.glob('../../assets/social-icons/*.png', { eager: true, as: 'url' });

const Cart = ({ user, setUser, logout, cartCount, showAuthModal, setShowAuthModal, isDefaultUserLoggedIn, setIsDefaultUserLoggedIn }) => {
  const [cart, setCart] = useState(cartData);
  const [hoveredCard, setHoveredCard] = useState(null);
  const [subscriptionPlan, setSubscriptionPlan] = useState('Yearly');
  const [isCheckoutComplete, setIsCheckoutComplete] = useState(false);
  const [draggedIndustry, setDraggedIndustry] = useState(null);
  const [dragOverCard, setDragOverCard] = useState(null);
  const [industrySearch, setIndustrySearch] = useState('');
  const [cartSearch, setCartSearch] = useState('');
  const [activeCountFilter, setActiveCountFilter] = useState('Total');

  // Calculate cart count from cartData
  const actualCartCount = cartData.length;

  // Filter cart items based on search and active count filter
  const filteredCart = cart.filter(item => {
    const matchesSearch =
      item.productName.toLowerCase().includes(cartSearch.toLowerCase()) ||
      item.productType.toLowerCase().includes(cartSearch.toLowerCase()) ||
      item.selectedIndustries.some(industry =>
        industry.toLowerCase().includes(cartSearch.toLowerCase())
      );
    if (!matchesSearch) return false;
    if (activeCountFilter === 'Total') return true;
    if (activeCountFilter === 'Suites') return item.productType === 'Suite';
    if (activeCountFilter === 'Modules') return item.productType === 'Module';
    if (activeCountFilter === 'Mobile') return item.platforms.isMobile;
    if (activeCountFilter === 'Web') return item.platforms.isWeb;
    return true;
  });

  // Calculate counts
  const suitesCount = cart.filter(item => item.productType === 'Suite').length;
  const modulesCount = cart.filter(item => item.productType === 'Module').length;
  const mobileCount = cart.filter(item => item.platforms.isMobile).length;
  const webCount = cart.filter(item => item.platforms.isWeb).length;

  const currentItems = filteredCart;

  // Handle document-level drag end to ensure industries return to list
  useEffect(() => {
    const handleDocumentDragEnd = () => {
      if (draggedIndustry) {
        setDraggedIndustry(null);
        setDragOverCard(null);
      }
    };

    document.addEventListener('dragend', handleDocumentDragEnd);
    
    return () => {
      document.removeEventListener('dragend', handleDocumentDragEnd);
    };
  }, [draggedIndustry]);

  const subscriptionPlans = [
    { value: 'Monthly', label: 'Monthly', discount: '0%' },
    { value: 'Quarterly', label: 'Quarterly', discount: '5%' },
    { value: 'Half-Yearly', label: 'Half-Yearly', discount: '10%' },
    { value: 'Yearly', label: 'Yearly', discount: '20%' }
  ];

  // Filter industries based on search
  const filteredIndustries = industryIconList.map(i => i.name).filter(industry =>
    industry.toLowerCase().includes(industrySearch.toLowerCase())
  ).sort((a, b) => a.localeCompare(b));

  const handlePlatformToggle = (cartId, platform) => {
    setCart(prevCart => {
      return prevCart.map(item => {
        if (item.cartId !== cartId) return item;
        const current = item.platforms;
        const otherPlatform = platform === "isWeb" ? "isMobile" : "isWeb";
        if (current[platform] && !current[otherPlatform]) {
          if (window.confirm("Are you sure you want to remove this item from the cart?")) {
            return null;
          } else {
            return item;
          }
        }
        return {
          ...item,
          platforms: {
            ...current,
            [platform]: !current[platform]
          }
        };
      }).filter(Boolean);
    });
  };

  const handleDelete = cartId => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      setCart(prevCart => prevCart.filter(item => item.cartId !== cartId));
    }
  };

  const handleDragStart = (e, industry) => {
    setDraggedIndustry(industry);
    e.dataTransfer.effectAllowed = 'copy';
  };

  const handleDragOver = (e, cartId) => {
    e.preventDefault();
    setDragOverCard(cartId);
  };

  const handleDrop = (e, cartId) => {
    e.preventDefault();
    if (draggedIndustry) {
      setCart(prevCart => 
        prevCart.map(item => 
          item.cartId === cartId 
            ? { 
                ...item, 
                selectedIndustries: [...new Set([...item.selectedIndustries, draggedIndustry])]
              }
            : item
        )
      );
    }
    setDragOverCard(null);
    setDraggedIndustry(null);
  };

  const handleDragLeave = () => {
    setDragOverCard(null);
  };

  // Reset dragged industry when drag ends without dropping on a cart item
  const handleDragEnd = () => {
    setDraggedIndustry(null);
    setDragOverCard(null);
  };

  const removeIndustry = (cartId, industryToRemove) => {
    setCart(prevCart => 
      prevCart.map(item => 
        item.cartId === cartId 
          ? { 
              ...item, 
              selectedIndustries: item.selectedIndustries.filter(industry => industry !== industryToRemove)
            }
          : item
      )
    );
  };

  const handleCheckout = () => {
    setIsCheckoutComplete(true);
  };

  const getPlatformIcon = (platform) => {
    if (platform === "isWeb") {
      return socialIcons['../../assets/social-icons/Website.png'];
    } else {
      return socialIcons['../../assets/social-icons/Phone.png'];
    }
  };

  const getIndustryIcon = (industry) => {
    const entry = industryIconList.find(item => item.name === industry);
    const iconPath = entry ? `../../assets/industry-icons/${entry.icons}` : null;
    return iconPath && iconImages[iconPath] ? iconImages[iconPath] : null;
  };

  if (isCheckoutComplete) {
    return (
      <>
        <Header 
          user={user} 
          setUser={setUser} 
          logout={logout} 
          cartCount={actualCartCount} 
          showAuthModal={showAuthModal} 
          setShowAuthModal={setShowAuthModal} 
          cartPage={true}
          isDefaultUserLoggedIn={isDefaultUserLoggedIn}
          setIsDefaultUserLoggedIn={setIsDefaultUserLoggedIn}
        />
        <main className="cart-container">
          <div className="cart-success">
            <div className="cart-success-icon">✅</div>
            <h1 className="cart-success-title">Thank you for your purchase!</h1>
            <p className="cart-success-text">
              Our team will contact you soon to discuss your implementation plan and next steps.
            </p>
          </div>
        </main>
        <Contact />
        <Footer />
        <FabMenu />
        <AssistantFab />
      </>
    );
  }

  return (
    <>
      <Header 
        user={user} 
        setUser={setUser} 
        logout={logout} 
        cartCount={actualCartCount} 
        showAuthModal={showAuthModal} 
        setShowAuthModal={setShowAuthModal} 
        cartPage={true}
        isDefaultUserLoggedIn={isDefaultUserLoggedIn}
        setIsDefaultUserLoggedIn={setIsDefaultUserLoggedIn}
      />
      <main className="cart-container">
        <div className="cart-header">
          <h1 className="cart-title">Shopping Cart</h1>
          <p className="cart-subtitle">
            Manage your selected modules and suites
          </p>
        </div>

        <div className="cart-main-layout">
          {/* Industry Sidebar */}
          <div className="cart-sidebar">
            <h3 className="cart-sidebar-title">Available Industries</h3>
            <p className="cart-sidebar-subtitle">
              Drag industries to add them to your cart items
            </p>
            
            <div className="cart-sidebar-search">
              <input
                type="text"
                placeholder="Search industries..."
                value={industrySearch}
                onChange={(e) => setIndustrySearch(e.target.value)}
              />
            </div>

            <div className="cart-industries-list">
              {filteredIndustries.map(industry => (
                <div
                  key={industry}
                  className={`cart-industry-item ${draggedIndustry === industry ? 'dragging' : ''}`}
                  draggable
                  onDragStart={(e) => handleDragStart(e, industry)}
                  onDragEnd={handleDragEnd}
                >
                  {getIndustryIcon(industry) && (
                    <div 
                      className="cart-industry-icon"
                      style={{ '--industry-icon-url': `url(${getIndustryIcon(industry)})` }}
                    />
                  )}
                  {industry}
                </div>
              ))}
            </div>
          </div>

          {/* Cart Grid */}
          <div className="cart-grid-container">
            {cart.length === 0 ? (
              <div className="cart-empty">
                <div className="cart-empty-icon"><ShoppingCartIcon className="cart-empty-icon-svg" /></div>
                <div className="cart-empty-text">Your cart is empty</div>
                <div className="cart-empty-subtext">
                  Add some modules or suites to get started
                </div>
              </div>
            ) : (
              <>
                {/* View Toggle Controls */}
                <div className="cart-view-controls">
                  <div className="cart-search-section">
                    <div className="cart-search-container">
                      <input
                        type="text"
                        placeholder="Search cart items..."
                        value={cartSearch}
                        onChange={(e) => setCartSearch(e.target.value)}
                        className="cart-search-input"
                      />
                      {cartSearch && (
                        <button
                          className="cart-search-clear"
                          onClick={() => setCartSearch('')}
                          title="Clear search"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="cart-counts-section">
                    <div
                      className={`cart-count-item cart-count-item-pointer${activeCountFilter === 'Suites' ? ' active' : ''}`}
                      onClick={() => setActiveCountFilter(activeCountFilter === 'Suites' ? 'Total' : 'Suites')}>
                      <span className="cart-count-icon"><BusinessCenterIcon fontSize="small" /></span>
                      <span className="cart-count-label">Suites:</span>
                      <span className="cart-count-value">{suitesCount}</span>
                    </div>
                    <div
                      className={`cart-count-item cart-count-item-pointer${activeCountFilter === 'Modules' ? ' active' : ''}`}
                      onClick={() => setActiveCountFilter(activeCountFilter === 'Modules' ? 'Total' : 'Modules')}>
                      <span className="cart-count-icon"><ExtensionIcon fontSize="small" /></span>
                      <span className="cart-count-label">Modules:</span>
                      <span className="cart-count-value">{modulesCount}</span>
                    </div>
                    <div
                      className={`cart-count-item cart-count-item-pointer${activeCountFilter === 'Mobile' ? ' active' : ''}`}
                      onClick={() => setActiveCountFilter(activeCountFilter === 'Mobile' ? 'Total' : 'Mobile')}>
                      <span className="cart-count-icon"><SmartphoneIcon fontSize="small" /></span>
                      <span className="cart-count-label">Mobile:</span>
                      <span className="cart-count-value">{mobileCount}</span>
                    </div>
                    <div
                      className={`cart-count-item cart-count-item-pointer${activeCountFilter === 'Web' ? ' active' : ''}`}
                      onClick={() => setActiveCountFilter(activeCountFilter === 'Web' ? 'Total' : 'Web')}>
                      <span className="cart-count-icon"><LanguageIcon fontSize="small" /></span>
                      <span className="cart-count-label">Web:</span>
                      <span className="cart-count-value">{webCount}</span>
                    </div>
                    <div
                      className={`cart-count-item cart-count-item-pointer total${activeCountFilter === 'Total' ? ' active' : ''}`}
                      onClick={() => setActiveCountFilter('Total')}>
                      <span className="cart-count-icon"><ShoppingCartIcon fontSize="small" /></span>
                      <span className="cart-count-label">Total:</span>
                      <span className="cart-count-value">{cart.length}</span>
                    </div>
                  </div>
                  <div className="cart-add-new-section">
                    <Link 
                      to="/"
                      className={`cart-add-new-btn ${hoveredCard === 'addBtn' ? 'hover' : ''}`}
                      onMouseEnter={() => setHoveredCard('addBtn')}
                      onMouseLeave={() => setHoveredCard(null)}
                      onClick={() => {
                        // Navigate to main page and switch to suites tab
                        setTimeout(() => {
                          const el = document.getElementById('solutions');
                          if (el) el.scrollIntoView({ behavior: 'smooth' });
                          window.dispatchEvent(new CustomEvent('solutionsTabSwitch', { detail: { tab: 'suites' } }));
                        }, 100);
                      }}
                    >
                      <AddCircleOutlineIcon fontSize="small" className="cart-add-new-icon" /> Add New Item
                    </Link>
                  </div>
                </div>

                {/* Card View */}
                <div className="cart-cards-view">
                  {currentItems.map(item => (
                    <div 
                      key={item.cartId} 
                      className={`cart-card ${hoveredCard === item.cartId ? 'hover' : ''}`}
                      onMouseEnter={() => setHoveredCard(item.cartId)}
                      onMouseLeave={() => setHoveredCard(null)}
                      onDragOver={(e) => handleDragOver(e, item.cartId)}
                      onDrop={(e) => handleDrop(e, item.cartId)}
                      onDragLeave={handleDragLeave}
                      onDragEnd={handleDragEnd}
                    >
                      <div className="cart-card-header">
                        <div className="cart-card-icon-container">
                          <DynamicIcon name={item.icon} />
                        </div>
                        <div className="cart-card-title-container">
                          <div className="cart-card-title">{item.productName}</div>
                          <div className="cart-card-type">{item.productType}</div>
                        </div>
                        <button
                          className="cart-delete-icon-btn"
                          onClick={() => handleDelete(item.cartId)}
                          title="Delete Item"
                          type="button"
                        >
                          <DeleteOutlineIcon className="cart-delete-icon" fontSize="medium" />
                        </button>
                      </div>
                      
                      <div className="cart-platforms-section">
                        <div className="cart-platforms-title">Platforms:</div>
                        <div className="cart-platforms-group">
                          <button
                            type="button"
                            className={`cart-platform-toggle${item.platforms.isWeb ? ' active' : ''}`}
                            onClick={() => handlePlatformToggle(item.cartId, "isWeb")}
                          >
                            <LanguageIcon className="cart-platform-icon" fontSize="small" />
                            <span className="cart-platform-label">Web</span>
                          </button>
                          <button
                            type="button"
                            className={`cart-platform-toggle${item.platforms.isMobile ? ' active' : ''}`}
                            onClick={() => handlePlatformToggle(item.cartId, "isMobile")}
                          >
                            <SmartphoneIcon className="cart-platform-icon" fontSize="small" />
                            <span className="cart-platform-label">Mobile</span>
                          </button>
                        </div>
                      </div>

                      <div className="cart-industries-section">
                        <div className="cart-industries-title">Selected Industries:</div>
                        <div 
                          className={`cart-drop-zone ${dragOverCard === item.cartId ? 'active' : ''}`}
                        >
                          {item.selectedIndustries.length === 0 ? (
                            <div className="cart-drop-zone-empty">
                              Drop industries here
                            </div>
                          ) : (
                            <div className="cart-selected-industries">
                              {item.selectedIndustries.map(industry => (
                                <span key={industry} className="cart-industry-tag">
                                  {industry}
                                  <span
                                    className="cart-remove-industry"
                                    onClick={() => removeIndustry(item.cartId, industry)}
                                  >
                                    ×
                                  </span>
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
        {/* Move cart-details-row here, outside cart-main-layout for full width */}
        <div className="cart-details-row">
          <div className="cart-subscription-section">
            <Paper elevation={3} className="cart-checkout-paper compact-flex">
              <div className="cart-subscription-content">
                <h3 className="cart-subscription-title">Select Subscription Plan</h3>
                <div className="cart-subscription-options">
                  {subscriptionPlans.map(plan => {
                    let icon = null;
                    if (plan.value === 'Monthly') icon = <CalendarMonthIcon sx={{mr:1}} fontSize="small" />;
                    if (plan.value === 'Quarterly') icon = <AutorenewIcon sx={{mr:1}} fontSize="small" />;
                    if (plan.value === 'Half-Yearly') icon = <DateRangeIcon sx={{mr:1}} fontSize="small" />;
                    if (plan.value === 'Yearly') icon = <EventRepeatIcon sx={{mr:1}} fontSize="small" />;
                    return (
                      <label
                        key={plan.value}
                        className={`cart-subscription-option ${subscriptionPlan === plan.value ? 'selected' : ''}`}
                      >
                        <input
                          type="radio"
                          name="subscription"
                          value={plan.value}
                          checked={subscriptionPlan === plan.value}
                          onChange={(e) => setSubscriptionPlan(e.target.value)}
                          className="cart-radio"
                        />
                        <div className="cart-subscription-icon-wrap">
                          {icon}
                          <div>
                            <div className="cart-subscription-label">{plan.label}</div>
                            <div className="cart-subscription-discount">
                              Save {plan.discount}
                            </div>
                          </div>
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>
            </Paper>
          </div>
          <div className="cart-checkout-section">
            <Paper elevation={3} className="cart-checkout-paper compact-flex">
              <div className="cart-checkout-left">
                <div className="cart-checkout-summary">
                  <h3 className="cart-checkout-title">Checkout Summary</h3>
                  <div><strong>Selected Plan:</strong> {subscriptionPlan}</div>
                  <div><strong>Total Items:</strong> {filteredCart.length}</div>
                  <div className="cart-checkout-subtext">Secure checkout. All data is encrypted.</div>
                </div>
              </div>
              <div className="cart-checkout-right">
                <Button 
                  variant="contained"
                  color="primary"
                  size="large"
                  sx={{ borderRadius: 2, fontWeight: 700, fontSize: '1.08rem', py: 1.5 }}
                  className={`cart-checkout-btn-mui ${hoveredCard === 'checkoutBtn' ? 'hover' : ''}`}
                  onClick={handleCheckout}
                  onMouseEnter={() => setHoveredCard('checkoutBtn')}
                  onMouseLeave={() => setHoveredCard(null)}
                  startIcon={<ShoppingCartCheckoutIcon />}
                >
                  Checkout
                </Button>
              </div>
            </Paper>
          </div>
        </div>
      </main>
      <Contact />
      <Footer />
      <FabMenu />
      <AssistantFab />
    </>
  );
};

export default Cart; 