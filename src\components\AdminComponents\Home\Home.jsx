import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FiHome, FiUsers, FiFileText, FiDollarSign, FiShield, FiShoppingCart, FiAward, FiHeadphones, FiBriefcase, FiFile, FiTruck, FiServer, FiDatabase } from 'react-icons/fi';
import { FaPlane } from 'react-icons/fa';
import Card from '../shared/Card';

const Home = () => {
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState({
    customers: { total: 0, active: 0, inactive: 0 },
    contracts: { total: 0, active: 0, expiring: 0, expired: 0 },
    billing: { total: 0, monthly: 0 },
    support: { total: 0 },
    licenses: { total: 0, active: 0 },
    travel: { total: 0, approved: 0, pending: 0 },
    purchases: { total: 0, completed: 0, pending: 0 },
    professional: { total: 0, completed: 0, inProgress: 0 },
    sla: { total: 0 },
    server: { total: 0 },
    branches: { total: 0 },
    warehouse: { total: 0 }
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch all data files
        const dataPromises = [
          fetch('/data/customer.json').then(res => res.json()),
          fetch('/data/contract.json').then(res => res.json()),
          fetch('/data/billing.json').then(res => res.json()),
          fetch('/data/support.json').then(res => res.json()),
          fetch('/data/license.json').then(res => res.json()),
          fetch('/data/travel.json').then(res => res.json()),
          fetch('/data/purchase.json').then(res => res.json()),
          fetch('/data/professional.json').then(res => res.json()),
          fetch('/data/sla.json').then(res => res.json()),
          fetch('/data/server.json').then(res => res.json()),
          fetch('/data/Branch.json').then(res => res.json()).catch(() => ({ branches: [] })),
          fetch('/data/Warehouse.json').then(res => res.json()).catch(() => ({ warehouses: [] }))
        ];

        const [
          customerData,
          contractData,
          billingData,
          supportData,
          licenseData,
          travelData,
          purchaseData,
          professionalData,
          slaData,
          serverData,
          branchData,
          warehouseData
        ] = await Promise.all(dataPromises);

        // Process customer data
        const customers = customerData.customers || [];
        const activeCustomers = customers.filter(c => c.status === 'Active').length;

        // Process contract data
        const contracts = contractData.contracts || [];
        const activeContracts = contracts.filter(c => c.status === 'Active').length;
        const expiringContracts = contracts.filter(c => c.status === 'Expiring Soon').length;
        const expiredContracts = contracts.filter(c => c.status === 'Expired').length;

        // Process travel data
        const travels = travelData.travels || [];
        const approvedTravels = travels.filter(t => t.status === 'Approved').length;
        const pendingTravels = travels.filter(t => t.status === 'Pending').length;

        // Process purchase data
        const purchases = purchaseData.purchases || [];
        const completedPurchases = purchases.filter(p => p.status === 'Completed').length;
        const pendingPurchases = purchases.filter(p => p.status === 'Pending').length;

        // Process professional services data
        const services = professionalData.services || [];
        const completedServices = services.filter(s => s.status === 'Completed').length;
        const inProgressServices = services.filter(s => s.status === 'In Progress').length;

        // Process license data
        const licenses = licenseData.licenses || [];
        const activeLicenses = licenses.filter(l => l.status === 'Active').length;

        setDashboardData({
          customers: { total: customers.length, active: activeCustomers, inactive: customers.length - activeCustomers },
          contracts: { total: contracts.length, active: activeContracts, expiring: expiringContracts, expired: expiredContracts },
          billing: { total: billingData.billingCycles?.length || 0, monthly: billingData.billingCycles?.length || 0 },
          support: { total: supportData.supports?.length || 0 },
          licenses: { total: licenses.length, active: activeLicenses },
          travel: { total: travels.length, approved: approvedTravels, pending: pendingTravels },
          purchases: { total: purchases.length, completed: completedPurchases, pending: pendingPurchases },
          professional: { total: services.length, completed: completedServices, inProgress: inProgressServices },
          sla: { total: slaData.slas?.length || 0 },
          server: { total: serverData.servers?.length || 0 },
          branches: { total: branchData.branches?.length || 0 },
          warehouse: { total: warehouseData.warehouses?.length || 0 }
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      }
    };

    fetchDashboardData();
  }, []);

  // Simple chart-like visualization using CSS
  const ProgressBar = ({ percentage, color, className }) => (
    <div className={`progress-bar-container ${className || ''}`}>
      <div 
        className="progress-bar-fill"
        style={{ width: `${percentage}%`, backgroundColor: color }}
      />
    </div>
  );

  const MiniChart = ({ data, labels, colors, className }) => (
    <div className={`mini-chart ${className || ''}`}>
      {data.map((value, index) => (
        <div key={index} className="mini-chart-bar-wrapper">
          <div 
            className="mini-chart-bar"
            style={{ 
              height: `${(value / Math.max(...data)) * 50}px`,
              backgroundColor: colors[index]
            }} 
          />
          <div className="mini-chart-label">
            {labels[index]}
          </div>
        </div>
      ))}
    </div>
  );

  // Card click handlers for navigation
  const handleCardClick = (path) => () => navigate(`/admin${path.startsWith('/') ? path : '/' + path}`);

  return (
    <div className="content-card home-dashboard">
      <div className="view-header">
        <h1><FiHome /> Dashboard</h1>
      </div>
      <div className="dashboard-spacer" />
      {/* Key Metrics Cards */}
      <div className="dashboard-metrics-grid">
        <div className="dashboard-card-link" onClick={handleCardClick('/customers')}>
          <Card
            title="Total Customers"
            subtitle={`${dashboardData.customers.active} Active`}
            icon={<FiUsers />}
            status={`${dashboardData.customers.total} Total`}
          >
            <div className="dashboard-metric-value dashboard-customers-value">
              {dashboardData.customers.total}
            </div>
            <ProgressBar percentage={(dashboardData.customers.active / dashboardData.customers.total) * 100} color="#10b981" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/contract')}>
          <Card
            title="Active Contracts"
            subtitle={`${dashboardData.contracts.expiring} Expiring Soon`}
            icon={<FiFileText />}
            status={`${dashboardData.contracts.total} Total`}
          >
            <div className="dashboard-metric-value dashboard-contracts-value">
              {dashboardData.contracts.active}
            </div>
            <ProgressBar percentage={(dashboardData.contracts.active / dashboardData.contracts.total) * 100} color="#10b981" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/billing')}>
          <Card
            title="Total Revenue"
            subtitle="This Month"
            icon={<FiDollarSign />}
            status="Active"
          >
            <div className="dashboard-metric-value dashboard-revenue-value">
              $2.4M
            </div>
            <div className="dashboard-revenue-change">
              ↗ +12% from last month
            </div>
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/license')}>
          <Card
            title="Active Licenses"
            subtitle={`${dashboardData.licenses.total} Total`}
            icon={<FiFile />}
            status="Valid"
          >
            <div className="dashboard-metric-value dashboard-licenses-value">
              {dashboardData.licenses.active}
            </div>
            <ProgressBar percentage={(dashboardData.licenses.active / dashboardData.licenses.total) * 100} color="#7c3aed" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/travel')}>
          <Card
            title="Travel Requests"
            subtitle={`${dashboardData.travel.pending} Pending`}
            icon={<FaPlane />}
            status={`${dashboardData.travel.total} Total`}
          >
            <div className="dashboard-metric-value dashboard-travel-value">
              {dashboardData.travel.approved}
            </div>
            <ProgressBar percentage={(dashboardData.travel.approved / dashboardData.travel.total) * 100} color="#f59e0b" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/purchase')}>
          <Card
            title="Purchase Orders"
            subtitle={`${dashboardData.purchases.pending} Pending`}
            icon={<FiShoppingCart />}
            status={`${dashboardData.purchases.total} Total`}
          >
            <div className="dashboard-metric-value dashboard-purchase-value">
              {dashboardData.purchases.completed}
            </div>
            <ProgressBar percentage={(dashboardData.purchases.completed / dashboardData.purchases.total) * 100} color="#dc2626" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/professional')}>
          <Card
            title="Professional Services"
            subtitle={`${dashboardData.professional.inProgress} In Progress`}
            icon={<FiBriefcase />}
            status={`${dashboardData.professional.total} Total`}
          >
            <div className="dashboard-metric-value dashboard-professional-value">
              {dashboardData.professional.completed}
            </div>
            <ProgressBar percentage={(dashboardData.professional.completed / dashboardData.professional.total) * 100} color="#0891b2" />
          </Card>
        </div>

        <div className="dashboard-card-link" onClick={handleCardClick('/support')}>
          <Card
            title="Support Tickets"
            subtitle="24/7 Support"
            icon={<FiHeadphones />}
            status={`${dashboardData.support.total} Active`}
          >
            <div className="dashboard-metric-value dashboard-support-value">
              {dashboardData.support.total}
            </div>
            <div className="dashboard-support-response">
              Average response: 2.3h
            </div>
          </Card>
        </div>
      </div>

      {/* Charts Section */}
      <div className="dashboard-charts-grid">
        {/* Customer Status Chart */}
        <Card title="Customer Status Distribution" icon={<FiUsers />}>
          <div className="dashboard-status-row">
            <div className="dashboard-status-item">
              <div className="dashboard-status-dot dashboard-status-dot-active"></div>
              <span className="dashboard-status-label">Active: {dashboardData.customers.active}</span>
            </div>
            <div className="dashboard-status-item">
              <div className="dashboard-status-dot dashboard-status-dot-inactive"></div>
              <span className="dashboard-status-label">Inactive: {dashboardData.customers.inactive}</span>
            </div>
          </div>
          <MiniChart 
            data={[dashboardData.customers.active, dashboardData.customers.inactive]} 
            labels={['Active', 'Inactive']} 
            colors={['#10b981', '#ef4444']} 
          />
        </Card>

        {/* Contract Status Chart */}
        <Card title="Contract Status Overview" icon={<FiFileText />}>
          <div className="dashboard-status-row">
            <div className="dashboard-status-item">
              <div className="dashboard-status-dot dashboard-status-dot-active"></div>
              <span className="dashboard-status-label">Active: {dashboardData.contracts.active}</span>
            </div>
            <div className="dashboard-status-item">
              <div className="dashboard-status-dot dashboard-status-dot-expiring"></div>
              <span className="dashboard-status-label">Expiring: {dashboardData.contracts.expiring}</span>
            </div>
            <div className="dashboard-status-item">
              <div className="dashboard-status-dot dashboard-status-dot-expired"></div>
              <span className="dashboard-status-label">Expired: {dashboardData.contracts.expired}</span>
            </div>
          </div>
          <MiniChart 
            data={[dashboardData.contracts.active, dashboardData.contracts.expiring, dashboardData.contracts.expired]} 
            labels={['Active', 'Expiring', 'Expired']} 
            colors={['#10b981', '#f59e0b', '#ef4444']} 
          />
        </Card>

        {/* Revenue Trend Chart */}
        <Card title="Revenue Trend (Last 6 Months)" icon={<FiDollarSign />}>
          <div className="dashboard-revenue-row">
            <div className="dashboard-revenue-total">$8.1M</div>
            <div className="dashboard-revenue-yoy">↗ +15% YoY</div>
          </div>
          <MiniChart 
            data={[1200000, 1350000, 1100000, 1450000, 1300000, 1400000]} 
            labels={['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']} 
            colors={['#0e7490', '#0e7490', '#0e7490', '#0e7490', '#0e7490', '#0e7490']} 
          />
        </Card>

        {/* Additional Metrics */}
        <Card title="System Overview" icon={<FiServer />}>
          <div className="dashboard-system-grid">
            <div className="dashboard-system-item">
              <div className="dashboard-system-value">{dashboardData.sla.total}</div>
              <div className="dashboard-system-label">SLA Agreements</div>
            </div>
            <div className="dashboard-system-item">
              <div className="dashboard-system-value">{dashboardData.server.total}</div>
              <div className="dashboard-system-label">Servers</div>
            </div>
            <div className="dashboard-system-item">
              <div className="dashboard-system-value">{dashboardData.branches.total}</div>
              <div className="dashboard-system-label">Branches</div>
            </div>
            <div className="dashboard-system-item">
              <div className="dashboard-system-value">{dashboardData.warehouse.total}</div>
              <div className="dashboard-system-label">Warehouses</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card title="Quick Actions" icon={<FiDatabase />}>
        <div className="dashboard-actions-grid">
          <button className="dashboard-action-btn dashboard-action-btn-customer">
            Add New Customer
          </button>
          <button className="dashboard-action-btn dashboard-action-btn-contract">
            Create Contract
          </button>
          <button className="dashboard-action-btn dashboard-action-btn-travel">
            Schedule Travel
          </button>
          <button className="dashboard-action-btn dashboard-action-btn-report">
            Generate Report
          </button>
        </div>
      </Card>
    </div>
  );
};

export default Home; 