import React from 'react';
import { faqData } from '../../data/faq';
import FaqItem from './FaqItem';
// import './Faq.css';

const Faq = () => {
    return (
        <section id="faq" className="faq-section">
            <div className="container">
                <div className="section-header">
                    <h2 className="section-title">Frequently Asked Questions</h2>
                    <p className="section-subtitle">
                        Have questions? We've got answers. If you can't find what you're looking for, feel free to contact us.
                    </p>
                </div>
                <div className="faq-container">
                    {faqData.map((faq, index) => (
                        <FaqItem key={index} faq={faq} />
                    ))}
                </div>
            </div>
        </section>
    );
};

export default Faq; 