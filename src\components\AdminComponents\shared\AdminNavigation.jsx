import React, { useState, useMemo, useEffect, useRef } from 'react';
import { NavLink } from 'react-router-dom';
import { Tooltip, Box, TextField, InputAdornment, Button, Menu, MenuItem, Grid, Typography, Modal, Paper, List, ListItem, ListItemText, ListItemIcon, ListSubheader, Divider, Alert } from '@mui/material';
import {
    HomeOutlined,
    PersonAddOutlined,
    PeopleOutline,
    DescriptionOutlined,
    ShoppingCartOutlined,
    SyncOutlined,
    BusinessCenterOutlined,
    CalendarTodayOutlined,
    DnsOutlined,
    HandshakeOutlined,
    FlightOutlined,
    HeadsetMicOutlined,
    SettingsOutlined,
    Search,
    KeyboardArrowDown,
    DragIndicator,
    SaveOutlined,
    Replay,
} from '@mui/icons-material';
import { AdminComponents } from '../../../styles/theme';


const navItems = [
    { id: 'dashboard', name: 'Dashboard', path: '/admin', icon: <HomeOutlined /> },
    { id: 'onboarding', name: 'On Boarding', path: '/admin/onboarding', icon: <PersonAddOutlined /> },
    { id: 'customers', name: 'Customers', path: '/admin/customers', icon: <PeopleOutline /> },
    { id: 'contracts', name: 'Contract Management', path: '/admin/contract', icon: <DescriptionOutlined /> },
    { id: 'quotation', name: 'Quotation', path: '/admin/Quotation', icon: <DescriptionOutlined /> },
    { id: 'purchase_orders', name: 'Purchase Orders', path: '/admin/purchase', icon: <ShoppingCartOutlined /> },
    { id: 'purchase_invoice', name: 'Purchase Invoice', path: '/admin/invoice', icon: <DescriptionOutlined /> },
    { id: 'renewals', name: 'Renewals', path: '/admin/renewals', icon: <SyncOutlined /> },
    { id: 'services', name: 'Professional Services', path: '/admin/professional', icon: <BusinessCenterOutlined /> },
    { id: 'license', name: 'Licensce Management', path: '/admin/license', icon: <DescriptionOutlined /> },
    { id: 'user', name: 'User', path: '/admin/user', icon: <PeopleOutline /> },
    { id: 'billing', name: 'Billing Cycle', path: '/admin/billing', icon: <CalendarTodayOutlined /> },
    { id: 'server', name: 'Server Maintenance', path: '/admin/server', icon: <DnsOutlined /> },
    { id: 'sla', name: 'SLA', path: '/admin/sla', icon: <HandshakeOutlined /> },
    { id: 'branches', name: 'Branches', path: '/admin/branches', icon: <PeopleOutline /> },
    { id: 'travel', name: 'Travel Details', path: '/admin/travel', icon: <FlightOutlined /> },
    { id: 'support', name: 'Product Support', path: '/admin/support', icon: <HeadsetMicOutlined /> },
    { id: 'admin_panel', name: 'Admin Panel', path: '/admin/panel', icon: <SettingsOutlined /> },
];

const menuCategories = {
    "Client Services": ['renewals', 'services', 'license', 'sla', 'support'],
    "Administration": ['user', 'billing', 'server', 'branches', 'admin_panel'],
    "Operations": ['travel'],
};

const DraggableListItem = ({ item, ...props }) => (
    <AdminComponents.DraggableListItemPaper component={ListItem} {...props}>
        <AdminComponents.DragIndicatorIcon><DragIndicator /></AdminComponents.DragIndicatorIcon>
        <AdminComponents.ListItemIconStyled>{item.icon}</AdminComponents.ListItemIconStyled>
        <ListItemText primary={item.name} />
    </AdminComponents.DraggableListItemPaper>
);


const ConfigureMenuModal = ({ open, onClose, navConfig, onSave, limit, defaultConfig }) => {
    const [primary, setPrimary] = useState([]);
    const [secondary, setSecondary] = useState([]);
    const [error, setError] = useState(''); 
    const draggedItemRef = useRef(null);
    const dragOverItemRef = useRef(null);
    const errorTimeoutRef = useRef(null);

    useEffect(() => {
        if (open) {
            setPrimary(navConfig.primary);
            setSecondary(navConfig.secondary);
            setError(''); 
        }
    }, [open, navConfig]);

    useEffect(() => {
        return () => clearTimeout(errorTimeoutRef.current);
    }, []);

    const handleDragStart = (e, position, list) => {
        draggedItemRef.current = { item: list === 'primary' ? primary[position] : secondary[position], position, list };
    };

    const handleDragEnter = (e, position, list) => {
        dragOverItemRef.current = { position, list };
    };
    
    const handleDrop = () => {
        if (!draggedItemRef.current || !dragOverItemRef.current) return;
        
        const source = draggedItemRef.current;
        const target = dragOverItemRef.current;
        
        if (source.list !== 'primary' && target.list === 'primary' && primary.length >= limit) {
            setError(`Maximum of ${limit} visible items reached. Please remove an item first.`);
            clearTimeout(errorTimeoutRef.current);
            errorTimeoutRef.current = setTimeout(() => setError(''), 3000);
            return;
        }

        let newPrimary = [...primary];
        let newSecondary = [...secondary];
        
        if (source.list === 'primary') {
            newPrimary.splice(source.position, 1);
        } else {
            newSecondary.splice(source.position, 1);
        }

        if (target.list === 'primary') {
            newPrimary.splice(target.position, 0, source.item);
        } else {
            newSecondary.splice(target.position, 0, source.item);
        }
        
        setPrimary(newPrimary);
        setSecondary(newSecondary);
        setError(''); 
        
        draggedItemRef.current = null;
        dragOverItemRef.current = null;
    };

    const handleSave = () => {
        onSave({ primary, secondary });
        onClose();
    };

    const handleReset = () => {
        setPrimary(defaultConfig.primary);
        setSecondary(defaultConfig.secondary);
        setError('');
    };

    return (
        <Modal open={open} onClose={onClose}>
            <AdminComponents.ConfigureMenuModalPaper>
                <AdminComponents.ConfigureMenuModalTitle variant="h5">Configure Top Navigation</AdminComponents.ConfigureMenuModalTitle>
                
                {error && <AdminComponents.ModalAlert severity="warning">{error}</AdminComponents.ModalAlert>}

                <AdminComponents.ConfigureMenuModalSubtitle variant="body2" color="text.secondary">
                    Drag items between lists. You can have a maximum of **{limit}** items visible on the top bar.
                </AdminComponents.ConfigureMenuModalSubtitle>
                
                {/* FIX: Removed 'container' prop from Grid */}
                <Grid spacing={2} onDrop={handleDrop} onDragOver={(e) => e.preventDefault()} sx={{ display: 'flex' }}>
                    <Grid xs={6}>
                        <AdminComponents.ConfigureMenuModalListTitle variant="subtitle2">Visible on Top Bar ({primary.length}/{limit})</AdminComponents.ConfigureMenuModalListTitle>
                        <AdminComponents.ConfigureMenuModalListPaper 
                            variant="outlined"
                            onDragEnter={() => dragOverItemRef.current = { position: primary.length, list: 'primary' }}
                        >
                            <List>
                                {primary.map((item, index) => (
                                    <Box key={item.id} onDragEnter={(e) => handleDragEnter(e, index, 'primary')}>
                                        <DraggableListItem 
                                            item={item} 
                                            draggable 
                                            onDragStart={(e) => handleDragStart(e, index, 'primary')} 
                                        />
                                    </Box>
                                ))}
                            </List>
                        </AdminComponents.ConfigureMenuModalListPaper>
                    </Grid>
                    <Grid xs={6}>
                        <AdminComponents.ConfigureMenuModalListTitle variant="subtitle2">Hidden in "More" Menu</AdminComponents.ConfigureMenuModalListTitle>
                         <AdminComponents.ConfigureMenuModalListPaper 
                            variant="outlined"
                            onDragEnter={() => dragOverItemRef.current = { position: secondary.length, list: 'secondary' }}
                         >
                            <List>
                                {secondary.map((item, index) => (
                                     <Box key={item.id} onDragEnter={(e) => handleDragEnter(e, index, 'secondary')}>
                                        <DraggableListItem 
                                            item={item} 
                                            draggable 
                                            onDragStart={(e) => handleDragStart(e, index, 'secondary')} 
                                        />
                                    </Box>
                                ))}
                            </List>
                        </AdminComponents.ConfigureMenuModalListPaper>
                    </Grid>
                </Grid>
                <AdminComponents.ConfigureMenuModalActions>
                    <Button variant="outlined" color="secondary" onClick={handleReset} startIcon={<Replay />}>Reset to Default</Button>
                    <Box sx={{flex: 1}} />
                    <Button variant="text" onClick={onClose}>Cancel</Button>
                    <Button variant="contained" onClick={handleSave} startIcon={<SaveOutlined />}>Save Configuration</Button>
                </AdminComponents.ConfigureMenuModalActions>
            </AdminComponents.ConfigureMenuModalPaper>
        </Modal>
    );
};


// --- Main Navigation Component ---
const AdminNavigation = ({ mode }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [anchorEl, setAnchorEl] = useState(null);
    const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
    
    const VISIBLE_ITEMS_LIMIT = 8;
    const DEFAULT_CONFIG = useMemo(() => ({ 
        primary: navItems.slice(0, VISIBLE_ITEMS_LIMIT), 
        secondary: navItems.slice(VISIBLE_ITEMS_LIMIT) 
    }), []);

    const [navConfig, setNavConfig] = useState(DEFAULT_CONFIG);

    useEffect(() => {
        try {
            const savedConfig = localStorage.getItem('adminNavConfig');
            if (!savedConfig) return;

            const parsedIds = JSON.parse(savedConfig);
            if (!parsedIds.primary || !parsedIds.secondary) throw new Error("Invalid config format");

            const navItemsMap = new Map(navItems.map(item => [item.id, item]));
            
            const primary = parsedIds.primary.map(id => navItemsMap.get(id)).filter(Boolean);
            const secondary = parsedIds.secondary.map(id => navItemsMap.get(id)).filter(Boolean);
            
            const allSavedIds = new Set([...parsedIds.primary, ...parsedIds.secondary]);
            const newItems = navItems.filter(item => !allSavedIds.has(item.id));

            const finalSecondary = [...secondary, ...newItems];

            setNavConfig({ primary, secondary: finalSecondary });

        } catch (error) {
            console.error("Failed to load or merge nav config, resetting to default.", error);
            localStorage.removeItem('adminNavConfig');
            setNavConfig(DEFAULT_CONFIG);
        }
    }, [DEFAULT_CONFIG]);


    const handleSaveNavConfig = (newConfig) => {
        const configToSave = {
            primary: newConfig.primary.map(item => item.id),
            secondary: newConfig.secondary.map(item => item.id),
        };
        setNavConfig(newConfig);
        localStorage.setItem('adminNavConfig', JSON.stringify(configToSave));
    };

    const handleMoreMenuOpen = (event) => setAnchorEl(event.currentTarget);
    const handleMoreMenuClose = () => setAnchorEl(null);

    const filteredNavItems = useMemo(() => 
        navItems.filter(item => 
            item.name.toLowerCase().includes(searchTerm.toLowerCase())
        ), 
    [searchTerm]);
    
    const categorizedIds = useMemo(() => new Set(Object.values(menuCategories).flat()), []);
    
    if (mode === 'sidebar') {
        const isCollapsed = !isHovered;
        return (
            <AdminComponents.AdminNavSidebar component="aside" className={`sidebar ${isCollapsed ? 'collapsed' : ''}`} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
                <AdminComponents.AdminNavSidebarSearch>
                    {!isCollapsed && ( <TextField fullWidth variant="outlined" size="small" placeholder="Search menu..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} InputProps={{ startAdornment: ( <InputAdornment position="start"><Search /></InputAdornment> ), }} /> )}
                </AdminComponents.AdminNavSidebarSearch>
                <AdminComponents.AdminNavSidebarNav component="nav" className="sidebar-nav" isCollapsed={isCollapsed}>
                    {filteredNavItems.map((item) => (
                        <Tooltip key={item.name} title={isCollapsed ? item.name : ''} placement="right">
                            <NavLink to={item.path} className={({ isActive }) => (isActive ? "active" : "")} end={item.path === '/admin'}>
                                {item.icon}<span>{item.name}</span>
                            </NavLink>
                        </Tooltip>
                    ))}
                </AdminComponents.AdminNavSidebarNav>
            </AdminComponents.AdminNavSidebar>
        );
    }

    return (
        <AdminComponents.AdminNavTopBar component="nav">
            <AdminComponents.AdminNavTopBarList>
                {navConfig.primary.map((item) => (
                    <li key={item.id}>
                        <NavLink 
                            to={item.path} 
                            className={({ isActive }) => isActive ? "top-nav-link active" : "top-nav-link"}
                            end={item.path === '/admin'}
                        >
                            {item.icon}
                            <span>{item.name}</span>
                        </NavLink>
                    </li>
                ))}
            
                <AdminComponents.AdminNavActionsContainer>
                    <AdminComponents.AdminNavTopBarMoreButton onClick={handleMoreMenuOpen} endIcon={<KeyboardArrowDown />}>More</AdminComponents.AdminNavTopBarMoreButton>
                    <AdminComponents.AdminNavMegaMenu
                        anchorEl={anchorEl} 
                        open={Boolean(anchorEl)} 
                        onClose={handleMoreMenuClose} 
                        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
                    >
                        <AdminComponents.MegaMenuContent>
                            {Object.entries(menuCategories).map(([category, itemIds]) => {
                                const itemsInCategory = navConfig.secondary.filter(item => itemIds.includes(item.id));
                                if (itemsInCategory.length === 0) return null;
                                return (
                                    <AdminComponents.MegaMenuCategory key={category}>
                                        <AdminComponents.MegaMenuListSubheader disableSticky>{category}</AdminComponents.MegaMenuListSubheader>
                                        <List dense>
                                            {itemsInCategory.map(item => (
                                                <AdminComponents.MegaMenuItem key={item.id} component={NavLink} to={item.path} onClick={handleMoreMenuClose}>
                                                    <AdminComponents.MegaMenuItemIcon>{item.icon}</AdminComponents.MegaMenuItemIcon>
                                                    <ListItemText primary={item.name} />
                                                </AdminComponents.MegaMenuItem>
                                            ))}
                                        </List>
                                    </AdminComponents.MegaMenuCategory>
                                );
                            })}
                            <AdminComponents.MegaMenuCategory>
                                <AdminComponents.MegaMenuListSubheader disableSticky>Other</AdminComponents.MegaMenuListSubheader>
                                    <List dense>
                                    {navConfig.secondary.filter(item => !categorizedIds.has(item.id)).map(item => (
                                            <AdminComponents.MegaMenuItem key={item.id} component={NavLink} to={item.path} onClick={handleMoreMenuClose}>
                                            <AdminComponents.MegaMenuItemIcon>{item.icon}</AdminComponents.MegaMenuItemIcon>
                                            <ListItemText primary={item.name} />
                                        </AdminComponents.MegaMenuItem>
                                    ))}
                                </List>
                            </AdminComponents.MegaMenuCategory>
                        </AdminComponents.MegaMenuContent>
                        <AdminComponents.MegaMenuDivider />
                        <AdminComponents.MegaMenuConfigureItem onClick={() => { handleMoreMenuClose(); setIsConfigureModalOpen(true); }}>
                            <ListItemIcon><SettingsOutlined /></ListItemIcon>
                            <ListItemText>Configure Menu</ListItemText>
                        </AdminComponents.MegaMenuConfigureItem>
                    </AdminComponents.AdminNavMegaMenu>
                </AdminComponents.AdminNavActionsContainer>
            </AdminComponents.AdminNavTopBarList>
            
            <ConfigureMenuModal 
                open={isConfigureModalOpen} 
                onClose={() => setIsConfigureModalOpen(false)} 
                navConfig={navConfig} 
                onSave={handleSaveNavConfig} 
                limit={VISIBLE_ITEMS_LIMIT}
                defaultConfig={DEFAULT_CONFIG}
            />
        </AdminComponents.AdminNavTopBar>
    );
};

export default AdminNavigation;