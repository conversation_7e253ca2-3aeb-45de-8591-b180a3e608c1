import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider as MuiThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';

// --- Static Page Imports ---
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import Hero from './components/sections/Hero';
import Industries from './components/sections/Industries';
import Solutions from './components/sections/Solutions';
import Partners from './components/sections/Partners';
import Pricing from './components/sections/Pricing';
import Faq from './components/sections/Faq';
import AboutUs from './components/sections/AboutUs';
import Contact from './components/sections/Contact';
import Cart from './components/sections/Cart';
import AuthModal from './components/common/AuthModal';
import FabMenu from './components/common/FabMenu';
import AssistantFab from './components/common/AssistantFab';

// --- Admin Dashboard Imports ---
import './App1.css';
import { theme as baseTheme, AdminComponents } from './styles/theme';
import TopBar from './components/AdminComponents/shared/TopBar';
import AdminNavigation from './components/AdminComponents/shared/AdminNavigation';
import AdminFooter from './components/AdminComponents/shared/Footer';
import Breadcrumbs from './components/AdminComponents/shared/Breadcrumbs';
import ErrorBoundary from './components/AdminComponents/shared/ErrorBoundary';

// --- Admin Page Imports ---
import HomeAdmin from './components/AdminComponents/Home/Home';
import Contract from './components/AdminComponents/Contract/Contract';
import License from './components/AdminComponents/License/License';
import Billing from './components/AdminComponents/Billing/Billing';
import Server from './components/AdminComponents/Server/Server';
import Travel from './components/AdminComponents/Travel/Travel';
import Support from './components/AdminComponents/Support/Support';
import Purchase from './components/AdminComponents/Purchase/Purchase';
import SLA from './components/AdminComponents/SLA/SLA';
import Professional from './components/AdminComponents/Professional/Professional';
import Customers from './components/AdminComponents/Customers/Customers';
import Renewals from './components/AdminComponents/Renewals/Renewals';
import OnBoarding from './components/AdminComponents/OnBoarding/OnBoarding';
import AdminPanel from './components/AdminComponents/AdminPanel/AdminPanel';
import User from './components/AdminComponents/Users/<USER>';
import Quotation from './components/AdminComponents/Quotation/Quotation';


// --- Constants ---
const defaultAdmin = {
  email: '<EMAIL>',
  password: '12345',
  type: 'admin',
  name: 'Admin',
  country: 'India',
  role: 'Admin',
  company: 'HCLTech',
  phone: '9876543210',
  address: '123, Main St, Anytown, USA',
  photo: 'src/assets/social-icons/user.png',
};

function App() {
  // --- STATE MANAGEMENT ---
  const [layoutMode, setLayoutMode] = useState('sidebar'); 
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [user, setUser] = useState(null);
  const [authReady, setAuthReady] = useState(false); // FIX: State to track if auth check is complete
  const [isDefaultUserLoggedIn, setIsDefaultUserLoggedIn] = useState(true);
  const [cart, setCart] = useState([]);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('success');
  const [approvalModal, setApprovalModal] = useState({ open: false, order: null, type: null });
  const [selectedIndustry, setSelectedIndustry] = useState(null);

  // --- EFFECT FOR INITIAL USER LOAD ---
  useEffect(() => {
    let activeUser = null;
    try {
      const stored = localStorage.getItem('user');
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed && parsed.loginTimestamp) {
          const now = Date.now();
          if (now - parsed.loginTimestamp < 10 * 60 * 1000) {
            activeUser = parsed;
          } else {
            localStorage.removeItem('user');
          }
        }
      }
    } catch (error) {
      console.error("Failed to parse user from localStorage:", error);
      localStorage.removeItem('user');
    }

    if (!activeUser && window.location.pathname.startsWith('/admin')) {
      const adminUser = { ...defaultAdmin, loginTimestamp: Date.now() };
      localStorage.setItem('user', JSON.stringify(adminUser));
      activeUser = adminUser;
    }
    
    setUser(activeUser);
    setAuthReady(true); // FIX: Signal that auth check is done
  }, []);

  // --- THEME AND LAYOUT ---
  const toggleTheme = () => setIsDarkMode(prev => !prev);
  
  const activeTheme = createTheme({
    ...baseTheme,
    palette: {
        ...baseTheme.palette,
        mode: isDarkMode ? 'dark' : 'light',
        background: {
            default: isDarkMode ? '#111827' : 'var(--background-primary)',
            paper: isDarkMode ? '#1f2937' : 'var(--background-secondary)',
        }
    }
  });

  // --- HANDLERS AND EFFECTS ---
  const handleIndustrySelect = (industryName) => {
    setSelectedIndustry(industryName);
    setTimeout(() => {
      const el = document.getElementById('solutions');
      if (el) {
        const y = el.getBoundingClientRect().top + window.pageYOffset - 60;
        window.scrollTo({ top: y, behavior: 'smooth' });
      }
    }, 100);
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    setIsDefaultUserLoggedIn(false);
    setCart([]);
    setMessage('You have been logged out successfully.');
    setMessageType('success');
    setTimeout(() => setMessage(''), 3000);
    window.location.href = '/';
  };

  const handleAddToCart = (item, type) => {
    if (user) {
      const identifier = type === 'module' ? item.id : item.name;
      const exists = cart.some(
        c => c.type === type && (type === 'module' ? c.id === identifier : c.name === identifier)
      );
      if (!exists) {
        setCart(c => [...c, type === 'module' ? { type, id: item.id } : { type, name: item.name }]);
        setMessage(type === 'module' ? `${item.name} added to cart.` : `${item.name} suite added to cart.`);
        setMessageType('success');
      } else {
        setMessage(type === 'module' ? `${item.name} is already in the cart.` : `${item.name} suite is already in the cart.`);
        setMessageType('warning');
      }
      setTimeout(() => setMessage(''), 2000);
    } else {
      setShowAuthModal(true);
    }
  };

  const cartCount = cart.length;

  const handleLogin = (userObj) => {
    const userWithTimestamp = { ...userObj, loginTimestamp: Date.now() };
    setUser(userWithTimestamp);
    localStorage.setItem('user', JSON.stringify(userWithTimestamp));
    setShowAuthModal(false);
  };

  useEffect(() => {
    if (user && user.type === 'admin' && !window.location.pathname.startsWith('/admin')) {
      window.location.href = '/admin';
    }
  }, [user]);

  // --- RENDER ---
  // FIX: Don't render anything until the auth check is complete
  if (!authReady) {
    return <div />; // Or a loading spinner
  }

  return (
    <MuiThemeProvider theme={activeTheme}>
        <CssBaseline />
        <Router>
            {message && (
                <AdminComponents.MessageContainer messageType={messageType}>
                <AdminComponents.MessageIcon>
                    {messageType === 'success' ? '\u2705' : '\u26a0\ufe0f'}
                </AdminComponents.MessageIcon>
                <span>{message}</span>
                </AdminComponents.MessageContainer>
            )}
            {user && user.type === 'admin' && window.location.pathname.startsWith('/admin') ? (
                // --- ADMIN DASHBOARD LAYOUT ---
                <AdminComponents.AdminLayoutContainer>
                    <TopBar
                        logout={logout}
                        layoutMode={layoutMode}
                        setLayoutMode={setLayoutMode}
                        toggleTheme={toggleTheme}
                        isDarkMode={isDarkMode}
                    />
                    <AdminComponents.AdminMainBody>
                        {layoutMode === 'sidebar' && <AdminNavigation mode="sidebar" />}
                        <AdminComponents.AdminContentContainer>
                            {layoutMode === 'topbar' && <AdminNavigation mode="topbar" />}
                            <AdminComponents.AdminMainContent component="main">
                                <Breadcrumbs />
                                <ErrorBoundary>
                                    <Routes>
                                        <Route path="/admin" element={<HomeAdmin />} />
                                        <Route path="/admin/contract" element={<Contract />} />
                                        <Route path="/admin/license" element={<License />} />
                                        <Route path="/admin/billing" element={<Billing />} />
                                        <Route path="/admin/server" element={<Server />} />
                                        <Route path="/admin/travel" element={<Travel />} />
                                        <Route path="/admin/support" element={<Support />} />
                                        <Route path="/admin/purchase" element={<Purchase />} />
                                        <Route path="/admin/sla" element={<SLA />} />
                                        <Route path="/admin/professional" element={<Professional />} />
                                        <Route path="/admin/customers" element={<Customers />} />
                                        <Route path="/admin/renewals" element={<Renewals />} />
                                        <Route path="/admin/onboarding" element={<OnBoarding />} />
                                        <Route path="/admin/panel" element={<AdminPanel />} />
                                        <Route path="/admin/user" element={<User />} />
                                        <Route path="/admin/Quotation" element={<Quotation />} />
                                    </Routes>
                                </ErrorBoundary>
                            </AdminComponents.AdminMainContent>
                        </AdminComponents.AdminContentContainer>
                    </AdminComponents.AdminMainBody>
                    <AdminFooter />
                </AdminComponents.AdminLayoutContainer>
            ) : (
                // --- PUBLIC WEBSITE LAYOUT ---
                <Routes>
                <Route path="/" element={
                    <>
                    <Header user={user} setUser={setUser} logout={logout} cartCount={cartCount} showAuthModal={showAuthModal} setShowAuthModal={setShowAuthModal} onLogin={handleLogin} />
                    <main>
                        <Hero />
                        <Industries onIndustrySelect={handleIndustrySelect} />
                        <Solutions handleAddToCart={handleAddToCart} selectedIndustry={selectedIndustry} onClearIndustry={() => setSelectedIndustry(null)} />
                        <Partners />
                        <Pricing />
                        <Faq />
                        <AboutUs />
                        <Contact />
                    </main>
                    <Footer />
                    </>
                } />
                <Route path="/cart" element={
                    <Cart 
                    user={user} 
                    setUser={setUser} 
                    logout={logout} 
                    cartCount={cartCount} 
                    showAuthModal={showAuthModal} 
                    setShowAuthModal={setShowAuthModal}
                    isDefaultUserLoggedIn={isDefaultUserLoggedIn}
                    setIsDefaultUserLoggedIn={setIsDefaultUserLoggedIn}
                    onLogin={handleLogin}
                    />
                } />
                </Routes>
            )}
            {showAuthModal && (
                <AuthModal mode={"login"} onClose={() => setShowAuthModal(false)} onLogin={handleLogin} />
            )}
            <FabMenu isAdmin={user && user.type === 'admin' && window.location.pathname.startsWith('/admin')} />
            <AssistantFab />
        </Router>
    </MuiThemeProvider>
  );
}

export default App;
