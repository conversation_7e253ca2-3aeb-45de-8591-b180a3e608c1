import React from 'react';
// import './Card.css';

const Card = ({
  title,
  subtitle,
  status,
  icon,
  children,
  actions,
  className = '',
  ...props
}) => {
  return (
    <div className={`modern-card ${className}`} {...props}>
      <div className="card-header">
        {icon && <div className="card-icon">{icon}</div>}
        <div className="card-header-text">
          <div className="card-title">{title}</div>
          {subtitle && <div className="card-subtitle">{subtitle}</div>}
        </div>
        {status && <div className={`card-status status-pill ${status.toLowerCase().replace(/\s+/g, '-')}`}>{status}</div>}
      </div>
      <div className="card-content">{children}</div>
      {actions && <div className="card-actions">{actions}</div>}
    </div>
  );
};

export default Card; 