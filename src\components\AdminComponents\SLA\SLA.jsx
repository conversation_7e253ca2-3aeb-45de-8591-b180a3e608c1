import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Menu, MenuItem, Drawer
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, CheckCircle, Cancel, MoreVert
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialSLAs = [
    { id: 1, slaId: 'SLA-001', customer: 'Acme Corp', status: 'Active', priority: 'Critical', subject: 'Database issue', created: '2024-07-01', slaTime: '2h', service: 'DB', uptime: '99.9%', responseTime: '1h' },
    { id: 2, slaId: 'SLA-002', customer: 'TechStart', status: 'Pending', priority: 'High', subject: 'Login failure', created: '2024-07-02', slaTime: '4h', service: 'Auth', uptime: '99.5%', responseTime: '2h' },
    { id: 3, slaId: 'SLA-003', customer: 'Global Solutions', status: 'Expired', priority: 'Medium', subject: 'Performance', created: '2024-06-28', slaTime: '8h', service: 'API', uptime: '98.7%', responseTime: '3h' },
];

const ALL_COLUMNS = [
    { key: 'slaId', label: 'SLA ID', type: 'string' },
    { key: 'customer', label: 'Customer', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'priority', label: 'Priority', type: 'string' },
    { key: 'subject', label: 'Subject', type: 'string' },
    { key: 'created', label: 'Created', type: 'string' },
    { key: 'slaTime', label: 'SLA Time', type: 'string' },
    { key: 'service', label: 'Service', type: 'string' },
    { key: 'uptime', label: 'Uptime', type: 'string' },
    { key: 'responseTime', label: 'Response Time', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ sla, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(sla, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(sla, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([sla.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const RenewalTable = ({ renewals, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < renewals.length} checked={renewals.length > 0 && selectedIds.length === renewals.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {renewals.map(r => (
                        <TableRow key={r.id} hover selected={selectedId === r.id} onClick={() => onRowClick(r)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(r.id)} onChange={() => onSelectOne(r.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{r[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons renewal={r} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const SLACard = ({ sla, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(sla.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons sla={sla} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{sla.subject}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{sla.customer}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: sla.status }} label={sla.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Priority:</strong> {sla.priority}</Typography>
            <Typography variant="body2"><strong>Created:</strong> {sla.created}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const SLACompactCard = ({ sla, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(sla.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons sla={sla} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{sla.subject}</Typography>
                <Typography variant="caption" color="text.secondary">{sla.customer}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{sla.priority}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: sla.status }} label={sla.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const SLAListItem = ({ sla, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(sla.id)} />
            <Box>
                <Typography fontWeight="bold">{sla.subject}</Typography>
                <Typography variant="body2" color="text.secondary">{sla.customer}</Typography>
            </Box>
            <Typography variant="body2">{sla.priority}</Typography>
            <Typography variant="body2">{sla.status}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: sla.status }} label={sla.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons sla={sla} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const SLAGraph = ({ sla }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && sla) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Response Time (h)'],
                    datasets: [{
                        label: 'Response Time (h)',
                        data: [parseInt(sla.responseTime) || 0],
                        backgroundColor: ['rgba(60, 145, 255, 0.7)'],
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${sla.subject} - Response Time` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [sla]);

    return (
        <>
            {sla ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select an SLA to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const SLA = () => {
    // --- State for sidebar and search/settings logic ---
    const [slas, setSLAs] = useState(initialSLAs);
    const [selectedSLA, setSelectedSLA] = useState(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('slaId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new SLA', target: 'SLA-001', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Activated SLA', target: 'SLA-002', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Support', action: 'Expired SLA', target: 'SLA-003', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Advanced Search State
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(slas.map(s => s.status))];
        const priorities = [...new Set(slas.map(s => s.priority))];
        return [...statuses, ...priorities];
    }, [slas]);

    const processedSLAs = useMemo(() => {
        let current = slas;
        if (filter !== 'all') current = current.filter(s => s.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(s => s.slaId.toLowerCase().includes(term) || s.customer.toLowerCase().includes(term));
        }
        if (activeFilters.length > 0) {
            current = current.filter(sla => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const slaValue = String(field.includes('.') ? field.split('.').reduce((o, i) => o?.[i], sla) : sla[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return slaValue === filterValue;
                        case 'Not Equals': return slaValue !== filterValue;
                        case 'Contains': return slaValue.includes(filterValue);
                        case 'Starts With': return slaValue.startsWith(filterValue);
                        case 'Ends With': return slaValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [slas, filter, searchTerm, activeFilters, sortColumn, sortDirection]);

    const summaryStats = useMemo(() => ({
        total: slas.length,
        active: slas.filter(s => s.status === 'Active').length,
        expired: slas.filter(s => s.status === 'Expired').length,
        pending: slas.filter(s => s.status === 'Pending').length,
    }), [slas]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (sla) => setSelectedSLA(sla);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedSLAs.map(s => s.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displaySLA = useMemo(() => {
        const isSelectedVisible = processedSLAs.some(s => s.id === selectedSLA?.id);
        if (isSelectedVisible) return selectedSLA;
        return processedSLAs.length > 0 ? processedSLAs[0] : null;
    }, [processedSLAs, selectedSLA]);

    // --- Advanced Search Handlers ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleAddQuickFilter = (value) => {
        const field = ['Active', 'Pending', 'Expired'].includes(value) ? 'status' : 'priority';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev => prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]);
    };

    // --- Sidebar logic (search/settings) ---
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) {
            setIsGraphVisible(false);
        }
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        const colLabel = ALL_COLUMNS.find(c => c.key === columnKey)?.label || columnKey;
        setActivityLog(prev => [{ user: 'Admin', action: `toggled '${colLabel}' column visibility`, timestamp: new Date().toLocaleString() }, ...prev].slice(0, 10));
    };

    // --- Render ---
    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedSLAs.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedSLAs.map(s => <SLACard key={s.id} sla={s} onClick={() => setSelectedSLA(s)} isSelected={displaySLA?.id === s.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(s.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <SLATable
                            slas={processedSLAs}
                            onRowClick={setSelectedSLA}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displaySLA?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedSLAs.map(s => <SLACompactCard key={s.id} sla={s} onClick={() => setSelectedSLA(s)} isSelected={displaySLA?.id === s.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(s.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedSLAs.map(s => <SLAListItem key={s.id} sla={s} onClick={() => setSelectedSLA(s)} isSelected={displaySLA?.id === s.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(s.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching SLAs</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><BarChart /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'active'} onClick={() => setFilter('active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expired'} onClick={() => setFilter('expired')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expired}</Typography><Typography variant="body2">Expired</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'pending'} onClick={() => setFilter('pending')}>
                                        <AdminComponents.SummaryAvatar variant="active"><BarChart /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.pending}</Typography><Typography variant="body2">Pending</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add SLA</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                    <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                    <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedSLAs.length > 0 && selectedIds.length === processedSLAs.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedSLAs.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <SLAGraph sla={displaySLA} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
            <Drawer variant="persistent" anchor="right" open={isSidebarOpen}>
                <AdminComponents.SidebarContainer>
                    <AdminComponents.SidebarHeader>
                        <Typography variant="h6">{sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}</Typography>
                        <IconButton onClick={() => setIsSidebarOpen(false)}><MoreVert /></IconButton>
                    </AdminComponents.SidebarHeader>
                    <AdminComponents.SidebarContent>
                        {sidebarMode === 'search' && (
                            <>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.QuickFilterContainer>{quickFilterOptions.map(opt => (<Button key={opt} onClick={() => handleAddQuickFilter(opt)}>{opt}</Button>))}</AdminComponents.QuickFilterContainer>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                    <TextField select label="Field" value={filterBuilder.field} onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))} fullWidth size="small">{ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}</TextField>
                                    <TextField select label="Operator" value={filterBuilder.operator} onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))} fullWidth size="small">{FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}</TextField>
                                    <TextField label="Value" value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))} fullWidth size="small" />
                                    <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.FilterChipContainer>{stagedFilters.length > 0 ? stagedFilters.map(f => (<Button key={f.id} onClick={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))}>{`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`}</Button>)) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}</AdminComponents.FilterChipContainer>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.FilterChipContainer>{activeFilters.length > 0 ? activeFilters.map(f => (<Button key={f.id} onClick={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))}>{`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`}</Button>)) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}</AdminComponents.FilterChipContainer>
                                </AdminComponents.SidebarSection>
                            </>
                        )}
                        {sidebarMode === 'grid' && (
                            <>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.ColumnActionContainer><Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button><Button size="small" onClick={() => setColumnOrder([])}>Deselect All</Button></AdminComponents.ColumnActionContainer>
                                    <AdminComponents.ColumnVisibilityContainer>{ALL_COLUMNS.map(col => (<AdminComponents.WrappedFormControlLabel key={col.key} control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} />} label={col.label} />))}</AdminComponents.ColumnVisibilityContainer>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.FilterChipContainer>{groupByKeys.length > 0 ? groupByKeys.map(key => (<Button key={key} onClick={() => handleGroupByChange(key)}>{ALL_COLUMNS.find(c => c.key === key)?.label}</Button>)) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}</AdminComponents.FilterChipContainer>
                                    <AdminComponents.ColumnVisibilityContainer>{ALL_COLUMNS.map(col => (<AdminComponents.WrappedFormControlLabel key={col.key} control={<Checkbox checked={groupByKeys.includes(col.key)} onChange={() => handleGroupByChange(col.key)} />} label={col.label} />))}</AdminComponents.ColumnVisibilityContainer>
                                </AdminComponents.SidebarSection>
                            </>
                        )}
                    </AdminComponents.SidebarContent>
                    <AdminComponents.SidebarFooter>
                        {sidebarMode === 'search' && (<><Button variant="outlined" onClick={handleResetFilters}>Reset</Button><Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button></>)}
                        {sidebarMode === 'grid' && (<Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>)}
                    </AdminComponents.SidebarFooter>
                </AdminComponents.SidebarContainer>
            </Drawer>
        </ThemeProvider>
    );
};

export default SLA; 