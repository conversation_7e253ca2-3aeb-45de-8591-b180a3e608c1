import React from 'react';
import { MdCheckCircle } from 'react-icons/md';
import Button from '@mui/material/Button';
// import './PricingCard.css';

const PricingCard = ({ plan }) => {
    const { name, price, period, features, isPopular, buttonText } = plan;

    return (
        <div className={`pricing-card ${isPopular ? 'popular' : ''}`}>
            {isPopular && <div className="popular-badge">Most Popular</div>}
            <div className="pricing-card-header">
                <h3 className="plan-name">{name}</h3>
                <div className="plan-price">
                    <span className="price-amount">{price}</span>
                    {period.startsWith('/') && <span className="price-period">{period}</span>}
                </div>
                 {period === 'Contact Us' && <p className="price-contact">{period}</p>}
            </div>
            <ul className="features-list">
                {features.map((feature, index) => (
                    <li key={index} className="feature-item">
                        <MdCheckCircle className="feature-icon" />
                        <span>{feature}</span>
                    </li>
                ))}
            </ul>
            <div className="pricing-card-footer">
                <Button
                  variant={isPopular ? 'contained' : 'outlined'}
                  color="primary"
                  fullWidth
                  className="pricing-card-btn"
                >
                  {buttonText}
                </Button>
            </div>
        </div>
    );
};

export default PricingCard; 