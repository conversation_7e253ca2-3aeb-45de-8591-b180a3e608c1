
// src/theme/overrideTheme.js
import { createTheme } from '@mui/material/styles';

/**
 * Creates a Material-UI theme object based on the provided palette configuration.
 * It integrates CSS variables for styling and sets up common component overrides.
 *
 * @param {object} muiPaletteConfig - The MUI palette configuration for the current brand.
 * @returns {object} A Material-UI theme object.
 */
export const createOverrideTheme = (muiPaletteConfig) => {
  if (!muiPaletteConfig) {
    console.error("MUI Palette configuration is missing. Using default MUI theme.");
    return createTheme(); // Return a default MUI theme if config is missing
  }

  return createTheme({
    palette: {
      ...muiPaletteConfig, // Dynamically inject primary, secondary, error, etc. colors
      // You can add or override specific palette properties here if needed
      // For example, to ensure contrastText for primary is always white even if not specified in BRAND_THEMES_DATA
      primary: {
        ...muiPaletteConfig.primary,
        contrastText: muiPaletteConfig.primary?.contrastText || '#FFFFFF',
      },
      secondary: {
        ...muiPaletteConfig.secondary,
        contrastText: muiPaletteConfig.secondary?.contrastText || '#000000', // Assuming black for light secondary colors
      },
    },
    typography: {
      fontFamily: 'var(--font-family-primary, "HCLTech Roobert", Roboto, sans-serif)', // Use CSS variable for font family
      h1: {
        color: 'var(--color-text-heading)',
        fontSize: '3.5rem', // Example specific font size
        fontWeight: 'var(--font-weight-bold)',
        lineHeight: '1.2',
      },
      h2: {
        color: 'var(--color-text-heading)',
        fontSize: '2.5rem',
        fontWeight: 'var(--font-weight-bold)',
        lineHeight: '1.3',
      },
      h3: {
        color: 'var(--color-text-heading)',
        fontSize: '2rem',
        fontWeight: 'var(--font-weight-medium)',
        lineHeight: '1.4',
      },
      body1: {
        color: 'var(--color-text-default)',
        fontSize: 'var(--font-size-base)',
        lineHeight: 'var(--line-height-body)',
        fontWeight: 'var(--font-weight-normal)',
      },
      body2: {
        color: 'var(--color-text-secondary)',
        fontSize: '0.875rem',
        lineHeight: '1.4',
        fontWeight: 'var(--font-weight-normal)',
      },
      subtitle1: {
        color: 'var(--color-text-muted)',
        fontSize: '1rem',
        fontWeight: 'var(--font-weight-normal)',
      },
      // You can define other typography variants as needed
    },
    spacing: (factor) => [`var(--spacing-xs)`, `var(--spacing-sm)`, `var(--spacing-md)`, `var(--spacing-lg)`, `var(--spacing-xl)`][factor - 1] || `${8 * factor}px`, // Map to CSS vars or fallback to default MUI spacing
    shape: {
      borderRadius: 'var(--radius-md, 8px)', // Use CSS variable for border radius
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          // This is where global CSS variables are actually applied to the root
          // However, your ThemeProvider already injects these into :root
          // You might use this for very specific global styles that MUI doesn't cover
          // or to override MUI's default styles with CSS variables more directly.
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            
            padding: `var(--button-padding)`,
            borderRadius: `var(--button-border-radius)`,
            fontWeight: `var(--font-weight-button)`,
            fontSize: `var(--font-size-base)`,
            transition: `var(--transition-ease-in-out)`,
            backgroundColor: `var(--button-primary-bg)`,
            color: `var(--button-color)`,
          },      
        containedAdd: {
          backgroundColor: 'var(--button-add)',
          '&:hover': {
            backgroundColor: 'var(--button-add-hover)',
          },
        },
        containedDelete: {
          backgroundColor: 'var(--button-delete)',
          '&:hover': {
            backgroundColor: 'var(--button-delete-hover)',
          },
        },
        containedEdit: {
          backgroundColor: 'var(--button-edit)',
          '&:hover': {
            backgroundColor: 'var(----button-edit-hover)',
          },
        },
        containedApprove: {
          backgroundColor: 'var(--button-approve)',
          '&:hover': {
            backgroundColor: 'var(--button-warning-hover-bg)',
          },
        },
        containedReject: {
          backgroundColor: 'var(--button-reject)',
          '&:hover': {
            backgroundColor: 'var(--button-error-hover-bg)',
          },
        },
        containedCancel: {
          backgroundColor: 'var(--button-cancel)',
          '&:hover': {
            backgroundColor: 'var(--button-error-hover-bg)',
          },
        },
      },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiInputBase-root': {
              borderRadius: 'var(--radius-sm)',
              color: 'var(--input-text-color)',
            },
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: 'var(--input-border-color)',
              borderWidth: 'var(--input-border-thickness)',
            },
            '& .MuiFilledInput-underline:before': {
              borderBottomColor: 'var(--input-border-color)',
            },
            '& .MuiInput-underline:before': {
              borderBottomColor: 'var(--input-border-color)',
            },
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: 'var(--input-focus-border)', // Hover state border
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: 'var(--input-focus-border)', // Focused state border
            },
            '& .MuiInputLabel-root': {
              color: 'var(--input-placeholder-color)', // Label color
            },
            '& .MuiInputLabel-root.Mui-focused': {
              color: 'var(--input-focus-border)', // Focused label color
            },
            '& .MuiInputBase-input::placeholder': {
              color: 'var(--input-placeholder-color)',
              opacity: 1, // Ensure placeholder is visible
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: 'var(--card-shadow)',
            borderRadius: 'var(--card-border-radius)',
            backgroundColor: 'var(--card-bg)',
            padding: 'var(--card-padding)',
          },
        },
      },
      MuiCardHeader: {
        styleOverrides: {
          title: {
            color: 'var(--card-header-color)',
          },
          subheader: {
            color: 'var(--color-text-secondary)',
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          colorPrimary: {
            backgroundColor: 'var(--navbar-bg)',
            color: 'var(--navbar-text-color)',
          },
        },
      },
      // --- FAB OVERRIDES START ---
    MuiFab: {
      styleOverrides: {
        // Styles applied to the root element.
        root: {
          // General styling for all Fabs
            backgroundColor: 'var(--color-light-teal)', 
          borderRadius: 'var(--fab-border-radius, 16px)', // Example: slightly more rounded
          boxShadow: 'var(--fab-box-shadow, 0px 4px 12px rgba(0, 0, 0, 0.2))', // Example: subtle shadow
          transition: 'transform 0.2s ease-in-out, background-color 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)', // Example: slight lift on hover
          },
        },
        // Styles applied to the root element if `color="primary"`.
       
        // Styles applied to the root element if `color="secondary"`.
        colorSecondary: {
          backgroundColor: 'var(--fab-secondary-bg, #dc004e)', // Default MUI secondary color
          color: 'var(--fab-secondary-text-color, #fff)',   // Default MUI text color on secondary
          '&:hover': {
            backgroundColor: 'var(--fab-secondary-hover-bg, #a7003c)', // Darker secondary on hover
          },
        },
        // Styles applied to the root element if `variant="extended"`.
        extended: {
          // Specific styles for extended Fab
          minWidth: 'var(--fab-extended-min-width, 120px)',
          padding: 'var(--fab-extended-padding, 0 22px)',
        },
        // Styles applied to the root element if `disabled={true}`.
        disabled: {
          backgroundColor: 'var(--fab-disabled-bg, rgba(0, 0, 0, 0.12))', // Default MUI disabled bg
          color: 'var(--fab-disabled-text-color, rgba(0, 0, 0, 0.26))', // Default MUI disabled text
          cursor: 'not-allowed',
          boxShadow: 'none', // No shadow when disabled
          transform: 'none', // No hover effect when disabled
        },
      },
    },
      MuiLink: {
        styleOverrides: {
          root: {
            color: 'var(--color-text-link)',
            '&:hover': {
              color: 'var(--color-text-link-hover)',
            },
          },
        },
      },
      MuiSwitch: {
        styleOverrides: {
          switchBase: {
            '&.Mui-checked': {
              color: 'var(--color-primary-brand)',
            },
            '&.Mui-checked + .MuiSwitch-track': {
              backgroundColor: 'var(--color-primary-brand)',
            },
          },
        },
      },
      MuiCheckbox: {
        styleOverrides: {
          root: {
            color: 'var(--checkbox)', // Uses the new --checkbox variable
            '&.Mui-checked': {
              color: 'var(--checkbox)',
            },
          },
        },
      },
      MuiRadio: {
        styleOverrides: {
          root: {
            color: 'var(--checkbox)', // Reusing checkbox color for radio for consistency or define a new var
            '&.Mui-checked': {
              color: 'var(--checkbox)',
            },
          },
        },
      },
      MuiRating: {
        styleOverrides: {
          iconFilled: {
            color: 'var(--color-rating-star)', // Uses the new --color-rating-star variable
          },
          iconEmpty: {
            color: 'var(--color-rating-star)', // Apply to empty as well for consistent opacity adjustment
          },
        },
      },
      MuiSlider: {
        styleOverrides: {
          root: {
            color: 'var(--color-primary-brand)', // Example: Slider track color
            width: 'var(--SliderWidth, 300px)', // Use SliderWidth
          },
          thumb: {
            backgroundColor: 'var(--color-primary-brand)', // Example: Slider thumb color
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 'var(--radius-sm)',
          },
          colorPrimary: {
            backgroundColor: 'var(--color-primary-brand)',
            color: 'var(--color-text-on-dark)',
          },
          colorSecondary: {
            backgroundColor: 'var(--color-secondary-brand)',
            color: 'var(--color-text-on-light)', // Assuming secondary might be lighter
          },
        },
      },
      MuiAvatar: {
        styleOverrides: {
          root: {
         
          },
        },
      },
      MuiDivider: {
        styleOverrides: {
          root: {
            borderColor: 'var(--theme-border-color)',
          },
        },
      },
      MuiAlert: {
        styleOverrides: {
          root: {
            borderRadius: 'var(--radius-sm)',
            '&.MuiAlert-standardSuccess': {
              backgroundColor: 'var(--alert-success-bg)', // Using the new variable
              color: 'var(--alert-success-color)', // Using the new variable
              '& .MuiAlert-icon': {
                color: 'var(--alert-success-color)', // Icon color
              },
            },
            '&.MuiAlert-standardInfo': {
              backgroundColor: 'var(--color-status-info-background)',
              color: 'var(--color-status-info)',
            },
            '&.MuiAlert-standardWarning': {
              backgroundColor: 'var(--color-status-warning-background)',
              color: 'var(--color-status-warning)',
            },
            '&.MuiAlert-standardError': {
              backgroundColor: 'var(--color-status-error-background)',
              color: 'var(--color-status-error)',
            },
          },
        },
      },
      MuiList: {
        styleOverrides: {
          root: {
            backgroundColor: 'var(--theme-background-secondary)',
            borderRadius: 'var(--card-border-radius)', // Example
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            color: 'var(--color-text-default)',
            '&.Mui-selected': {
              backgroundColor: 'rgba(var(--color-primary-brand-rgb), 0.08)',
              color: 'var(--color-primary-brand)',
            },
          },
        },
      },
      MuiTableContainer: {
        styleOverrides: {
          root: {
            boxShadow: 'var(--card-shadow)',
            borderRadius: 'var(--card-border-radius)',
            backgroundColor: 'var(--card-bg)',
          },
        },
      },
      /*SPEEDDAIL*/
      MuiSpeedDial: {
  styleOverrides: {
    root: {
      backgroundColor: 'transparent', // Consistent with MuiStepper
    },
    fab: {
      backgroundColor: 'var(--color-primary-brand)',
      color: 'var(--color-mid-teal)',
      borderRadius: 'var(--radius-md)',
      transition: 'var(--transition-fast)',
      '&:hover': {
        backgroundColor: 'var(--button-primary-hover-bg-brand)',
      },
    },
  },
},

      MuiTableHead: {
        styleOverrides: {
          root: {
            backgroundColor: 'var(--theme-background-primary)', // Example
          },
        },
      },
      MuiTableCell: {
        styleOverrides: {
          root: {
            borderColor: 'var(--theme-border-color)',
            color: 'var(--color-text-default)',
          },
          head: {
            color: 'var(--color-text-heading)', // Table header text color
            fontWeight: 'var(--font-weight-medium)',
          },
        },
      },
      MuiTooltip: {
        styleOverrides: {
          tooltip: {
            backgroundColor: 'var(--color-text-default)', // Example: Dark tooltip background
            color: 'var(--color-white)', // Example: Light tooltip text
            fontSize: '0.8rem',
            borderRadius: 'var(--radius-sm)',
          },
        },
      },
      
      MuiDialog: {
        styleOverrides: {
          paper: {
            backgroundColor: 'var(--theme-background-secondary)',
            color: 'var(--color-text-default)',
            borderRadius: 'var(--card-border-radius)',
            boxShadow: 'var(--card-shadow)',
          },
        },
      },
      MuiDialogTitle: {
        styleOverrides: {
          root: {
            color: 'var(--color-text-heading)',
          },
        },
      },
      MuiSelect: {
      styleOverrides: {
        // Styles applied to the root element.
        root: {
          // General styling for the select container
          // You might target specific variants like 'outlined', 'filled', 'standard' here if needed
          // For now, these apply broadly.
          borderRadius: 'var(--select-border-radius, 8px)',
          backgroundColor: 'var(--select-bg, #f0f0f0)',
          color: 'var(--select-text-color, rgba(0, 0, 0, 0.87))',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'var(--select-hover-border-color, rgba(0, 0, 0, 0.87))', // For outlined variant
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: 'var(--select-focus-border-color, #1976d2)', // For outlined variant when focused
          },
        },
        // Styles applied to the select input element.
        select: {
          padding: 'var(--select-padding, 12px 14px)', // Adjust padding inside the select box
          '&:focus': {
            backgroundColor: 'var(--select-focus-bg, transparent)', // Remove default blue background on focus
            borderRadius: 'var(--select-border-radius, 8px)', // Ensure border-radius on focus
          },
        },
        // Styles applied to the icon component.
        icon: {
          color: 'var(--select-icon-color, rgba(0, 0, 0, 0.54))',
        },
        // Styles applied to the root element if `variant="outlined"`.
        outlined: {
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'var(--select-border-color, rgba(0, 0, 0, 0.23))',
          },
        },
        // Styles applied to the root element if `variant="filled"`.
        filled: {
          backgroundColor: 'var(--select-filled-bg, rgba(0, 0, 0, 0.09))',
          '&:hover': {
            backgroundColor: 'var(--select-filled-hover-bg, rgba(0, 0, 0, 0.13))',
          },
          '&.Mui-focused': {
            backgroundColor: 'var(--select-filled-focus-bg, rgba(0, 0, 0, 0.09))',
          },
        },
        // Styles applied to the root element if `variant="standard"`.
        standard: {
          '&:before': {
            borderColor: 'var(--select-standard-border-color, rgba(0, 0, 0, 0.42))',
          },
          '&:hover:not(.Mui-disabled):before': {
            borderColor: 'var(--select-standard-hover-border-color, rgba(0, 0, 0, 0.87))',
          },
          '&.Mui-focused:before': {
            borderColor: 'var(--select-standard-focus-border-color, #1976d2)',
          },
        },
      },
    },
    // --- MENUITEM OVERRIDES START (for the dropdown options) ---
    MuiMenuItem: {
      styleOverrides: {
        // Styles applied to the root element.
        root: {
          backgroundColor: 'var(--menuitem-bg, #fff)',
          color: 'var(--menuitem-text-color, rgba(0, 0, 0, 0.87))',
          '&:hover': {
            backgroundColor: 'var(--menuitem-hover-bg, rgba(0, 0, 0, 0.04))',
          },
          '&.Mui-selected': {
            backgroundColor: 'var(--menuitem-selected-bg, rgba(25, 118, 210, 0.08))', // Default MUI primary light
            color: 'var(--menuitem-selected-text-color, #1976d2)', // Default MUI primary
            '&:hover': {
              backgroundColor: 'var(--menuitem-selected-hover-bg, rgba(25, 118, 210, 0.12))',
            },
          },
        },
      },
    },
      MuiLinearProgress: {
        styleOverrides: {
          colorPrimary: {
            backgroundColor: 'var(--theme-border-color)',
            '& .MuiLinearProgress-barColorPrimary': {
              backgroundColor: 'var(--color-primary-brand)',
            },
          },
          colorSecondary: {
            backgroundColor: 'var(--theme-border-color)',
            '& .MuiLinearProgress-barColorSecondary': {
              backgroundColor: 'var(--color-secondary-brand)',
            },
          },
        },
      },
      MuiCircularProgress: {
        styleOverrides: {
          colorPrimary: {
            color: 'var(--color-primary-brand)',
          },
          colorSecondary: {
            color: 'var(--color-secondary-brand)',
          },
        },
      },
      MuiSkeleton: {
        styleOverrides: {
          root: {
            backgroundColor: 'rgba(0, 0, 0, 0.11)', // Default skeleton color
          },
        },
      },
      MuiSnackbarContent: { // For Snackbar content styling
        styleOverrides: {
          root: {
            backgroundColor: 'var(--color-text-default)', // Dark background for snackbar
            color: 'var(--color-white)', // White text for snackbar
            borderRadius: 'var(--radius-sm)',
          },
        },
      },
      MuiBackdrop: {
        styleOverrides: {
          root: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)', // Default backdrop color
          },
        },
      },
      MuiBottomNavigation: {
        styleOverrides: {
          root: {
            backgroundColor: 'var(--navbar-bg)', // Example: using navbar background
            '& .MuiBottomNavigationAction-root': {
              color: 'var(--navbar-text-color)',
              '&.Mui-selected': {
                color: 'var(--color-accent-brand)', // Highlight selected item
              },
            },
          },
        },
      },
      MuiBreadcrumbs: {
        styleOverrides: {
          li: {
            color: 'var(--color-text-muted)',
            '& .MuiLink-root': {
              color: 'var(--color-text-link)',
            },
          },
        },
      },
      MuiStepper: {
        styleOverrides: {
          root: {
            backgroundColor: 'transparent', // Match background
          },
        },
      },
      MuiStepLabel: {
        styleOverrides: {
          label: {
            color: 'var(--color-text-default)',
            '&.Mui-active': {
              color: 'var(--color-primary-brand)',
              fontWeight: 'var(--font-weight-medium)',
            },
            '&.Mui-completed': {
              color: 'var(--color-status-success)',
            },
          },
          iconContainer: {
            '& .MuiSvgIcon-root': {
              color: 'var(--color-text-muted)', // Default icon color
              '&.Mui-active': {
                color: 'var(--color-primary-brand)',
              },
              '&.Mui-completed': {
                color: 'var(--color-status-success)',
              },
            },
          },
        },
      },
    },
  });
};
