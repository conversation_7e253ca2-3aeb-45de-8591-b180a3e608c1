import React, { useState } from 'react';
import { MdSearch, MdStar, MdGridView, MdBusinessCenter, MdLayers, MdBusiness } from 'react-icons/md';
import { industryIconList } from '../../data/industryIconMap';
// import './SidebarAdvancedFilter.css';
import DynamicIcon from '../common/DynamicIcon';


const iconImages = import.meta.glob('../../assets/industry-icons/*.png', { eager: true, as: 'url' });

// Utility to normalize industry names (remove invisible/special whitespace)
function normalizeIndustryName(name) {
  return name.replace(/[\u200B-\u200D\uFEFF\u00A0\u001F]/g, '').trim();
}

const SidebarAdvancedFilter = ({
  open,
  onClose,
  search,
  setSearch,
  specialViews,
  selectedSpecialView,
  setSelectedSpecialView,
  functionalAreas,
  selectedFunctionalArea,
  setSelectedFunctionalArea,
  industries,
  selectedIndustries,
  setSelectedIndustries,
  solutionType,
  setSolutionType,
  onClearAll,
  resultCount,
  onFilterApply,
}) => {
  // Responsive: overlay and close only on mobile
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 900;

  const handleApply = () => {
    if (onFilterApply) onFilterApply();
  };

  // Industry search state
  const [industrySearch, setIndustrySearch] = useState('');
  const filteredIndustries = industries.filter(ind =>
    ind.toLowerCase().includes(industrySearch.toLowerCase())
  ).sort((a, b) => a.localeCompare(b));

  // Special Views (tags) search
  const [specialViewSearch, setSpecialViewSearch] = useState('');
  const filteredSpecialViews = specialViews.filter(view =>
    view.toLowerCase().includes(specialViewSearch.toLowerCase())
  ).sort((a, b) => a.localeCompare(b));

  // Functional Areas (categories) search
  const [functionalAreaSearch, setFunctionalAreaSearch] = useState('');
  const filteredFunctionalAreas = functionalAreas.filter(area =>
    area.toLowerCase().includes(functionalAreaSearch.toLowerCase())
  ).sort((a, b) => a.localeCompare(b));

  return (
    <>
      {isMobile && open && <div className="sidebar-overlay" onClick={onClose} />}
      <aside className={`sidebar-advanced-filter${open ? ' open' : ''}${isMobile ? ' mobile' : ' desktop'}`}>
        <div className="sidebar-header">
          <h3><DynamicIcon name="widgets" className="sidebar-header-icon" /> Filters</h3>
          {isMobile && <button className="sidebar-close-btn" onClick={onClose}>&times;</button>}
        </div>
        <div className="sidebar-content">
          <div className="filter-group">
            <div className="filter-label"><MdSearch className="filter-icon" /> Search Solutions...</div>
            <input className="filter-search" type="text" placeholder="Search Solutions..." value={search} onChange={e => setSearch(e.target.value)} onBlur={handleApply} />
          </div>
          <div className="filter-group">
            <div className="filter-label"><MdStar className="filter-icon" /> Special Views</div>
            <input className="filter-search" type="text" placeholder="Search special views..." value={specialViewSearch} onChange={e => setSpecialViewSearch(e.target.value)} />
            <div className="filter-links">
              {filteredSpecialViews.map(view => (
                <button key={view} className={`filter-link${selectedSpecialView === view ? ' selected' : ''}`} onClick={() => { setSelectedSpecialView(view); handleApply(); }}>{view}</button>
              ))}
            </div>
          </div>
          <div className="filter-group">
            <div className="filter-label"><MdGridView className="filter-icon" /> Functional Areas</div>
            <input className="filter-search" type="text" placeholder="Search functional areas..." value={functionalAreaSearch} onChange={e => setFunctionalAreaSearch(e.target.value)} />
            <div className="filter-links">
              {filteredFunctionalAreas.map(area => (
                <button key={area} className={`filter-link${selectedFunctionalArea === area ? ' selected' : ''}`} onClick={() => { setSelectedFunctionalArea(area); handleApply(); }}>{area}</button>
              ))}
            </div>
          </div>
          <div className="filter-group">
            <div className="filter-label"><MdBusinessCenter className="filter-icon" /> Industries</div>
            <input className="industry-search-box" type="text" placeholder="Search industries..." value={industrySearch} onChange={e => setIndustrySearch(e.target.value)} />
            <div className="industry-list">
              {filteredIndustries.map(ind => {
                const entry = industryIconList.find(item => item.name === ind);
                const iconFile = entry ? entry.icons : null;
                const imgUrl = iconFile && iconImages[`../../assets/industry-icons/${iconFile}`]
                  ? iconImages[`../../assets/industry-icons/${iconFile}`]
                  : null;
                return (
                  <label key={ind} className="industry-checkbox-label">
                    <input type="checkbox" checked={selectedIndustries.includes(ind)} onChange={() => {
                      setSelectedIndustries(selectedIndustries.includes(ind)
                        ? selectedIndustries.filter(i => i !== ind)
                        : [...selectedIndustries, ind]);
                      handleApply();
                    }} />
                    <span
                      className="industry-image-thumb"
                      style={{
                        '--industry-thumb-url': imgUrl ? `url(${imgUrl})` : 'none',
                      }}
                    />
                    <span className="industry-label-text">{ind}</span>
                  </label>
                );
              })}
            </div>
          </div>
          <div className="filter-group">
            <div className="filter-label"><MdLayers className="filter-icon" /> Solution Type</div>
            <div className="solution-type-list">
              {['all', 'bundles', 'modules'].map(type => (
                <label key={type} className="solution-type-radio-label">
                  <input type="radio" checked={solutionType === type} onChange={() => { setSolutionType(type); handleApply(); }} />
                  {type === 'all' ? 'All' : type.charAt(0).toUpperCase() + type.slice(1)}
                </label>
              ))}
            </div>
          </div>
        </div>
        <div className="sidebar-footer">
          <button className="clear-filters-btn" onClick={() => { onClearAll(); handleApply(); }}>Clear All</button>
          <span className="result-count">{resultCount} Result(s)</span>
        </div>
      </aside>
    </>
  );
};

export default SidebarAdvancedFilter; 