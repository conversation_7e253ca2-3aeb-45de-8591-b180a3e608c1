import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    ToggleButton, Avatar, FormControlLabel, Menu, MenuItem, Drawer,
    Chip, FormControl, InputLabel, Select
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, MoreVert, Close, SearchOff,
    <PERSON><PERSON>hart, <PERSON>hart, DonutLarge, Settings
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialBillingCycles = [
    { id: 1, billingId: 'BILL-001', cycle: 'Monthly', status: 'Active', amount: '$1000', nextBilling: '2024-07-01' },
    { id: 2, billingId: 'BILL-002', cycle: 'Quarterly', status: 'Pending', amount: '$2500', nextBilling: '2024-09-01' },
    { id: 3, billingId: 'BILL-003', cycle: 'Yearly', status: 'Expired', amount: '$9000', nextBilling: '2023-12-01' },
];

const ALL_COLUMNS = [
    { key: 'billingId', label: 'Billing ID', type: 'string' },
    { key: 'cycle', label: 'Cycle', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'amount', label: 'Amount', type: 'string' },
    { key: 'nextBilling', label: 'Next Billing', type: 'string' },
];

const ActionButtons = ({ billing, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(billing, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(billing, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([billing.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);


const BillingTable = ({ billings, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < billings.length} checked={billings.length > 0 && selectedIds.length === billings.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {billings.map(b => (
                        <TableRow key={b.id} hover selected={selectedId === b.id} onClick={() => onRowClick(b)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(b.id)} onChange={() => onSelectOne(b.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{b[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons billing={b} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const BillingCard = ({ billing, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(billing.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons billing={billing} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{billing.cycle}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{billing.amount}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: billing.status }} label={billing.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Next Billing:</strong> {billing.nextBilling}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const BillingCompactCard = ({ billing, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(billing.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons billing={billing} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{billing.cycle}</Typography>
                <Typography variant="caption" color="text.secondary">{billing.amount}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{billing.status}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: billing.status }} label={billing.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const BillingListItem = ({ billing, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(billing.id)} />
            <Box>
                <Typography fontWeight="bold">{billing.cycle}</Typography>
                <Typography variant="body2" color="text.secondary">{billing.amount}</Typography>
            </Box>
            <Typography variant="body2">{billing.status}</Typography>
            <Typography variant="body2">{billing.nextBilling}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: billing.status }} label={billing.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons billing={billing} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const BillingGraph = ({ billing, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && billing) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Amount'],
                    datasets: [{
                        label: `Billing Amount (${billing.cycle})`,
                        data: [parseFloat(billing.amount.replace(/[^0-9.-]+/g, ""))],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${billing.cycle} - Billing Amount` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [billing, chartType]);

    return (
        <>
            {billing ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a billing cycle to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Billing = () => {
    // --- State for sidebar and search/settings logic ---
    const [billings, setBillings] = useState(initialBillingCycles);
    const [selectedBilling, setSelectedBilling] = useState(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [viewMode, setViewMode] = useState('cards');
    const [sortColumn, setSortColumn] = useState('billingId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [isGraphVisible, setIsGraphVisible] = useState(false);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new billing cycle', target: 'BILL-001', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Activated billing cycle', target: 'BILL-002', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Finance', action: 'Expired billing cycle', target: 'BILL-003', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    // Add chartType state for graph type selection
    const [chartType, setChartType] = useState('bar');

    // Advanced Search State (copied from Customers.jsx, adapted for billing fields)
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });

    const quickFilterOptions = useMemo(() => {
        const cycles = [...new Set(billings.map(b => b.cycle))];
        const statuses = [...new Set(billings.map(b => b.status))];
        return [...statuses, ...cycles];
    }, [billings]);

    const processedBillings = useMemo(() => {
        let current = billings;
        if (filter !== 'all') current = current.filter(b => b.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(b => b.billingId.toLowerCase().includes(term) || b.cycle.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(billing => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const billingValue = String(billing[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return billingValue === filterValue;
                        case 'Not Equals': return billingValue !== filterValue;
                        case 'Contains': return billingValue.includes(filterValue);
                        case 'Starts With': return billingValue.startsWith(filterValue);
                        case 'Ends With': return billingValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [billings, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const summaryStats = useMemo(() => ({
        total: billings.length,
        active: billings.filter(b => b.status === 'Active').length,
        expired: billings.filter(b => b.status === 'Expired').length,
        pending: billings.filter(b => b.status === 'Pending').length,
    }), [billings]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (billing) => setSelectedBilling(billing);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedBillings.map(b => b.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    // --- Advanced Search Handlers (copied and adapted) ---
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleAddQuickFilter = (value) => {
        const field = ['Active', 'Pending', 'Expired'].includes(value) ? 'status' : 'cycle';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };

    const displayBilling = useMemo(() => {
        const isSelectedVisible = processedBillings.some(b => b.id === selectedBilling?.id);
        if (isSelectedVisible) return selectedBilling;
        return processedBillings.length > 0 ? processedBillings[0] : null;
    }, [processedBillings, selectedBilling]);

    // --- Sidebar logic (search/settings) ---
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        if (willBeOpen) {
            setIsGraphVisible(false);
        }
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };

    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
        const colLabel = ALL_COLUMNS.find(c => c.key === columnKey)?.label || columnKey;
        setActivityLog(prev => [{ user: 'Admin', action: `toggled '${colLabel}' column visibility`, timestamp: new Date().toLocaleString() }, ...prev].slice(0, 10));
    };

    // --- Render ---
    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedBillings.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedBillings.map(b => <BillingCard key={b.id} billing={b} onClick={() => setSelectedBilling(b)} isSelected={displayBilling?.id === b.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(b.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <BillingTable
                            billings={processedBillings}
                            onRowClick={setSelectedBilling}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displayBilling?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedBillings.map(b => <BillingCompactCard key={b.id} billing={b} onClick={() => setSelectedBilling(b)} isSelected={displayBilling?.id === b.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(b.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedBillings.map(b => <BillingListItem key={b.id} billing={b} onClick={() => setSelectedBilling(b)} isSelected={displayBilling?.id === b.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(b.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Billing Cycles</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><BarChart /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'active'} onClick={() => setFilter('active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expired'} onClick={() => setFilter('expired')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expired}</Typography><Typography variant="body2">Expired</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'pending'} onClick={() => setFilter('pending')}>
                                        <AdminComponents.SummaryAvatar variant="active"><BarChart /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.pending}</Typography><Typography variant="body2">Pending</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add Billing Cycle</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedBillings.length > 0 && selectedIds.length === processedBillings.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedBillings.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<Settings />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <BillingGraph billing={displayBilling} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
            <Drawer
                variant="persistent"
                anchor="right"
                open={isSidebarOpen}
            >
                <AdminComponents.SidebarContainer>
                    <AdminComponents.SidebarHeader>
                        <Typography variant="h6">
                            {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                        </Typography>
                        <IconButton onClick={() => setIsSidebarOpen(false)}>
                            <Close />
                        </IconButton>
                    </AdminComponents.SidebarHeader>
                    <AdminComponents.SidebarContent>
                        {sidebarMode === 'search' && (
                            <>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.QuickFilterContainer>
                                        {quickFilterOptions.map(opt => (
                                            <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                        ))}
                                    </AdminComponents.QuickFilterContainer>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                    <FormControl fullWidth size="small">
                                        <InputLabel>Field</InputLabel>
                                        <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                            {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                        </Select>
                                    </FormControl>
                                    <FormControl fullWidth size="small">
                                        <InputLabel>Operator</InputLabel>
                                        <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                            {['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'].map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                        </Select>
                                    </FormControl>
                                    <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                    <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.FilterChipContainer>
                                        {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                            <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                        )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                    </AdminComponents.FilterChipContainer>
                                </AdminComponents.SidebarSection>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.FilterChipContainer>
                                        {activeFilters.length > 0 ? activeFilters.map(f => (
                                            <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                        )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                    </AdminComponents.FilterChipContainer>
                                    <Button variant="outlined" fullWidth onClick={handleApplyFilters} sx={{ mt: 1 }}>Apply Filters</Button>
                                    <Button variant="outlined" fullWidth onClick={handleResetFilters} sx={{ mt: 1 }}>Reset Filters</Button>
                                </AdminComponents.SidebarSection>
                            </>
                        )}
                        {sidebarMode === 'grid' && (
                            <>
                                <AdminComponents.SidebarSection>
                                    <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                    <AdminComponents.ColumnActionContainer>
                                        <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                        <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                    </AdminComponents.ColumnActionContainer>
                                    <AdminComponents.ColumnVisibilityContainer>
                                        {ALL_COLUMNS.map(col => (
                                            <FormControlLabel
                                                key={col.key}
                                                control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                label={col.label}
                                            />
                                        ))}
                                    </AdminComponents.ColumnVisibilityContainer>
                                </AdminComponents.SidebarSection>
                            </>
                        )}
                    </AdminComponents.SidebarContent>
                </AdminComponents.SidebarContainer>
            </Drawer>
        </ThemeProvider>
    );
};

export default Billing; 