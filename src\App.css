/* fonts.css */
/* HCLTechRoobert-Light */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

/* HCLTechRoobert-Regular */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

/* HCLTechRoobert-Medium */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
}

/* HCLTechRoobert-Bold */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* HCLTechRoobert-ExtraBold */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
}

/* HCLTechRoobert-Heavy */
@font-face {
    font-family: 'HCLTechRoobert';
    src: url('./assets/HCLTech Roobert_font/WOFF2/HCLTechRoobert-Heavy.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
}

body {
  font-family: 'HCLTechRoobert', Arial, sans-serif;
}

/* === Imports & Variables === */
/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'); */

:root {
  --background-primary: #F3F4F6;
  --background-secondary: #FFFFFF;
  --border-color: #E5E7EB;
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --accent-primary: #2ec0cb; /* Teal 40 */
  --accent-primary-hover: #1db6c0; /* Teal 50 */
  --status-active-bg: #d1fafd; /* Teal 10 */
  --status-active-text: #0E7490;
  --status-expiring-bg: #FEFCE8;
  --status-expiring-text: #713F12;
  --status-expired-bg: #FEE2E2;
  --status-expired-text: #991B1B;
  --status-pending-bg: #FFFBEB;
  --status-pending-text: #B45309;
  --status-completed-bg: #EFF6FF;
  --status-completed-text: #1D4ED8;
  --status-inprogress-bg: #F0F9FF;
  --status-inprogress-text: #026AA2;
  --status-scheduled-bg: #FDF2F8;
  --status-scheduled-text: #9D174D;
  /* --font-sans: 'Inter', sans-serif;
  --font-roobert: 'HCL Roobert', sans-serif; */
}

/* === Global Styles === */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  /* font-family: var(--font-sans); */
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* === Layout === */
.app-container {
  display: flex;
  height: 100vh;
}

.main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* === Sidebar === */
.sidebar {
  width: 260px;
  background-color: var(--background-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  height: 100vh;
  min-height: 100vh;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.collapsed .sidebar-header h1,
.sidebar.collapsed .sidebar-nav a span,
.sidebar.collapsed .sidebar-footer .toggle-text {
  display: none;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.sidebar-header h1 {
  margin: 0;
  /* font-family: var(--font-roobert); */
  font-size: 24px;
  color: var(--accent-primary-hover);
  font-weight: 700;
}

.sidebar-nav {
  flex-grow: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  margin-bottom: 4px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
  white-space: nowrap;
  font-size: 16px;
}

.sidebar-nav a:hover {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.sidebar-nav a.active {
  background-color: var(--accent-primary);
  color: #fff;
  font-weight: 600;
}

.sidebar-nav a svg {
  flex-shrink: 0;
  font-size: 22px;
  margin-right: 16px;
  min-width: 22px;
  min-height: 22px;
  max-width: 22px;
  max-height: 22px;
  display: inline-block;
}

.sidebar-nav a span {
  display: inline;
  opacity: 1;
  margin-left: 0;
}

.sidebar.collapsed .sidebar-nav a {
  justify-content: center;
  padding: 16px 0;
  margin-bottom: 8px;
}

.sidebar.collapsed .sidebar-nav a svg {
  margin-right: 0;
}

.sidebar.collapsed .sidebar-header {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar.collapsed .sidebar-footer {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar.collapsed .sidebar-toggle {
  width: 44px;
  height: 44px;
}

.sidebar-footer {
  padding: 24px 0;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  min-height: 80px;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--background-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 22px;
  margin: 0 auto;
} 
 /* AdmindashboardIndex.css */
 /* === Imports & Variables === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.admin-dashboard {
  /* Dashboard-specific root variables */
  --background-primary: #F3F4F6;
  --background-secondary: #FFFFFF;
  --border-color: #E5E7EB;
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --accent-primary: #2ec0cb; /* Teal 40 */
  --accent-primary-hover: #1db6c0; /* Teal 50 */
  --status-active-bg: #d1fafd; /* Teal 10 */
  --status-active-text: #0E7490;
  --status-expiring-bg: #FEFCE8;
  --status-expiring-text: #713F12;
  --status-expired-bg: #FEE2E2;
  --status-expired-text: #991B1B;
  --status-pending-bg: #FFFBEB;
  --status-pending-text: #B45309;
  --status-completed-bg: #EFF6FF;
  --status-completed-text: #1D4ED8;
  --status-inprogress-bg: #F0F9FF;
  --status-inprogress-text: #026AA2;
  --status-scheduled-bg: #FDF2F8;
  --status-scheduled-text: #9D174D;
  /* --font-sans: 'Inter', sans-serif;
  --font-roobert: 'HCL Roobert', sans-serif; */
}

.admin-dashboard * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.admin-dashboard {
  /* font-family: var(--font-sans); */
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.admin-dashboard .app-container {
  display: flex;
  height: 100vh;
}

.admin-dashboard .main-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.admin-dashboard .content-area {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.admin-dashboard .sidebar {
  width: 260px;
  background-color: var(--background-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  height: 100vh;
  min-height: 100vh;
}

.admin-dashboard .sidebar.collapsed {
  width: 70px;
}

.admin-dashboard .sidebar.collapsed .sidebar-header h1,
.admin-dashboard .sidebar.collapsed .sidebar-nav a span,
.admin-dashboard .sidebar.collapsed .sidebar-footer .toggle-text {
  display: none;
}

.admin-dashboard .sidebar-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.admin-dashboard .sidebar-header h1 {
  margin: 0;
  /* font-family: var(--font-roobert); */
  font-size: 24px;
  color: var(--accent-primary-hover);
  font-weight: 700;
}

.admin-dashboard .sidebar-nav {
  flex-grow: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.admin-dashboard .sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  margin-bottom: 4px;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
  white-space: nowrap;
  font-size: 16px;
}

.admin-dashboard .sidebar-nav a:hover {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

.admin-dashboard .sidebar-nav a.active {
  background-color: var(--accent-primary);
  color: #fff;
  font-weight: 600;
}

.admin-dashboard .sidebar-nav a svg {
  flex-shrink: 0;
  font-size: 22px;
  margin-right: 16px;
  min-width: 22px;
  min-height: 22px;
  max-width: 22px;
  max-height: 22px;
  display: inline-block;
}

.admin-dashboard .sidebar-nav a span {
  display: inline;
  opacity: 1;
  margin-left: 0;
}

.admin-dashboard .sidebar.collapsed .sidebar-nav a {
  justify-content: center;
  padding: 16px 0;
  margin-bottom: 8px;
}

.admin-dashboard .sidebar.collapsed .sidebar-nav a svg {
  margin-right: 0;
}

.admin-dashboard .sidebar.collapsed .sidebar-header {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.admin-dashboard .sidebar.collapsed .sidebar-footer {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.admin-dashboard .sidebar.collapsed .sidebar-toggle {
  width: 44px;
  height: 44px;
}

.admin-dashboard .sidebar-footer {
  padding: 24px 0;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  min-height: 80px;
}

.admin-dashboard .sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--background-secondary);
  border: 1.5px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 22px;
  margin: 0 auto;
}

.admin-dashboard .sidebar-toggle:hover {
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

/* === Main Layout === */
.admin-dashboard .main-layout {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #F3F4F6;
  transition: margin-left 0.3s ease;
}

.admin-dashboard .top-bar-container {
    background-color: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.admin-dashboard .top-bar {
  height: 81px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background-color: var(--background-secondary);
  flex-shrink: 0;
}

.admin-dashboard .top-bar-search {
    position: relative;
    display: flex;
    align-items: center;
}

.admin-dashboard .top-bar-search .icon {
    position: absolute;
    left: 16px;
    font-size: 20px;
    color: var(--text-secondary);
}

.admin-dashboard .top-bar-search .input {
    width: 320px;
    padding: 10px 16px 10px 48px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--background-secondary);
    font-size: 14px;
}
.admin-dashboard .top-bar-search .input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.admin-dashboard .top-bar-actions {
    display: flex;
    align-items: center;
    gap: 24px;
    height: 44px;
}

.admin-dashboard .icon-button {
  position: relative;
  background: none;
  border: none;
  padding: 0;
  margin: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  width: 44px;
  cursor: pointer;
  font-size: 22px;
}

.admin-dashboard .top-bar-actions .profile-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
}

.admin-dashboard .content {
  padding: 32px;
  overflow-y: auto;
}

.admin-dashboard .view-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.admin-dashboard .view-header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.admin-dashboard .view-toggle {
    display: flex;
    background-color: var(--background-primary);
    border-radius: 8px;
    padding: 4px;
}

.admin-dashboard .view-toggle button {
    background: none;
    border: none;
    padding: 6px 10px;
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: 6px;
}

.admin-dashboard .view-toggle button.active {
    background-color: var(--background-secondary);
    color: var(--text-primary);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.admin-dashboard .billing-cards-container {
    display: grid;
    gap: 24px;
    margin-bottom: 32px;
}

.admin-dashboard .billing-cards-container.grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

.admin-dashboard .billing-cards-container.list {
    grid-template-columns: 1fr;
}

.admin-dashboard .billing-card {
    background-color: var(--background-secondary);
    border-radius: 8px;
    padding: 24px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.admin-dashboard .billing-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

.admin-dashboard .billing-card h3 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: var(--text-primary);
}

.admin-dashboard .billing-card a {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 600;
}

.admin-dashboard .view-header h1 {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: #111827;
}

.admin-dashboard .add-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: var(--accent-primary);
    color: white;
    border: 1px solid var(--accent-primary);
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.admin-dashboard .add-button:hover {
    background-color: var(--accent-primary-hover);
    border-color: var(--accent-primary-hover);
}

.admin-dashboard .content-card {
  background-color: var(--background-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.admin-dashboard .table-container {
  overflow-x: auto;
}

.admin-dashboard .data-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
}

.admin-dashboard .data-table th, .admin-dashboard .data-table td {
  text-align: left;
  vertical-align: middle;
  white-space: nowrap;
  padding: 14px 18px;
  font-size: 15px;
  min-width: 120px;
}

.admin-dashboard .data-table th {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border-color);
  background: #fafbfc;
  display: table-cell;
  align-items: center;
  gap: 8px;
}

.admin-dashboard .data-table td {
  background: #fff;
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
  display: table-cell;
}

.admin-dashboard .data-table th svg {
  font-size: 16px;
  margin-right: 4px;
  vertical-align: middle;
}

.admin-dashboard .data-table td .status-pill,
.admin-dashboard .data-table td .type-pill {
  margin: 0;
}

.admin-dashboard .data-table td .cell-main {
  font-weight: 700;
  font-size: 15px;
  color: var(--text-primary);
}

.admin-dashboard .data-table td .cell-sub {
  font-size: 13px;
  color: var(--text-secondary);
}

.admin-dashboard .data-table td .validity-pill,
.admin-dashboard .data-table td .validity-bar-bg {
  margin-top: 2px;
}

.admin-dashboard .data-table td .value-main {
  font-size: 16px;
  font-weight: 700;
}

.admin-dashboard .data-table tbody tr:last-child td {
  border-bottom: none;
}

.admin-dashboard .action-buttons button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 4px;
  font-size: 16px;
  margin-right: 16px;
}

.admin-dashboard .action-buttons button:hover {
  color: var(--accent-primary);
}

/* === Status Pills === */
.admin-dashboard .status-pill {
    padding: 2px 10px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 12px;
    display: inline-block;
}

.admin-dashboard .status-pill.active, .admin-dashboard .status-pill.approved {
    background-color: var(--status-active-bg);
    color: var(--status-active-text);
}

.admin-dashboard .status-pill.expiring-soon {
    background-color: var(--status-expiring-bg);
    color: var(--status-expiring-text);
}

.admin-dashboard .status-pill.expired {
    background-color: var(--status-expired-bg);
    color: var(--status-expired-text);
}

.admin-dashboard .status-pill.pending {
    background-color: var(--status-pending-bg);
    color: var(--status-pending-text);
}

.admin-dashboard .status-pill.completed, .admin-dashboard .status-pill.shipped {
    background-color: var(--status-completed-bg);
    color: var(--status-completed-text);
}

.admin-dashboard .status-pill.in-progress {
    background-color: var(--status-inprogress-bg);
    color: var(--status-inprogress-text);
}

.admin-dashboard .status-pill.scheduled {
    background-color: var(--status-scheduled-bg);
    color: var(--status-scheduled-text);
}

/* === Pagination === */
.admin-dashboard .pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  border-top: 1px solid var(--border-color);
  background-color: white;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.admin-dashboard .pagination button {
  background-color: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 0 8px;
  cursor: pointer;
  font-weight: 500;
}

.admin-dashboard .pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.admin-dashboard .pagination span {
    font-size: 14px;
    color: var(--text-secondary);
}

/* === Modal === */
.admin-dashboard .modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
.admin-dashboard .modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  width: 400px;
}
.admin-dashboard .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}
.admin-dashboard .modal-field {
  margin-bottom: 1rem;
}
.admin-dashboard .modal-field label {
  display: block;
  margin-bottom: 0.5rem;
}
.admin-dashboard .modal-field input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.admin-dashboard .modal-actions .btn-primary {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.admin-dashboard .modal-actions .btn-primary:hover {
    background-color: var(--accent-primary-hover);
    border-color: var(--accent-primary-hover);
}

.admin-dashboard .view-header-content h1 {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 28px;
    font-weight: 700;
    margin: 0;
}

.admin-dashboard .view-header-content p {
    margin: 4px 0 0 0;
    color: var(--text-secondary);
}

.admin-dashboard .create-button {
    background-color: var(--accent-primary);
    color: white;
}


.admin-dashboard .billing-cards-container {
    display: grid;
    gap: 24px;
    margin-bottom: 48px;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
}

.admin-dashboard .billing-card {
    background-color: var(--background-secondary);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.admin-dashboard .card-icon-background {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 24px;
}
.admin-dashboard .card-icon-background.blue { background-color: #3b82f6; }
.admin-dashboard .card-icon-background.green { background-color: #10b981; }
.admin-dashboard .card-icon-background.orange { background-color: #f97316; }
.admin-dashboard .card-icon-background.purple { background-color: #8b5cf6; }

.admin-dashboard .card-content h3 {
    margin: 0 0 4px 0;
    font-size: 20px;
}
.admin-dashboard .card-content p {
    margin: 0 0 16px 0;
    color: var(--text-secondary);
    font-size: 14px;
}
.admin-dashboard .card-stats {
    border-top: 1px solid var(--border-color);
    padding-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.admin-dashboard .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}
.admin-dashboard .stat-value {
    font-weight: 600;
}
.admin-dashboard .stat-value.revenue {
    color: #10b981;
    font-size: 16px;
}

.admin-dashboard .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.admin-dashboard .section-header h2 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 22px;
    margin: 0;
}

.admin-dashboard .section-header.admin-panel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    margin-top: 48px;
}

.admin-dashboard .export-button {
    background: none;
    border: 1px solid var(--border-color);
    padding: 8px 14px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.admin-dashboard .breadcrumbs {
    padding: 12px 24px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.admin-dashboard .breadcrumbs a {
    color: var(--text-secondary);
    text-decoration: none;
}

.admin-dashboard .breadcrumbs a:hover {
    color: var(--accent-primary);
}

.admin-dashboard .breadcrumbs span {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-weight: 500;
}

.admin-dashboard .breadcrumbs svg {
    color: var(--text-secondary);
}

.admin-dashboard .section-header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.admin-dashboard .view-toggle { display: flex; }

.admin-dashboard .grid-view-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.admin-dashboard .grid-view-card {
    background-color: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.admin-dashboard .grid-view-card h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
}

.admin-dashboard .grid-view-card p {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--text-secondary);
}

.admin-dashboard .grid-view-card .action-buttons {
    margin-top: 8px;
    border-top: 1px solid var(--border-color);
    padding-top: 12px;
}

/* === Contract Page Specifics === */

.admin-dashboard .summary-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 24px;
    margin: 32px 0;
}
.admin-dashboard .summary-card {
    background-color: var(--background-secondary);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
}
.admin-dashboard .icon-bg {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 22px;
    flex-shrink: 0;
}
.admin-dashboard .icon-bg.orange { background-color: #f97316; }
.admin-dashboard .icon-bg.cyan { background-color: #06b6d4; }
.admin-dashboard .icon-bg.blue { background-color: #3b82f6; }
.admin-dashboard .icon-bg.green { background-color: #10b981; }
.admin-dashboard .icon-bg.red { background-color: #ef4444; }
.admin-dashboard .icon-bg.gray { background-color: #6b7280; }

.admin-dashboard .summary-title {
    margin: 0 0 4px 0;
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}
.admin-dashboard .summary-value {
    font-size: 24px;
    font-weight: 700;
    margin-right: 8px;
}
.admin-dashboard .summary-trend {
    font-size: 13px;
    color: #10b981;
}

.admin-dashboard .status-overview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.admin-dashboard .status-card {
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
}
.admin-dashboard .status-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.08);
}
.admin-dashboard .status-card.active-border {
    border-color: var(--accent-primary);
}
.admin-dashboard .status-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}
.admin-dashboard .arrow-icon {
    color: var(--text-secondary);
}
.admin-dashboard .status-card-body h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
}
.admin-dashboard .status-card-body p {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0 0 16px 0;
}
.admin-dashboard .status-value {
    display: block;
    font-size: 28px;
    font-weight: 700;
}
.admin-dashboard .status-total-value {
    font-size: 13px;
    color: var(--text-secondary);
    margin-top: 4px;
}

/* === Vertical Table Layout === */
.admin-dashboard .vertical-table-layout,
.admin-dashboard .vertical-table-header,
.admin-dashboard .header-cell,
.admin-dashboard .vertical-table-body,
.admin-dashboard .body-row,
.admin-dashboard .body-cell {
  all: unset;
}

/* === Responsive Design === */

/* Tablet and smaller */
@media (max-width: 1024px) {
    .admin-dashboard .sidebar {
        position: absolute;
        z-index: 1000;
        height: 100%;
    }
    .admin-dashboard .sidebar.collapsed {
        transform: translateX(-100%);
    }
    .admin-dashboard .main-layout {
        margin-left: 0 !important;
    }
    .admin-dashboard .content {
        padding: 24px;
    }
    .admin-dashboard .top-bar-search .input {
        width: 250px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .admin-dashboard .app-container {
        flex-direction: column;
    }

    .admin-dashboard .sidebar {
        width: 260px; /* Full width when open on mobile */
    }

    .admin-dashboard .top-bar {
        flex-direction: column;
        height: auto;
        padding: 16px;
        gap: 16px;
    }

    .admin-dashboard .view-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    .admin-dashboard .view-header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .admin-dashboard .billing-cards-container.grid,
    .admin-dashboard .grid-view-container {
        grid-template-columns: 1fr;
    }

    .admin-dashboard .table-container {
        overflow-x: auto;
    }

    .admin-dashboard .data-table {
        min-width: 600px;
    }
}

.admin-dashboard .contract-table .cell-main {
  font-weight: 700;
  font-size: 15px;
  color: var(--text-primary);
}
.admin-dashboard .contract-table .cell-sub {
  font-size: 13px;
  color: var(--text-secondary);
}
.admin-dashboard .type-pill {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 12px;
  background: #f3f4f6;
}
.admin-dashboard .type-license { background: #ede9fe; color: #7c3aed; }
.admin-dashboard .type-support { background: #cffafe; color: #0891b2; }
.admin-dashboard .type-service { background: #d1fae5; color: #059669; }

.admin-dashboard .status-pill.status-active {
  background: #d1fae5;
  color: #059669;
  font-weight: 600;
  border-radius: 16px;
  padding: 4px 16px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.admin-dashboard .status-pill.status-expiring {
  background: #fee2e2;
  color: #b91c1c;
  font-weight: 600;
  border-radius: 16px;
  padding: 4px 16px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.admin-dashboard .status-pill.status-expired {
  background: #f3f4f6;
  color: #6b7280;
  font-weight: 600;
  border-radius: 16px;
  padding: 4px 16px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}
.admin-dashboard .validity-pill {
  display: inline-block;
  font-size: 13px;
  font-weight: 600;
  border-radius: 8px;
  padding: 4px 18px;
  margin-bottom: 4px;
}
.admin-dashboard .validity-pill.active { background: #d1fae5; color: #059669; }
.admin-dashboard .validity-pill.expiring { background: #fee2e2; color: #b91c1c; }
.admin-dashboard .validity-pill.expired { background: #f3f4f6; color: #6b7280; }
.admin-dashboard .validity-bar-bg {
  width: 100%;
  height: 5px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}
.admin-dashboard .validity-bar.active { background: #059669; height: 100%; }
.admin-dashboard .validity-bar.expiring { background: #b91c1c; height: 100%; }
.admin-dashboard .validity-bar.expired { background: #6b7280; height: 100%; }
.admin-dashboard .value-main { font-size: 16px; font-weight: 700; }

@media (max-width: 900px) {
  .admin-dashboard .data-table th, .admin-dashboard .data-table td {
    min-width: 100px;
    padding: 10px 8px;
    font-size: 13px;
  }
  .admin-dashboard .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 80vw;
    max-width: 320px;
    height: 100vh;
    z-index: 1100;
    transform: translateX(-100%);
    transition: transform 0.3s;
    box-shadow: 0 0 0 100vw rgba(0,0,0,0.15);
  }
  .admin-dashboard .sidebar.mobile-open {
    transform: translateX(0);
    box-shadow: 0 0 0 100vw rgba(0,0,0,0.25);
  }
  .admin-dashboard .sidebar-mobile-trigger {
    display: block;
  }
  .admin-dashboard .main-layout {
    margin-left: 0 !important;
  }
}

@media (min-width: 901px) {
  .admin-dashboard .sidebar-mobile-trigger {
    display: none;
  }
}

.admin-dashboard .sidebar-logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  /* font-family: var(--font-roobert); */
  font-size: 28px;
  font-weight: 900;
  color: var(--accent-primary-hover);
  height: 40px;
  width: 40px;
  margin: 0 auto;
  background: #e6fafd;
  border-radius: 12px;
}

.admin-dashboard .sidebar.collapsed .sidebar-nav a {
  justify-content: center;
  padding: 16px 0;
  margin-bottom: 8px;
}

.admin-dashboard .sidebar.collapsed .sidebar-nav a svg {
  margin-right: 0;
}

.admin-dashboard .sidebar.collapsed .sidebar-header {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.admin-dashboard .sidebar.collapsed .sidebar-footer {
  padding: 16px 0 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.admin-dashboard .sidebar.collapsed .sidebar-toggle {
  width: 44px;
  height: 44px;
}

.admin-dashboard .search-results-dropdown {
  position: absolute;
  top: 44px;
  left: 0;
  width: 100%;
  background: #fff;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  z-index: 1001;
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 220px;
  overflow-y: auto;
}
.admin-dashboard .search-results-dropdown li {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 15px;
  color: var(--text-primary);
  transition: background 0.15s;
}
.admin-dashboard .search-results-dropdown li:hover {
  background: var(--background-primary);
}

.admin-dashboard .notification-dropdown {
  position: absolute;
  top: 44px;
  right: 0;
  width: 320px;
  background: #fff;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  z-index: 1001;
  padding: 0;
}
.admin-dashboard .notification-header {
  font-weight: 700;
  font-size: 16px;
  padding: 14px 18px 8px 18px;
  border-bottom: 1px solid var(--border-color);
  background: #fafbfc;
}
.admin-dashboard .notification-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.admin-dashboard .notification-dropdown li {
  padding: 14px 18px;
  font-size: 15px;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}
.admin-dashboard .notification-dropdown li:last-child {
  border-bottom: none;
}

.admin-dashboard .notification-badge {
  position: absolute;
  top: 4px;
  right: 6px;
  min-width: 16px;
  height: 16px;
  background: #e11d48;
  color: #fff;
  font-size: 11px;
  font-weight: 700;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.10);
  border: 2px solid #fff;
  z-index: 2;
  pointer-events: none;
}


/* AuthModal.css */
.auth-modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  min-height: 100vh;
  min-width: 100vw;
}

.auth-modal-card {
  background: transparent;
  border-radius: 22px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.18);
  width: 960px;
  height: 600px;
  min-width: 320px;
  min-height: 400px;
  max-width: 98vw;
  max-height: 98vh;
  padding: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  overflow: hidden;
  animation: modalFadeIn 0.38s cubic-bezier(.4,1.2,.6,1);
}
@media (max-width: 1100px) {
  .auth-modal-card {
    width: 98vw;
    height: 98vh;
    min-width: 0;
    min-height: 0;
  }
}
.auth-modal-2col {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
}
.auth-modal-2col-inner {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
}
.auth-modal-2col-left {
  width: 50%;
  min-width: 320px;
  height: 100%;
  /* background: linear-gradient(120deg, #f7f9fc 60%, #eaf1fb 100%); */
  background: var(--secondary-teal-extra-light);
  border-radius: 22px 0 0 22px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.6rem 1.2rem 2.6rem 2.2rem;
  box-shadow: 2px 0 12px 0 rgba(0,86,214,0.04);
}
.auth-modal-2col-right {
  width: 50%;
  min-width: 320px;
  height: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2.6rem 2.2rem 2.6rem 1.2rem;
  position: relative;
}
.auth-modal-content {
  width: 80%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-modal-content-inner {
  width: 100%;
  max-width: 420px;
  min-height: 420px;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1 1 auto;
  margin: 0 auto;
  position: relative;
}
.auth-modal-tabs {
  display: flex;
  justify-content: center;
  gap: 3rem;
}
.auth-modal-tab-content-wrapper {
  position: relative;
  min-height: 420px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.signup-form, .login-form {
  margin-bottom: 1.5rem;
}
.social-login-options {
  margin-top: 1.2rem;
  margin-bottom: 0.5rem;
  width: 100%;
}
.social-login-row {
  display: flex;
  flex-direction: row;
  gap: 0;
  width: 100%;
}
.social-login-btn {
  flex: 1 1 0;
  width: 100%;
  max-width: none;
}
.auth-modal-switch-link {
  text-align: center;
  margin-top: 1.1rem;
  font-size: 0.98rem;
  color: #3a4a6b;
}
.auth-modal-switch-link span {
  color: var(--primary-color, #0056d6);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  margin-left: 0.2em;
  transition: color 0.18s;
}
.auth-modal-switch-link span:hover,
.auth-modal-switch-link span:focus {
  color: #1976f8;
  text-decoration: underline;
}
@media (max-width: 900px) {
  .auth-modal-card {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }
  .auth-modal-2col,
  .auth-modal-2col-inner {
    flex-direction: column;
  }
  .auth-modal-2col-left, .auth-modal-2col-right {
    width: 100%;
    min-width: 0;
    border-radius: 0;
    height: auto;
    min-height: 220px;
    padding: 1.2rem 1rem;
  }
}

.auth-modal-close {
  position: absolute;
  top: 24px;
  right: 32px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  z-index: 20;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auth-modal-close-bg {
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.18s;
}
.auth-modal-close:hover .auth-modal-close-bg,
.auth-modal-close:focus .auth-modal-close-bg {
  box-shadow: 0 4px 16px rgba(0,150,167,0.18);
}
.auth-modal-close svg {
  display: block;
  width: 28px;
  height: 28px;
  stroke: #0096a7;
}

.auth-modal-branding {
  text-align: center;
  width: 100%;
}
.auth-modal-branding .logo {
  margin-bottom: 1.2rem;
  /* max-width: 220px; */
  width: 100%;
  height: auto;
}
.auth-modal-headline {
  font-size: 1.35rem;
  font-weight: 700;
  color: #1a237e;
  margin-bottom: 1.1rem;
  margin-top: 0.2rem;
  letter-spacing: 0.01em;
}
.auth-modal-values {
  list-style: none;
  padding: 0;
  margin: 0;
  color: #3a4a6b;
  font-size: 1.04rem;
  font-weight: 400;
  line-height: 1.7;
  text-align: left;
  margin-top: 1.1rem;
}
.auth-modal-values li {
  margin-bottom: 0.4rem;
  display: flex;
  align-items: flex-start;
  gap: 0.7em;
}
.auth-modal-values li:before {
  content: '\2714';
  color: var(--primary-color, #00b6b6);
  font-size: 1.1em;
  font-weight: 700;
  margin-right: 0.4em;
  line-height: 1.2;
}

.auth-modal-tab {
  background: none;
  border: none;
  outline: none;
  font-size: 1.1rem;
  font-weight: 500;
  color: #222;
  padding: 0.5rem 0.75rem 0.25rem 0.75rem;
  cursor: pointer;
  position: relative;
  transition: color 0.18s;
  border-radius: 0;
}
.auth-modal-tab.active {
  font-weight: 700;
  color: var(--primary-color, #00b6b6);
}
.auth-modal-tab-underline {
  position: absolute;
  left: 0; right: 0; bottom: -2px;
  height: 3px;
  border-radius: 2px;
  background: var(--primary-color, #00b6b6);
  z-index: 2;
}
.auth-modal-tab-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0 0.5rem 0;
  margin-top: 0.75rem;
}

.social-login-btn.icon-only {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  box-shadow: none;
  width: 48px;
  height: 48px;
  min-width: 48px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.social-login-icon-circle {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  transition: none;
}
.social-login-btn.icon-only:hover .social-login-icon-circle,
.social-login-btn.icon-only:focus .social-login-icon-circle {
  box-shadow: 0 2px 8px rgba(0,182,182,0.18);
}
.social-login-img {
  width: 26px;
  height: 26px;
  object-fit: contain;
}

.forgot-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: rgb(26, 35, 126);
}
.forgot-desc {
  text-align: center;
}

.auth-link-text {
  color: var(--primary-teal-dark) !important;
  font-weight: 600;
  position: relative;
  cursor: pointer;
  transition: color 0.22s cubic-bezier(.4,1.2,.6,1);
}
.auth-link-text::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 0;
  height: 2px;
  background: var(--primary-teal-dark);
  border-radius: 1px;
  transition: width 0.28s cubic-bezier(.4,1.2,.6,1);
}
.auth-link-text:hover,
.auth-link-text:focus {
  color: var(--primary-teal);
}
.auth-link-text:hover::after,
.auth-link-text:focus::after {
  width: 100%;
}

.header-profile-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
}
.header-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 22px;
  padding: 0.2rem 0.9rem 0.2rem 0.2rem;
  transition: background 0.18s;
}
.header-profile:hover, .header-profile:focus {
  background: #e0f7fa;
}
.header-profile-photo {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.7rem;
  border: 2px solid var(--primary-teal, #0096a7);
  background: #fff;
}
.header-profile-name {
  font-weight: 600;
  color: var(--primary-teal-dark, #005e6a);
  font-size: 1.08rem;
  letter-spacing: 0.01em;
}
.header-profile-menu {
  position: absolute;
  top: 110%;
  right: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  min-width: 170px;
  z-index: 100;
  padding: 0.5rem 0;
  display: flex;
  flex-direction: column;
  animation: fadeInProfileMenu 0.22s cubic-bezier(.4,1.2,.6,1);
}
.header-profile-menu-item {
  padding: 0.7rem 1.2rem;
  color: #005e6a;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.16s, color 0.16s;
  border: none;
  background: none;
  text-align: left;
}
.header-profile-menu-item:hover, .header-profile-menu-item:focus {
  background: #e0f7fa;
  color: #0096a7;
}
@keyframes fadeInProfileMenu {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
} 

/* FabMenu.css */
.fab-menu {
  position: fixed;
  z-index: 1200;
  right: 2.5rem;
  bottom: 2.5rem;
  display: flex;
  flex-direction: column-reverse;
  align-items: flex-end;
  pointer-events: none;
}

.fab-main {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-teal), var(--primary-blue));
  color: var(--primary-white);
  border: none;
  box-shadow: 0 8px 32px rgba(46,192,203,0.25);
  font-size: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;
  pointer-events: auto;
}
.fab-main:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-teal));
  transform: scale(1.08);
}

.fab-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 1rem;
}

.fab-action {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
  pointer-events: none;
  margin-bottom: 1rem;
  width: 52px;
  height: 52px;
  border-radius: 50%;
  background: var(--primary-white);
  color: var(--primary-teal);
  border: none;
  box-shadow: 0 4px 16px rgba(46,192,203,0.15);
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: opacity 0.3s, transform 0.3s, background 0.2s, color 0.2s;
}

.fab-action:hover {
  background: var(--primary-teal);
  color: var(--primary-white);
}

.fab-menu.open .fab-action {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

.fab-tooltip {
  position: absolute;
  right: 110%;
  top: 50%;
  transform: translateY(-50%) scale(0.95);
  background: var(--primary-teal);
  color: var(--primary-white);
  padding: 0.4rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 2px 8px rgba(46,192,203,0.12);
}

.fab-action:hover .fab-tooltip {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.assistant-fab-main {
  position: fixed;
  left: 2.5rem;
  bottom: 2.5rem;
  z-index: 1200;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-teal), var(--primary-blue));
  color: var(--primary-white);
  border: none;
  box-shadow: 0 8px 32px rgba(46,192,203,0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.3s, transform 0.2s;
  pointer-events: auto;
}
.assistant-fab-main:hover {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-teal));
  transform: scale(1.08);
}

@media (max-width: 600px) {
  .fab-menu {
    right: 1rem;
    bottom: 1rem;
  }
  .fab-main {
    width: 52px;
    height: 52px;
    font-size: 1.5rem;
  }
  .fab-action {
    width: 44px;
    height: 44px;
    font-size: 1.1rem;
  }
  .assistant-fab-main {
    left: 1rem;
    bottom: 1rem;
    width: 52px;
    height: 52px;
  }
  .assistant-fab-main img {
    width: 28px;
    height: 28px;
  }
} 

/* SignUp.css */
.signup-form-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: center;
}
.signup-form {
  width: 100%;
  max-width: 420px;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  /* gap: 1.2rem; */
  margin: 0 auto;
}
.signup-fields {
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
}
.signup-field-row {
  width: 100%;
}
.floating-label-group {
  position: relative;
  width: 100%;
  margin-bottom: 0.2rem;
}
.floating-input {
  width: 100%;
  padding: 1.1rem 1rem 0.5rem 1rem;
  font-size: 1rem;
  border: 2px solid #e0e6ef;
  border-radius: 14px;
  background: #fff;
  outline: none;
  transition: border-color 0.22s;
  /* font-family: inherit; */
  box-shadow: none;
  position: relative;
  z-index: 1;
}
.floating-input:focus {
  border-color: var(--primary-color, #00b6b6);
}
.floating-label-group label {
  position: absolute;
  left: 1.1rem;
  top: 1.1rem;
  color: #6a7ba3;
  font-size: 1rem;
  font-weight: 600;
  pointer-events: none;
  background: #fff;
  padding: 0 0.3em 0 0.2em;
  border-radius: 6px;
  transition: all 0.18s cubic-bezier(.4,1.2,.6,1);
  z-index: 2;
  line-height: 1.1;
  display: flex;
  align-items: center;
}
.floating-label-group label .required {
  color: var(--primary-color, #00b6b6);
  font-weight: 700;
  margin-left: 0.15em;
}
.floating-input:focus + label,
.floating-label-group label.filled {
  top: -0.85rem;
  left: 0.9rem;
  font-size: 1.01rem;
  color: var(--primary-color, #00b6b6);
  font-weight: 700;
  background: #fff;
  padding: 0 0.5em 0 0.5em;
  z-index: 3;
}
.floating-label-group select + label {
  top: -0.85rem;
  left: 0.9rem;
  font-size: 1.01rem;
  color: var(--primary-color, #00b6b6);
  font-weight: 700;
  background: #fff;
  padding: 0 0.5em 0 0.5em;
  z-index: 3;
}
.input-border-anim {
  display: none;
}
.input-error-text {
  color: #e53935;
  font-size: 0.92rem;
  margin-top: 0.18rem;
  margin-left: 0.2rem;
  min-height: 1.1em;
  font-weight: 500;
  letter-spacing: 0.01em;
  transition: opacity 0.2s;
}
.signup-form-actions {
  display: flex;
  justify-content: flex-end;
  /* gap: 1rem; */
  margin-top: 0.5rem;
  margin-bottom: 0;
}
.password-strength-meter {
  width: 100%;
  height: 7px;
  background: #e0e6ef;
  border-radius: 4px;
  margin-top: 0.2rem;
  overflow: hidden;
}
.password-strength-bar {
  height: 100%;
  background: linear-gradient(90deg, #ffb347 0%, #4caf50 100%);
  border-radius: 4px;
  transition: width 0.32s cubic-bezier(.4,1.2,.6,1);
}
.signup-spinner {
  display: inline-block;
  width: 1.2em;
  height: 1.2em;
  border: 2.5px solid #fff;
  border-top: 2.5px solid var(--primary-color, #0056d6);
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
  vertical-align: middle;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.signup-step-indicator {
  text-align: center;
  font-size: 1.02rem;
  color: #6a7ba3;
  font-weight: 500;
  margin-bottom: 0.7rem;
  letter-spacing: 0.01em;
}
.signup-row {
  display: flex;
  gap: 1.1rem;
  width: 100%;
  margin-bottom: 0.1rem;
}
.signup-field-col {
  flex: 1 1 0;
  min-width: 0;
  display: flex;
  flex-direction: column;
}
.signup-field-col-full {
  flex: 1 1 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.signup-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.1rem 1.1rem;
  width: 100%;
}
.signup-grid-full {
  grid-column: 1 / 3;
}
@media (max-width: 600px) {
  .signup-grid {
    grid-template-columns: 1fr;
    gap: 0.7rem 0;
  }
  .signup-grid-full {
    grid-column: 1 / 2;
  }
}
.password-group {
  position: relative;
}
.show-hide-toggle {
  position: absolute;
  right: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  color: #00b6b6;
  cursor: pointer;
  user-select: none;
  font-weight: 600;
  z-index: 4;
  background: transparent;
  border: none;
  padding: 0 0.2em;
  border-radius: 3px;
  transition: color 0.18s;
  outline: none;
  display: flex;
  align-items: center;
}
.show-hide-toggle:hover, .show-hide-toggle:focus {
  color: #009e9e;
  background: #f7f9fc;
}
.show-hide-toggle svg {
  display: block;
}
.social-login-options {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.7rem;
}
.social-login-divider {
  width: 100%;
  text-align: center;
  margin: 0.7rem 0 0.5rem 0;
  color: #b0b8c9;
  font-size: 0.98rem;
  position: relative;
}
.social-login-divider span {
  background: #fff;
  padding: 0 0.7em;
  position: relative;
  z-index: 2;
}
.social-login-divider:before,
.social-login-divider:after {
  content: '';
  display: block;
  position: absolute;
  top: 50%;
  width: 40%;
  height: 1px;
  background: #e0e6ef;
}
.social-login-divider:before {
  left: 0;
}
.social-login-divider:after {
  right: 0;
}


.forgot-password-link {
  width: 100%;
  text-align: right;
  margin: 0.2rem 0 0.7rem 0;
}
.forgot-password-link span {
  color: var(--primary-color, #0056d6);
  font-size: 0.97rem;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.18s;
}
.forgot-password-link span:hover,
.forgot-password-link span:focus {
  color: #1976f8;
  text-decoration: underline;
}
.login-form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0.5rem 0 0.5rem 0;
  width: 100%;
}
.remember-me {
  display: flex;
  align-items: center;
  gap: 0.45em;
  font-size: 0.98rem;
  color: #3a4a6b;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
}
.remember-me input[type="checkbox"] {
  display: none;
}
.custom-checkbox {
  width: 1.1em;
  height: 1.1em;
  border: 2px solid var(--primary-color, #00b6b6);
  border-radius: 4px;
  display: inline-block;
  position: relative;
  margin-right: 0.2em;
  background: #fff;
  transition: border-color 0.18s;
}
.remember-me input[type="checkbox"]:checked + .custom-checkbox {
  background: var(--primary-color, #00b6b6);
  border-color: var(--primary-color, #00b6b6);
}
.remember-me input[type="checkbox"]:checked + .custom-checkbox:after {
  content: '';
  position: absolute;
  left: 0.28em;
  top: 0.05em;
  width: 0.35em;
  height: 0.65em;
  border: solid #fff;
  border-width: 0 0.18em 0.18em 0;
  transform: rotate(45deg);
  display: block;
}
.login-btn {
  width: 100%;
  background: var(--primary-color, #00b6b6);
  color: #fff;
  font-weight: 700;
  font-size: 1.08rem;
  border-radius: 24px;
  padding: 0.7em 0;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  border: none;
  box-shadow: 0 2px 8px rgba(0,182,182,0.08);
  transition: background 0.18s, box-shadow 0.18s, color 0.18s;
}
.login-btn:hover, .login-btn:focus {
  background: #00d6c6;
  color: #fff;
  box-shadow: 0 4px 16px rgba(0,182,182,0.16);
}

/* Footer.css */
.site-footer {
    background-color: var(--tertiary-navy);
    color: var(--tertiary-gray-light);
    padding: 4rem 0 2rem;
    font-size: 0.95rem;
}

.footer-main {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-column.about p {
    margin-top: 1rem;
    color: var(--secondary-gray-light);
    line-height: 1.7;
}

.social-links {
    margin-top: 1.5rem;
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: var(--tertiary-gray-light);
    font-size: 1.5rem;
    transition: color 0.3s ease, transform 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-teal);
    transform: translateY(-2px);
}

.footer-heading {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-white);
}

.footer-column.links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column.links li {
    margin-bottom: 0.75rem;
}

.footer-column.links a, .footer-column.contact a {
    color: var(--tertiary-gray-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column.links a:hover, .footer-column.contact a:hover {
    color: var(--primary-teal);
}

.footer-column.contact p {
    line-height: 1.7;
    color: var(--tertiary-gray-light);
}

.footer-bottom {
    border-top: 1px solid var(--secondary-teal-dark);
    padding-top: 2rem;
    text-align: center;
    font-size: 0.9rem;
    color: var(--secondary-gray-light);
} 

/* Header.css */
.site-header {
    background-color: var(--primary-white);
    padding: 0.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 2000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
}

.header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-nav ul {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.main-nav a {
    text-decoration: none;
    color: var(--tertiary-navy);
    font-weight: 500;
    position: relative;
    transition: color 0.3s ease;
}

.main-nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-teal);
    transition: width 0.3s ease;
}

.main-nav a:hover,
.main-nav a.active {
    color: var(--primary-teal);
}

.main-nav a:hover::after,
.main-nav a.active::after {
    width: 100%;
}

.button-primary {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 15px rgba(46, 192, 203, 0.3);
}

.button-primary:hover {
    background-color: var(--primary-teal-dark);
    transform: translateY(-2px);
}

.has-submenu {
    position: relative;
}

.nav-link {
    cursor: pointer;
    font-weight: 500;
    color: var(--tertiary-navy);
    padding: 0.5rem 1.2rem;
    border-radius: 50px;
    transition: background 0.2s, color 0.2s;
}

.has-submenu:hover .submenu,
.has-submenu:focus-within .submenu {
    display: block;
    opacity: 1;
    pointer-events: auto;
}

.submenu {
    display: none;
    position: absolute;
    top: 110%;
    left: 0;
    min-width: 160px;
    background: var(--primary-white);
    box-shadow: 0 8px 32px rgba(46,192,203,0.13);
    border-radius: 12px;
    padding: 0.5rem 0.2rem;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.submenu li {
    list-style: none;
}

.submenu-link {
    background: none;
    border: none;
    color: var(--tertiary-navy);
    font-size: 1rem;
    font-weight: 500;
    padding: 0.7rem 1.5rem;
    width: 100%;
    text-align: left;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    outline: none;
}

.submenu-link:hover, .submenu-link:focus {
    background: var(--primary-teal);
    color: var(--primary-white);
}

/* Add margin to main content to prevent overlap */
#hero {
     margin-top: 75px; 
}

.auth-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-left: 1.5rem;
}

.btn.auth-signup {
    background: var(--primary-color, #0056d6);
    color: #fff;
    font-weight: 600;
    border-radius: 24px;
    padding: 0.5rem 1.5rem;
    box-shadow: 0 2px 8px rgba(0,86,214,0.08);
    transition: transform 0.18s cubic-bezier(.4,1.2,.6,1),
        background 0.18s, box-shadow 0.18s;
    border: none;
    outline: none;
}
.btn.auth-signup:hover, .btn.auth-signup:focus {
    transform: translateY(-3px) scale(1.04);
    background: #1976f8;
    box-shadow: 0 4px 16px rgba(0,86,214,0.16);
}

.btn.auth-login {
    background: transparent;
    color: var(--primary-color, #0056d6);
    font-weight: 600;
    border: 2px solid var(--primary-color, #0056d6);
    border-radius: 24px;
    padding: 0.5rem 1.5rem;
    position: relative;
    overflow: hidden;
    transition: color 0.18s, border-color 0.18s;
    outline: none;
}
.btn.auth-login::after {
    content: '';
    display: block;
    position: absolute;
    left: 0; bottom: 0;
    width: 0%;
    height: 2px;
    background: var(--primary-color, #0056d6);
    transition: width 0.28s cubic-bezier(.4,1.2,.6,1);
}
.btn.auth-login:hover, .btn.auth-login:focus {
    color: #1976f8;
    border-color: #1976f8;
}
.btn.auth-login:hover::after, .btn.auth-login:focus::after {
    width: 100%;
}

@media (max-width: 1200px) {
  .main-nav {
    display: none;
  }
  .header-hamburger {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 2101;
    margin-right: 1rem;
  }
  .header-hamburger span {
    display: block;
    width: 26px;
    height: 3px;
    background: var(--primary-teal, #0096a7);
    margin: 3px 0;
    border-radius: 2px;
    transition: all 0.22s;
  }
  .header-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 270px;
    height: 100vh;
    background: #fff;
    box-shadow: 2px 0 24px rgba(0,0,0,0.10);
    z-index: 2102;
    transform: translateX(-100%);
    transition: transform 0.28s cubic-bezier(.4,1.2,.6,1);
    display: flex;
    flex-direction: column;
    padding: 1.2rem 0.7rem 1.2rem 1.2rem;
  }
  .header-sidebar.open {
    transform: translateX(0);
  }
  .header-sidebar-close {
    background: none;
    border: none;
    font-size: 2.2rem;
    color: var(--primary-teal, #0096a7);
    align-self: flex-end;
    margin-bottom: 1.2rem;
    cursor: pointer;
    z-index: 2103;
  }
  .header-sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  .header-sidebar-nav li {
    margin-bottom: 0.2rem;
  }
  .header-sidebar-nav .nav-link,
  .header-sidebar-nav a,
  .header-sidebar-nav .submenu-link,
  .header-sidebar-nav .auth-link-text,
  .header-sidebar-nav .header-profile-menu-item {
    font-size: 1.08rem;
    color: #005e6a;
    font-weight: 500;
    padding: 0.7rem 0.8rem;
    border-radius: 8px;
    background: none;
    border: none;
    text-align: left;
    width: 100%;
    display: block;
    transition: background 0.18s, color 0.18s;
    cursor: pointer;
  }
  .header-sidebar-nav a.btn.btn-primary {
    margin-top: 1.2rem;
    width: 90%;
    text-align: center;
    align-self: center;
  }
  .header-sidebar-nav .nav-link:hover,
  .header-sidebar-nav a:hover,
  .header-sidebar-nav .submenu-link:hover,
  .header-sidebar-nav .auth-link-text:hover,
  .header-sidebar-nav .header-profile-menu-item:hover {
    background: #e0f7fa;
    color: #0096a7;
  }
  .header-sidebar-backdrop {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.18);
    z-index: 2100;
    animation: fadeInSidebarBackdrop 0.22s cubic-bezier(.4,1.2,.6,1);
  }
  @keyframes fadeInSidebarBackdrop {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

@media (min-width: 1201px) {
  .header-hamburger {
    display: none;
  }
  .header-sidebar {
    display: none;
  }
  .header-sidebar-backdrop {
    display: none;
  }
}

.header-cart-login-row {
  display: flex;
  align-items: center;
  gap: 0.2rem;
}
.header-cart-icon-wrap {
  position: relative;
  display: flex;
  align-items: center;
}
.header-cart-icon {
  font-size: 2rem;
  color: var(--primary-teal, #0096a7);
  cursor: pointer;
  transition: color 0.18s;
}
.header-cart-badge {
  position: absolute;
  top: -7px;
  right: -10px;
  background: #e53935;
  color: #fff;
  font-size: 0.85rem;
  font-weight: 700;
  border-radius: 50%;
  padding: 2px 7px;
  min-width: 22px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(229,57,53,0.18);
  pointer-events: none;
  z-index: 2;
}

.header-cart-login-row .auth-link-text {
  margin-left: 2.2rem !important;
  margin-right: 0.2rem !important;
}

.header-cart-login-row .header-profile-wrapper {
  margin-left: 2.2rem !important;
  margin-right: 0.2rem !important;
}

/* Cart Header Styles */
.cart-header-custom {
    background-color: var(--primary-white);
    padding: 0.5rem 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 2000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.cart-header-custom .header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.cart-header-nav {
    display: flex;
    gap: 2rem;
    list-style: none;
    cursor: pointer;
}

.cart-header-link {
    text-decoration: none;
    color: var(--tertiary-navy);
    font-weight: 500;
    position: relative;
    transition: color 0.3s ease;
}

.cart-header-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-teal);
    transition: width 0.3s ease;
}

.cart-header-link:hover {
    color: var(--primary-teal);
}

.cart-header-link:hover::after {
    width: 100%;
}

/* Cart count styling */
.cart-header-link:has(span:contains("Cart")) {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Ensure cart header uses same styles as main page for these elements */
.cart-header-custom .header-cart-login-row {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.cart-header-custom .btn.btn-primary {
    background: var(--primary-teal);
    color: var(--primary-white);
    font-weight: 600;
    border-radius: 50px;
    padding: 0.7rem 1.6rem;
    box-shadow: 0 2px 10px rgba(46,192,203,0.13);
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
    border: none;
    outline: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.cart-header-custom .btn.btn-primary:hover {
    background: var(--primary-teal-dark);
    color: var(--primary-white);
    transform: translateY(-2px);
}

/* Profile styles for cart header */
.cart-header-custom .header-profile-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 1.5rem;
}

.cart-header-custom .header-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 22px;
    padding: 0.2rem 0.9rem 0.2rem 0.2rem;
    transition: background 0.18s;
}

.cart-header-custom .header-profile:hover, 
.cart-header-custom .header-profile:focus {
    background: #e0f7fa;
}

.cart-header-custom .header-profile-photo {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 0.7rem;
    border: 2px solid var(--primary-teal, #0096a7);
    background: #fff;
}

.cart-header-custom .header-profile-name {
    font-weight: 600;
    color: var(--primary-teal-dark, #005e6a);
    font-size: 1.08rem;
    letter-spacing: 0.01em;
}

.cart-header-custom .header-profile-menu {
    position: absolute;
    top: 110%;
    right: 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.10);
    min-width: 170px;
    z-index: 100;
    padding: 0.5rem 0;
    display: flex;
    flex-direction: column;
    animation: fadeInProfileMenu 0.22s cubic-bezier(.4,1.2,.6,1);
}

.cart-header-custom .header-profile-menu-item {
    padding: 0.7rem 1.2rem;
    color: #005e6a;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.16s, color 0.16s;
    border: none;
    background: none;
    text-align: left;
}

.cart-header-custom .header-profile-menu-item:hover, 
.cart-header-custom .header-profile-menu-item:focus {
    background: #e0f7fa;
    color: #0096a7;
}

@keyframes fadeInProfileMenu {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.cart-contact-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 3000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cart-contact-modal {
    background: var(--primary-white);
    border-radius: 12px;
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.cart-contact-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--tertiary-navy);
    z-index: 1;
}

.cart-contact-modal-close:hover {
    color: var(--primary-teal);
}

@media (max-width: 768px) {
    .cart-header-nav {
        gap: 1.5rem;
        flex-wrap: wrap;
    }
    
    .cart-header-link {
        font-size: 1rem;
        padding: 0.4rem 0;
    }
    
    .cart-header-custom .header-inner {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    /* Hide desktop navigation on mobile */
    .cart-header-custom .cart-header-nav {
        display: none;
    }
    
    /* Show hamburger on mobile */
    .cart-header-custom .header-hamburger {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 24px;
        height: 20px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
    }
    
    .cart-header-custom .header-hamburger span {
        width: 100%;
        height: 2px;
        background: var(--primary-teal);
        transition: all 0.3s ease;
    }
}

@media (min-width: 769px) {
    .cart-header-custom .header-hamburger {
        display: none;
    }
    
    .cart-header-custom .header-sidebar {
        display: none;
    }
    
    .cart-header-custom .header-sidebar-backdrop {
        display: none;
    }
}

/* Cart header sidebar styles */
.cart-header-custom .header-sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: var(--primary-white);
    box-shadow: -4px 0 24px rgba(0,0,0,0.15);
    z-index: 2200;
    transition: right 0.3s cubic-bezier(.4,1.2,.6,1);
    overflow-y: auto;
}

.cart-header-custom .header-sidebar.open {
    right: 0;
}

.cart-header-custom .header-sidebar-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--tertiary-navy);
    z-index: 1;
}

.cart-header-custom .header-sidebar-nav {
    padding: 4rem 2rem 2rem 2rem;
    list-style: none;
    margin: 0;
}

.cart-header-custom .header-sidebar-nav li {
    margin-bottom: 1rem;
}

.cart-header-custom .header-sidebar-nav .nav-link,
.cart-header-custom .header-sidebar-nav a {
    display: block;
    padding: 0.7rem 0.8rem;
    border-radius: 8px;
    background: none;
    border: none;
    text-align: left;
    width: 100%;
    display: block;
    transition: background 0.18s, color 0.18s;
    cursor: pointer;
    text-decoration: none;
    color: var(--tertiary-navy);
    font-weight: 500;
}

.cart-header-custom .header-sidebar-nav a.btn.btn-primary {
    margin-top: 1.2rem;
    width: 90%;
    text-align: center;
    align-self: center;
}

.cart-header-custom .header-sidebar-nav .nav-link:hover,
.cart-header-custom .header-sidebar-nav a:hover,
.cart-header-custom .header-sidebar-nav .header-profile-menu-item:hover {
    background: #e0f7fa;
    color: #0096a7;
}

.cart-header-custom .header-sidebar-backdrop {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.18);
    z-index: 2100;
    animation: fadeInSidebarBackdrop 0.22s cubic-bezier(.4,1.2,.6,1);
}

/* Logo.css */
.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--tertiary-navy);
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.logo-text-main {
    font-size: 1.5rem; /* 24px */
    font-weight: 800; /* ExtraBold */
    color: var(--primary-teal);
    letter-spacing: -1px;
}

.logo-text-sub {
    font-size: 0.875rem; /* 14px */
    font-weight: 400; /* Regular */
    color: var(--tertiary-navy);
    text-transform: uppercase;
    letter-spacing: 1.5px;
} 

/* AboutUs.css */
.about-section {
  width: 100vw;
  padding: 5rem 0;
  background: linear-gradient(45deg, var(--primary-white) 60%, #e6fafd 100%);
  position: relative;
  overflow: hidden;
}

.about-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background: linear-gradient(120deg, var(--primary-teal) 0%, var(--secondary-teal-dark) 50%, var(--tertiary-navy) 100%);
  opacity: 0.10;
  background-size: 200% 200%;
  animation: aboutBgMove 32s ease-in-out infinite;
  pointer-events: none;
}

@keyframes aboutBgMove {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 0;
}

.about-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: none;
  margin: 0;
  background: rgba(255,255,255,0.98);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(46,192,203,0.10), 0 2px 8px rgba(0,0,0,0.04);
  padding: 3rem 3vw 2.5rem 3vw;
  display: flex;
  flex-direction: column;
  gap: 2.2rem;
  animation: fadeInAbout 1.2s cubic-bezier(.4,1.4,.6,1) both;
}

@keyframes fadeInAbout {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: none; }
}

.section-header {
  text-align: center;
  margin-bottom: 0.5rem;
}

.section-title {
  font-size: 2.7rem;
  font-weight: 800;
  color: var(--primary-teal);
  margin-bottom: 0.5rem;
  letter-spacing: -0.5px;
}

.section-subtitle {
  color: var(--primary-blue);
}

.about-mission {
  font-size: 1.18rem;
  font-weight: 500;
  color: var(--tertiary-navy);
  text-align: center;
  margin-bottom: 0.5rem;
}

.about-industry-challenge, .about-solution, .about-heritage-vision {
  background: #f7fafd;
  border-radius: 16px;
  padding: 1.5rem 1.2rem 1.2rem 1.2rem;
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  margin-bottom: 0.5rem;
  animation: fadeInAbout 1.2s cubic-bezier(.4,1.4,.6,1) both;
}

.about-industry-challenge h3, .about-solution h3, .about-heritage-vision h3 {
  color: var(--primary-teal);
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.about-cta {
  text-align: center;
  margin-top: 1.2rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin-top: 1.2rem;
}

.cta-btn {
  display: inline-block;
  padding: 0.95rem 2.5rem;
  border-radius: 50px;
  font-size: 1.12rem;
  font-weight: 700;
  color: var(--primary-teal);
  background: #e6fafd;
  border: 2.5px solid var(--primary-teal);
  text-decoration: none;
  transition: all 0.25s cubic-bezier(.4,1.4,.6,1);
  box-shadow: 0 2px 8px rgba(46,192,203,0.07);
  position: relative;
  overflow: hidden;
}

.cta-btn:after {
  content: '';
  position: absolute;
  left: -60%;
  top: 0;
  width: 60%;
  height: 100%;
  background: linear-gradient(120deg, rgba(46,192,203,0.12) 0%, rgba(15,95,220,0.18) 100%);
  transform: skewX(-20deg);
  transition: left 0.5s;
  z-index: 1;
  pointer-events: none;
}

.cta-btn:hover:after {
  left: 110%;
  transition: left 0.5s;
}

.cta-btn:hover {
  background: #e6fafd;
  color: var(--primary-blue);
  border-width: 3px;
  box-shadow: 0 8px 32px rgba(46,192,203,0.13);
}

.cta-btn-primary {
  background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
  color: #fff;
  border: 2.5px solid var(--primary-blue);
  font-weight: 800;
}

.cta-btn-primary:hover {
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-teal));
  color: #fff;
  border-width: 3px;
  box-shadow: 0 8px 32px rgba(15,95,220,0.18);
}

.cta-btn .cta-arrow {
  opacity: 0;
  margin-left: 0.2em;
  font-size: 1.1em;
  transform: translateY(6px);
  transition: opacity 0.3s, transform 0.3s;
  display: inline-block;
}

.cta-btn:hover .cta-arrow {
  opacity: 1;
  transform: translateY(0);
}

/* Image/animation placeholder styles */
.about-visual {
  width: 100%;
  max-width: 340px;
  margin: 0 auto 2rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  background: linear-gradient(120deg, #e6fafd 60%, #fff 100%);
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(46,192,203,0.07);
  overflow: hidden;
  position: relative;
  animation: fadeInAbout 1.2s cubic-bezier(.4,1.4,.6,1) both;
}

@media (max-width: 900px) {
  .about-content {
    padding: 2rem 1vw 1.5rem 1vw;
  }
  .section-title {
    font-size: 2rem;
  }
}

@media (max-width: 600px) {
  .about-content {
    padding: 1.2rem 0.2rem 1rem 0.2rem;
  }
  .section-title {
    font-size: 1.3rem;
  }
  .about-visual {
    min-height: 120px;
    max-width: 98vw;
  }
}

.about-row {
  display: flex;
  flex-direction: row;
  gap: 2.5rem;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.about-card {
  background: #f7fafd;
  border-radius: 20px;
  padding: 2.2rem 1.2rem 1.5rem 1.2rem;
  box-shadow: 0 4px 24px rgba(46,192,203,0.10), 0 2px 8px rgba(0,0,0,0.04);
  margin-bottom: 0.5rem;
  border-top: 4px solid;
  border-image: linear-gradient(90deg, var(--primary-teal), var(--primary-blue)) 1;
  border-right: none;
  border-bottom: none;
  border-left: none;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.3s, border-color 0.3s, transform 0.3s;
  opacity: 0;
  transform: translateY(40px);
}

.about-card[data-animate="left"].in-view {
  animation: aboutColFadeIn 0.9s 0.1s cubic-bezier(.4,1.4,.6,1) forwards;
}
.about-card[data-animate="right"].in-view {
  animation: aboutColFadeIn 0.9s 0.2s cubic-bezier(.4,1.4,.6,1) forwards;
}
.about-card[data-animate="center"].in-view {
  animation: aboutColFadeIn 1.1s 0.4s cubic-bezier(.4,1.4,.6,1) forwards;
}

@keyframes aboutColFadeIn {
  to {
    opacity: 1;
    transform: none;
  }
}

.about-card:hover {
  box-shadow: 0 12px 40px rgba(15,95,220,0.16), 0 2px 8px rgba(0,0,0,0.07);
  border-top: 4px solid;
  border-image: linear-gradient(90deg, var(--primary-blue), var(--primary-teal)) 1;
  transform: translateY(-8px) scale(1.03);
}

.about-card:hover .about-card-icon svg {
  stroke: var(--primary-blue);
  filter: drop-shadow(0 0 6px var(--primary-blue));
  animation: aboutIconPulse 1.2s infinite alternate;
}

@keyframes aboutIconPulse {
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(1.12); opacity: 0.85; }
}

.about-card-icon-wrap {
  margin-bottom: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-card-icon {
  width: 20%;
  max-width: 90px;
  min-width: 48px;
  height: auto;
  display: block;
  margin: 0 auto 1rem auto;
}

.about-card-icon img, .about-card-icon {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.about-card-central {
  background: linear-gradient(120deg, #f7fafd 80%, #e6fafd 100%);
  box-shadow: 0 8px 40px rgba(15,95,220,0.10), 0 2px 8px rgba(0,0,0,0.04);
  z-index: 3;
  min-height: 110%;
  margin-top: -20px;
  margin-bottom: -20px;
  filter: drop-shadow(0 8px 32px rgba(46,192,203,0.10));
  border-top: 4px solid;
  border-image: linear-gradient(90deg, var(--primary-blue), var(--primary-teal)) 1;
  position: relative;
}

.about-card-central .about-card-icon svg {
  stroke: var(--primary-blue);
}

.about-card h3 {
  color: var(--primary-teal);
  font-size: 1.35rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  text-align: center;
  letter-spacing: -0.5px;
}

.about-card-central h3 {
  color: var(--primary-blue);
}

@media (max-width: 900px) {
  .about-row {
    flex-direction: column;
    gap: 1.2rem;
  }
  .about-card-central {
    min-height: unset;
    margin-top: 0;
    margin-bottom: 0;
  }
} 

/* Cart */
/* Cart Container Styles */
.cart-container {
  width: 100%;
  margin: 0 auto;
  margin-top: 90px; /* Account for fixed header */
  padding: 0px 24px;
  min-height: calc(100vh - 200px);
  max-width: none;
}

.cart-header {
  text-align: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
}

.cart-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-teal);
  margin-bottom: 0.5rem;
}

.cart-subtitle {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  color: var(--primary-blue);
}

/* Main Layout */
.cart-main-layout {
  display: flex;
  gap: 32px;
  align-items: flex-start;
  min-height: calc(100vh - 300px);
  position: relative;
  transition: all 0.3s ease;
}

.cart-main-layout.global-drag-over {
  background: rgba(0, 123, 255, 0.1);
  border: 2px dashed #007bff;
  border-radius: 16px;
  padding: 20px;
}

/* Sidebar Styles */
.cart-sidebar {
  width: 320px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 24px;
  flex-shrink: 0;
  max-height: calc(100vh - 200px);
}

.cart-sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  text-align: center;
}

.cart-sidebar-subtitle {
  font-size: 0.875rem;
  color: #718096;
  margin-bottom: 20px;
  text-align: center;
}

.cart-sidebar-search {
  margin-bottom: 20px;
}

.cart-sidebar-search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: #ffffff;
}

.cart-industries-list {
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.cart-industry-item {
  padding: 12px 16px;
  margin: 8px 0;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
  color: #4a5568;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-industry-item:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.cart-industry-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.cart-industry-icon {
  width: 50px;
  height: 40px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* Grid Container */
.cart-grid-container {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  min-height: 0;
  background: #fff;
  border-radius: 16px;
  scrollbar-width: none; /* Firefox */
}
.cart-grid-container::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* View Controls */
.cart-view-controls {
  position: sticky;
  top: 0;
  z-index: 2;
  background: #fff;
  box-shadow: 0 2px 8px rgba(20, 184, 166, 0.07);
  border-radius: 16px 16px 0 0;
  padding-top: 8px;
  padding-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 18px;
  margin-bottom: 0;
}

.cart-search-section {
  flex: 1;
  max-width: 300px;
}

.cart-search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.cart-search-input {
  width: 100%;
  padding: 10px 16px;
  padding-right: 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.cart-search-input:focus {
  outline: none;
  border-color: var(--primary-teal);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.cart-search-input::placeholder {
  color: #9ca3af;
}

.cart-search-clear {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: #e5e7eb;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  color: #6b7280;
  transition: all 0.2s ease;
}

.cart-search-clear:hover {
  background: #d1d5db;
  color: #374151;
}

.cart-counts-section {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.cart-count-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.cart-count-icon {
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.cart-count-icon-img {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.cart-count-label {
  color: #6b7280;
  font-weight: 500;
}

.cart-count-value {
  color: var(--primary-teal);
  font-weight: 700;
  font-size: 1rem;
}

.cart-count-item.active {
  background: var(--primary-teal);
  color: white;
  border-color: var(--primary-teal);
}

.cart-count-item.active .cart-count-label,
.cart-count-item.active .cart-count-value {
  color: white;
}

.cart-pagination-info {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  flex-shrink: 0;
}

/* Card View */
.cart-cards-view {
  margin-top: 8px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  align-items: start;
  margin-bottom: 24px;
}

/* Pagination */
.cart-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin: 32px 0;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.cart-pagination-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.cart-pagination-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.cart-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cart-pagination-pages {
  display: flex;
  gap: 4px;
}

.cart-pagination-page {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-pagination-page:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.cart-pagination-page.active {
  background: var(--primary-teal);
  color: white;
  border-color: var(--primary-teal);
}

/* Card Styles */
.cart-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 15px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: fit-content;
  min-height: 300px;
}

.cart-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.cart-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 48px; /* Adjust to match your grid card header height */
  display: flex;
  align-items: center;
  justify-content: center;
}

.cart-card-icon-container {
  background: var(--primary-teal);
  color: #ffffff;
  border-radius: 8px;
  padding: 12px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  min-width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.cart-card-title-container {
  flex: 1;
  min-width: 0;
}

.cart-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cart-card-type {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
}

/* Platforms Section */
.cart-platforms-section {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.cart-platforms-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.cart-platforms-group {
  display: flex;
  gap: 1.5rem;
  margin-top: 0.5rem;
}

.cart-platform-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fff;
  border: 2px solid #d1d5db;
  border-radius: 2rem;
  padding: 0.4rem 1.2rem 0.4rem 0.8rem;
  font-size: 1rem;
  color: #444;
  cursor: pointer;
  transition: background 0.2s, border 0.2s, color 0.2s, box-shadow 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
  outline: none;
}

.cart-platform-toggle .cart-platform-icon {
  width: 20px;
  height: 20px;
  display: block;
  transition: filter 0.2s;
}

.cart-platform-toggle .cart-platform-label {
  font-weight: 500;
  letter-spacing: 0.01em;
  transition: color 0.2s;
}

.cart-platform-toggle.active {
  background: #fff;
  color: #14b8a6;
  border-color: #14b8a6;
  box-shadow: 0 2px 8px rgba(20,184,166,0.08);
}

.cart-platform-toggle.active .cart-platform-label {
  color: #14b8a6;
}

.cart-platform-toggle.active .cart-platform-icon {
  filter: none;
}

.cart-platform-toggle:hover,
.cart-platform-toggle:focus-visible {
  border-color: #14b8a6;
  color: #14b8a6;
  background: #f0fdfa;
}

.cart-platform-toggle:active {
  background: #ccfbf1;
}

/* Industries Section */
.cart-industries-section {
  margin-top: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 120px;
}

.cart-industries-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

/* Cart Industries Section - Drop Zone Scrollable (2 rows, single scrollbar) */
.cart-drop-zone {
  min-height: 40px;
  height: 75px; /* 2 rows of tags, adjust as needed for your tag size */
  overflow-y: auto;
  background: #f8fafc;
  border: 1.5px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  margin-top: 0.25rem;
  transition: border-color 0.2s, background 0.2s;
}

.cart-drop-zone.active {
  border-color: #14b8a6;
  background: #f0fdfa;
}

.cart-selected-industries {
  display: flex;
  flex-wrap: wrap;
  /*gap: 0.5rem;*/
  /* Remove max-height and overflow here to avoid double scrollbars */
}

.cart-drop-zone::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}
.cart-drop-zone::-webkit-scrollbar-thumb {
  background: #e0e7ef;
  border-radius: 4px;
}

.cart-drop-zone-empty {
  color: #a0aec0;
  font-size: 0.95rem;
  padding: 0.25rem 0;
}

.cart-industry-tag {
  color: #000000;
  padding: 0px 2px;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.cart-remove-industry {
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: bold;
  color: red;
}

/* Card Footer */
.cart-card-footer {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.cart-delete-btn {
  background: linear-gradient(135deg, #fc8181 0%, #e53e3e 100%);
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 0px 0px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
  justify-content: center;
}

.cart-delete-btn:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

/* Subscription Section */

.cart-subscription-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  text-align: center;
}

.cart-subscription-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.cart-subscription-option.selected {
  border-color: #4299e1;
  background: #ebf8ff;
}

.cart-radio {
  width: 16px;
  height: 16px;
  accent-color: #4299e1;
}

.cart-subscription-label {
  font-weight: 600;
}

.cart-subscription-discount {
  font-size: 0.75rem;
  color: #718096;
}

/* Action Buttons */
.cart-action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  margin-top: 32px;
}

.cart-add-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cart-add-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #f8fafc;
  color: var(--primary-teal);
  text-decoration: none;
  border: 2px solid var(--primary-teal);
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.cart-add-btn:hover {
  background: var(--primary-teal);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.cart-total-count {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  white-space: nowrap;
}

.cart-checkout-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 32px;
  background: var(--primary-teal);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cart-checkout-btn:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Empty Cart */
.cart-empty {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  grid-column: 1 / -1;
}

.cart-empty-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  opacity: 0.5;
}

.cart-empty-text {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 8px;
}

.cart-empty-subtext {
  font-size: 1rem;
  color: #a0aec0;
}

/* Success Message */
.cart-success {
  text-align: center;
  padding: 80px 20px;
  color: #2d3748;
  grid-column: 1 / -1;
}

.cart-success-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  color: #48bb78;
}

.cart-success-title {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2d3748;
}

.cart-success-text {
  font-size: 1.1rem;
  color: #4a5568;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .cart-cards-view {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 1024px) {
  .cart-main-layout {
    flex-direction: column;
    gap: 24px;
  }

  .cart-sidebar {
    width: 100%;
    position: static;
    max-height: 300px;
  }

  .cart-grid-container {
    max-height: 300px;
  }

  .cart-cards-view {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .cart-view-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .cart-search-section {
    max-width: none;
  }

  .cart-counts-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .cart-pagination {
    flex-wrap: wrap;
    gap: 12px;
  }

  .cart-add-new-section {
    margin-left: 0;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .cart-container {
    padding: 0px 16px;
  }

  .cart-title {
    font-size: 2rem;
  }

  .cart-cards-view {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .cart-subscription-options {
    flex-direction: column;
    gap: 12px;
  }

  .cart-action-buttons {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .cart-add-section {
    justify-content: center;
  }

  .cart-sidebar {
    max-height: 250px;
  }

  .cart-industries-list {
    max-height: 180px;
  }

  .cart-pagination {
    flex-direction: column;
    gap: 12px;
  }

  .cart-pagination-pages {
    justify-content: center;
  }

  .cart-view-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .cart-search-section {
    max-width: none;
  }

  .cart-counts-section {
    justify-content: center;
    gap: 6px;
  }

  .cart-count-item {
    padding: 4px 8px;
    font-size: 0.75rem;
  }

  .cart-add-new-section {
    justify-content: center;
  }

  .cart-add-new-btn {
    width: 100%;
    justify-content: center;
    padding: 12px 16px;
  }
}

@media (max-width: 480px) {
  .cart-container {
    padding: 0px 12px;
  }

  .cart-title {
    font-size: 1.75rem;
  }

  .cart-subtitle {
    font-size: 1rem;
  }

  .cart-sidebar {
    padding: 16px;
  }

  .cart-card {
    padding: 12px;
    min-height: 250px;
  }

  .cart-view-controls {
    padding: 12px;
  }

  .cart-pagination-page {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .cart-search-input {
    padding: 6px 10px;
    padding-right: 32px;
    font-size: 0.75rem;
  }

  .cart-search-clear {
    width: 18px;
    height: 18px;
    font-size: 0.75rem;
  }

  .cart-counts-section {
    gap: 4px;
  }

  .cart-count-item {
    padding: 3px 6px;
    font-size: 0.75rem;
    gap: 3px;
  }

  .cart-count-value {
    font-size: 0.875rem;
  }

  .cart-count-icon {
    font-size: 0.75rem;
  }

  .cart-count-icon-img {
    width: 12px;
    height: 12px;
  }
}

/* Remove old checkbox styles if not used anymore */
.cart-checkbox-container, .cart-checkbox, .cart-checkbox-label {
  display: none !important;
}

/* Delete Icon Button Styles */
.cart-delete-icon-btn {
  display: none;
  background: none;
  border: none;
  padding: 0.2rem;
  margin-left: 0.5rem;
  cursor: pointer;
  border-radius: 50%;
  transition: background 0.15s;
  align-items: center;
  justify-content: center;
  height: 32px;
  width: 32px;
  outline: none;
  box-shadow: none;
}
.cart-card:hover .cart-delete-icon-btn,
.cart-delete-icon-btn:focus-visible {
  display: flex;
}
.cart-delete-icon {
  width: 30px;
  height: 25px;
  display: block;
  color: #e53e3e;
  filter: none;
  transition: color 0.18s;
}

.cart-add-new-section {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.cart-add-new-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: var(--primary-teal);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-teal);
}

.cart-add-new-btn:hover {
  background: white;
  color: var(--primary-teal);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cart-add-new-btn.hover {
  background: white;
  color: var(--primary-teal);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cart-action-buttons.center {
  justify-content: center;
}

.cart-subscription-section.split {
  display: flex;
  gap: 18px;
  align-items: flex-start;
  justify-content: space-between;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px rgba(20, 184, 166, 0.07), 0 1px 3px rgba(0,0,0,0.03);
  padding: 18px 18px 12px 18px;
  margin-top: 28px;
  margin-bottom: 24px;
}
.cart-subscription-left {
  flex: 0 0 68%;
  max-width: 68%;
}
.cart-subscription-right {
  flex: 0 0 32%;
  max-width: 32%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 0 15px 0 0;
  min-height: 120px;
  background: #f8fafc;
  border-radius: 0 10px 10px 0;
}
.cart-subscription-title {
  font-size: 1.18rem;
  font-weight: 700;
  color: var(--primary-teal);
  margin-bottom: 16px;
  text-align: left;
  letter-spacing: -0.5px;
}
.cart-subscription-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.cart-subscription-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 10px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: #f7fafc;
  transition: all 0.18s cubic-bezier(.4,0,.2,1);
  box-shadow: 0 1px 4px rgba(20, 184, 166, 0.03);
  min-width: 120px;
  font-size: 0.98rem;
  font-weight: 500;
  position: relative;
}
.cart-subscription-option.selected {
  border-color: var(--primary-teal);
  background: #e6fafd;
  box-shadow: 0 2px 8px rgba(20, 184, 166, 0.07);
}
.cart-subscription-option:hover {
  border-color: #23a3ad;
  background: #f0fdfa;
  box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
}
.cart-radio {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-teal);
  margin-right: 4px;
}
.cart-subscription-label {
  font-weight: 600;
  font-size: 0.98rem;
  color: #222;
}
.cart-subscription-discount {
  font-size: 0.78rem;
  color: #23a3ad;
  font-weight: 500;
  margin-top: 1px;
}
.cart-subscription-right {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  min-width: 120px;
  background: #f8fafc;
  border-radius: 0 10px 10px 0;
  box-shadow: none;
}
.cart-checkout-summary {
  font-size: 0.98rem;
  color: #222;
  font-weight: 500;
  margin-bottom: 10px;
  margin-top: 0;
  line-height: 1.4;
}
.cart-checkout-btn.cart-add-new-btn {
  width: 100%;
  font-size: 1rem;
  padding: 10px 0;
  border-radius: 8px;
  margin-top: 4px;
  font-weight: 700;
  background: linear-gradient(90deg, var(--primary-teal) 60%, #23a3ad 100%);
  color: #fff;
  border: none;
  box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
  transition: background 0.15s, color 0.15s;
}
.cart-checkout-btn.cart-add-new-btn:hover {
  background: linear-gradient(90deg, #23a3ad 0%, var(--primary-teal) 100%);
  color: #fff;
}
@media (max-width: 900px) {
  .cart-subscription-section.split {
    flex-direction: column;
    gap: 10px;
    padding: 12px 4px 10px 4px;
  }
  .cart-subscription-left, .cart-subscription-right {
    max-width: 100%;
    flex: 1 1 100%;
    padding-top: 0;
    border-radius: 10px;
    border-left: none;
  }
  .cart-subscription-right {
    padding: 10px 0 0 0;
    min-width: unset;
    background: #f8fafc;
    border-radius: 10px;
    box-shadow: none;
  }
}

.cart-checkout-paper {
  width: 100%;
  padding: 18px 18px 14px 18px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(20, 184, 166, 0.10), 0 1.5px 6px rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 0;
}
.cart-checkout-subtext {
  font-size: 0.92rem;
  color: #23a3ad;
  margin-top: 8px;
  font-weight: 500;
  opacity: 0.85;
}
.cart-checkout-btn-mui {
  margin-top: 12px;
  font-size: 1.08rem;
  font-weight: 700;
  border-radius: 8px;
  box-shadow: 0 1.5px 6px rgba(20, 184, 166, 0.07);
  max-width: 220px;
  width: 100%;
  align-self: center;
}

.cart-details-row {
  display: flex;
  gap: 24px;
  width: 100%;
  margin-top: 28px;
  margin-bottom: 24px;
}
.cart-subscription-section {
  flex: 0 0 60%;
  max-width: 60%;
  
}
.cart-checkout-section {
  flex: 0 0 30%;
  max-width: 30%;
  background: #fff;
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
@media (max-width: 900px) {
  .cart-details-row {
    flex-direction: column;
    gap: 12px;
    margin-top: 16px;
    margin-bottom: 12px;
  }
  .cart-subscription-section, .cart-checkout-section {
    max-width: 100%;
    padding: 12px 6px 10px 6px;
    border-radius: 10px;
  }
  .cart-subscription-section {
    flex: 1 1 100%;
  }
  .cart-checkout-section {
    flex: 1 1 100%;
  }
}

.cart-checkout-paper.compact-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 14px;
  height: 100%;
}
.cart-checkout-title {
  font-size: 1.18rem;
  font-weight: 700;
  color: var(--primary-teal);
  margin-bottom: 16px;
  text-align: left;
  letter-spacing: -0.5px;
}
.cart-checkout-left {
  flex: 1 1 60%;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.cart-checkout-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.cart-checkout-btn-mui {
  margin-top: 0;
  max-width: 180px;
  min-width: 140px;
  width: auto;
  align-self: flex-end;
}
@media (max-width: 900px) {
  .cart-checkout-paper.compact-flex {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  .cart-checkout-left, .cart-checkout-right {
    width: 100%;
    justify-content: center;
    align-items: center;
  }
  .cart-checkout-btn-mui {
    align-self: center;
    width: 100%;
    max-width: 220px;
  }
} 

/* Contact.css */
/* Contact Section - Advanced Styling */

.contact-section {
  padding: 5rem 0;
  background: var(--primary-white);
  position: relative;
  overflow: hidden;
}

.contact-advanced-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

/* Left Column - Informational Content */
.contact-info-col {
  padding-right: 2rem;
}

.contact-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-teal);
  margin-bottom: 1.5rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.contact-desc {
  font-size: 1.1rem;
  line-height: 1.7;
  color: var(--secondary-gray-dark, #4a5568);
  margin-bottom: 2rem;
}

.contact-desc b {
  color: var(--primary-navy, #1a365d);
  font-weight: 600;
}

.contact-phones {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
}

.contact-phone-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-teal, #2ec0cb);
  text-decoration: none;
  padding: 1rem 1.5rem;
  background: rgba(46, 192, 203, 0.1);
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.contact-phone-link:hover {
  background: rgba(46, 192, 203, 0.15);
  border-color: var(--primary-teal, #2ec0cb);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(46, 192, 203, 0.2);
}

.contact-phone-icon {
  font-size: 1.4rem;
  color: var(--primary-teal, #2ec0cb);
}

.contact-desc-bottom {
  font-size: 1rem;
  color: var(--secondary-gray, #718096);
  margin-top: 2rem;
}

/* Right Column - Form */
.contact-form-col {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.contact-form-advanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Rows */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.form-row .form-floating-group.full-width {
  grid-column: 1 / -1;
}

/* Floating Label Groups */
.form-floating-group {
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-floating-group input,
.form-floating-group select {
  width: 100%;
  padding: 1.25rem 1rem 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  /* font-family: inherit; */
  background: white;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.form-floating-group input:focus,
.form-floating-group select:focus {
  outline: none;
  border-color: var(--primary-teal, #2ec0cb);
  box-shadow: 0 0 0 4px rgba(46, 192, 203, 0.1);
  transform: translateY(-1px);
}

.form-floating-group input.invalid,
.form-floating-group select.invalid {
  border-color: #e53e3e;
  box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.1);
}

.form-floating-group label {
  position: absolute;
  top: 1.25rem;
  left: 1rem;
  font-size: 1rem;
  color: #718096;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 2;
  background: white;
  padding: 0 0.25rem;
}

.form-floating-group input:focus + label,
.form-floating-group input:not(:placeholder-shown) + label,
.form-floating-group select:focus + label,
.form-floating-group select:not([value=""]) + label {
  top: -0.5rem;
  font-size: 0.875rem;
  color: var(--primary-teal, #2ec0cb);
  font-weight: 600;
  background: white;
  padding: 0 0.5rem;
  border-radius: 4px;
}

.form-floating-group input:focus.invalid + label,
.form-floating-group input.invalid:not(:placeholder-shown) + label {
  color: #e53e3e;
}

/* Error Messages */
.form-error {
  color: #e53e3e;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: slideIn 0.3s ease;
}

.form-error::before {
  content: '⚠';
  font-size: 0.75rem;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Checkbox Styling */
.checkbox-row {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.custom-checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  padding: 1rem;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.custom-checkbox-label:hover {
  background: rgba(46, 192, 203, 0.05);
  border-color: rgba(46, 192, 203, 0.2);
}

.custom-checkbox-label.invalid {
  border-color: rgba(229, 62, 62, 0.3);
  background: rgba(229, 62, 62, 0.05);
}

.custom-checkbox-label input[type="checkbox"] {
  display: none;
}

.custom-checkbox {
  width: 24px;
  height: 24px;
  border: 2px solid #cbd5e0;
  border-radius: 6px;
  background: white;
  position: relative;
  flex-shrink: 0;
  margin-top: 0.125rem;
  transition: all 0.3s ease;
}

.custom-checkbox-label:hover .custom-checkbox {
  border-color: var(--primary-teal, #2ec0cb);
  background: rgba(46, 192, 203, 0.1);
}

.custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox {
  background: var(--primary-teal, #2ec0cb);
  border-color: var(--primary-teal, #2ec0cb);
}

.custom-checkbox-label input[type="checkbox"]:checked + .custom-checkbox::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.checkbox-main-label {
  font-weight: 500;
  color: var(--primary-navy, #1a365d);
  line-height: 1.5;
}

.checkbox-main-label a {
  color: var(--primary-teal, #2ec0cb);
  text-decoration: none;
  font-weight: 600;
}

.checkbox-main-label a:hover {
  text-decoration: underline;
}

.checkbox-desc {
  font-size: 0.875rem;
  color: #718096;
  line-height: 1.6;
  margin-left: 2.5rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  border-left: 3px solid #e2e8f0;
}

.checkbox-desc.small {
  font-size: 0.8rem;
  max-height: 80px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
}

.checkbox-desc.small::-webkit-scrollbar {
  width: 6px;
}

.checkbox-desc.small::-webkit-scrollbar-track {
  background: transparent;
}

.checkbox-desc.small::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.checkbox-desc a {
  color: var(--primary-teal, #2ec0cb);
  text-decoration: none;
  font-weight: 500;
}

.checkbox-desc a:hover {
  text-decoration: underline;
}

/* Submit Button */
.contact-submit-btn {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, var(--primary-teal, #2ec0cb) 0%, #1a9ba8 100%);
  color: white;
  border: none;
  padding: 1.25rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.contact-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.contact-submit-btn:hover::before {
  left: 100%;
}

.contact-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(46, 192, 203, 0.3);
  background: linear-gradient(135deg, #1a9ba8 0%, var(--primary-teal, #2ec0cb) 100%);
}

.contact-submit-btn:active {
  transform: translateY(0);
}

.contact-submit-btn:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.contact-submit-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

.submit-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.contact-submit-btn:hover .submit-arrow {
  transform: translateX(4px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contact-advanced-grid {
    gap: 3rem;
    padding: 0 1.5rem;
  }
  
  .contact-info-col {
    padding-right: 1rem;
  }
  
  .contact-form-col {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .contact-advanced-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .contact-info-col {
    padding-right: 0;
    text-align: center;
  }
  
  .contact-title {
    font-size: 2rem;
  }
  
  .contact-phones {
    align-items: center;
  }
  
  .contact-form-col {
    padding: 2rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .contact-submit-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .contact-section {
    padding: 4rem 0;
  }
  
  .contact-advanced-grid {
    padding: 0 1rem;
  }
  
  .contact-form-col {
    padding: 1.5rem;
  }
  
  .contact-title {
    font-size: 1.75rem;
  }
  
  .contact-desc {
    font-size: 1rem;
  }
} 

/* Faq.css */
.faq-section {
    padding: 5rem 0;
}

.faq-container {
    max-width: 900px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem 2.5rem;
}

@media (max-width: 800px) {
  .faq-container {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }
}

.section-title {
  color: var(--primary-teal);
}

.section-subtitle {
  color: var(--primary-blue);
} 

/* FaqItem */
.faq-item {
    border-bottom: 1px solid var(--secondary-gray-light);
    padding: 1.5rem 0;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.faq-question h4 {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--tertiary-navy);
    margin: 0;
}

.faq-icon {
    font-size: 1.5rem;
    color: var(--primary-teal);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s cubic-bezier(0, 1, 0, 1), padding 0.5s ease;
}

.faq-item.open .faq-answer {
    max-height: 1000px; /* Large enough to fit any content */
    padding-top: 1rem;
    transition: max-height 1s cubic-bezier(1, 0, 1, 0), padding 0.5s ease;
}

.faq-answer p {
    margin: 0;
    line-height: 1.7;
    color: var(--secondary-teal-dark);
} 

/* Hero.css */
.hero-section {
    position: relative;
    padding: 3.5rem 0;
    text-align: center;
    color: var(--primary-white);
    overflow: hidden;
    min-height: 48vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(45deg, var(--primary-teal), var(--secondary-teal-dark), var(--tertiary-navy));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.hero-headline {
    font-size: 3.5rem;
    font-weight: 800; /* ExtraBold */
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-subheadline {
    font-size: 1.25rem;
    font-weight: 300; /* Light */
    max-width: 600px;
    margin: 0 auto 2.5rem;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.button-secondary {
    background-color: transparent;
    color: var(--primary-white);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-white);
}

.button-secondary:hover {
    background-color: var(--primary-white);
    color: var(--primary-teal);
    transform: translateY(-2px);
} 

/* Industries.css */
.industries-section {
    padding: 5rem 0 2rem 0;
    overflow: hidden;
    margin-bottom: 0;
}

.marquee-container {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 2rem 0;
}

.marquee-container::before,
.marquee-container::after {
    content: '';
    position: absolute;
    top: 0;
    width: 100px;
    height: 100%;
    z-index: 2;
}

.marquee-container::before {
    left: 0;
    background: linear-gradient(to right, var(--primary-white), transparent);
}

.marquee-container::after {
    right: 0;
    background: linear-gradient(to left, var(--primary-white), transparent);
}

.marquee {
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
    width: max-content;
    animation: marqueeAnimation 60s linear infinite;
    align-items: flex-start;
}

@keyframes marqueeAnimation {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-50%);
    }
}

.marquee-card {
    min-width: 180px;
    max-width: 200px;
    width: 180px;
    min-height: 200px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    border-radius: 16px;
    margin: 0.5rem 0;
    flex: 0 0 auto;
}

.industry-item {
    display: none;
}

.industries-grid-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    margin: 2.5rem 0 1.5rem 0;
    color: var(--tertiary-navy);
}

.industries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 2rem;
    margin: 2.5rem auto 3rem auto;
    max-width: 1200px;
}

.industry-card {
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.07);
    border: 1px solid var(--tertiary-gray-light);
    background: var(--primary-white);
    overflow: hidden;
    transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.18s cubic-bezier(.4,2,.6,1);
    cursor: pointer;
    min-height: 200px;
}

.industry-card:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 8px 32px rgba(0,0,0,0.13);
    border-color: var(--primary-blue);
}

.industry-card-header {
    width: 100%;
    text-align: center;
    font-size: 1.18rem;
    font-weight: 700;
    color: #181818;
    background: rgba(245, 250, 255, 0.98);
    border-radius: 16px 16px 0 0;
    padding: 0.95rem 0.5rem 0.7rem 0.5rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    letter-spacing: 0.01em;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(255,255,255,0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 48px; /* Adjust to match your grid card header height */
    display: flex;
    align-items: center;
    justify-content: center;
}


.industry-card-icon {
    font-size: 2.5rem;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.industry-card-icon img {
    width: 80px !important;
    height: 80px !important;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

.industry-card-bg,
.industry-card-icon img {
    display: none;
}

.industry-card-title-above {
    width: 100%;
    text-align: center;
    font-size: 1.15rem;
    font-weight: 700;
    color: var(--primary-blue);
    background: rgba(255,255,255,0.95);
    border-radius: 16px 16px 0 0;
    padding: 0.75rem 0.5rem 0.5rem 0.5rem;
    margin-bottom: 0.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    z-index: 2;
    position: relative;
    letter-spacing: 0.01em;
}

.industry-card-bg-overlay {
    min-height: 170px;
    height: 170px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 24px rgba(0,0,0,0.07);
    border: 1px solid var(--tertiary-gray-light);
    cursor: pointer;
    transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.18s cubic-bezier(.4,2,.6,1);
    padding-top: 2.5rem;
}

.industry-card-bg-overlay:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 8px 32px rgba(0,0,0,0.13);
    border-color: var(--primary-blue);
}

.industry-card-title,
.industry-card-title-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
    text-align: center;
    line-height: 1.3;
    background: none;
    padding: 0;
    margin: 0;
    text-shadow: 0 2px 8px rgba(255,255,255,0.85), 0 1px 2px rgba(0,0,0,0.08);
    width: 90%;
    word-break: break-word;
}

.industries-header-row {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.7rem;
    margin-top: 0.5rem;
}

.section-title {
    color: var(--primary-teal);
}

.section-subtitle {
    color: var(--primary-blue);
}

.industries-toggle-view {
    display: flex;
    gap: 0;
    background: #f2f7fd;
    border-radius: 32px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding: 0.18rem;
    margin-top: 0.2rem;
    position: relative;
}

.toggle-btn {
    background: transparent;
    color: var(--primary-blue);
    border: none;
    border-radius: 32px;
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: color 0.18s, background 0.18s;
    outline: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
}

.toggle-btn.active {
    background: var(--primary-blue);
    color: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}

.toggle-btn:not(.active):hover {
    background: #e0eafc;
    color: var(--primary-blue);
}



/* Modulescard.css */
/* All tag-related styles have been removed. */

.module-card {
    background-color: var(--primary-white);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--tertiary-gray-light);
    position: relative;
    max-width: 370px;
    min-width: 280px;
    margin: 0 auto;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.card-icon-container {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    border-radius: 8px;
    padding: 0.5rem;
    margin-right: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.card-category {
    font-size: 0.8rem;
    color: var(--secondary-teal-dark);
    text-transform: uppercase;
    font-weight: 500;
}

.card-description {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--tertiary-navy);
    flex-grow: 1;
    margin-bottom: 1rem;
}

.card-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid var(--tertiary-gray-light);
}

.card-actions {
    display: flex;
    gap: 0.7rem;
    justify-content: center;
    width: 100%;
}

.add-to-cart-btn, .try-now-btn {
    background-color: var(--secondary-gray-light);
    color: var(--primary-teal);
    border: 1px solid #b2e0ea;
    padding: 0.6rem 1.1rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.98rem;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    line-height: 1.2;
}
.add-to-cart-btn:hover {
    background: var(--primary-teal);
    color: var(--primary-white);
    transform: translateY(-2px);
}
.try-now-btn {
    background: var(--primary-blue);
    color: var(--primary-white);
}
.try-now-btn:hover {
    background: var(--primary-teal-dark);
    color: var(--primary-white);
    transform: translateY(-2px);
}

/* Suite tag styles for modules (copied from Suites.css) */
.suite-tags {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}
/*.suite-tag {
  background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
  color: var(--primary-white);
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.35rem 1.1rem;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(46,192,203,0.10);
  letter-spacing: 0.5px;
  margin: 0;
  min-width: 110px;
  text-align: center;
  white-space: nowrap;
  transition: background 0.2s;
}
.suite-tag:not(:last-child) {
  margin-bottom: 0.2rem;
}
.suite-tag.tag-best-seller { background: #7c3aed; color: #fff; }
.suite-tag.tag-top-rated { background: #ffb300; color: #222; }
.suite-tag.tag-recently-launched { background: #8bc34a; color: #fff; }
.suite-tag.tag-oem-focused { background: #222; color: #fff; }
.suite-tag.tag-included-in-suite { background: #5e35b1; color: #fff; }
.suite-tag.tag-mobile-ready-so-and-so { background: #0F5FDC; color: #fff; }*/

.card-tags {
  position: absolute;
  top: 0.8rem;
  right: 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}

.module-tag-badge {
  position: absolute;
  top: 0;
  right: 0;
  left: auto;
  background: #FFD700;
  color: #222;
  font-size: 0.95rem;
  font-weight: 700;
  padding: 0.1rem 0.5rem 0.1rem 0.5rem;
  border-top-right-radius: 12px;
  border-bottom-left-radius: 12px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(46,192,203,0.10);
  margin: 0;
  text-align: center;
  white-space: nowrap;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Custom colors for each tag */
.module-tag-badge.tag-bestseller { background: #ff9800; color: #000000; }
.module-tag-badge.tag-top-rated { background: #ffd600; color: #222; }
.module-tag-badge.tag-mobile-ready { background: #00b6e6; color: #fff; }
.module-tag-badge.tag-included-in-suits { background: #5e35b1; color: #fff; }
.module-tag-badge.tag-oem-focused { background: #222; color: #fff; }
.module-tag-badge.tag-trending { background: #8bc34a; color: #fff; }
.module-tag-badge.tag-best-value { background: #7c3aed; color: #fff; }
.module-tag-badge.tag-enterprise-ready { background: #0080c6; color: #fff; }
.module-tag-badge.tag-ai-powered { background: #0F5FDC; color: #fff; }
.module-tag-badge.tag-analytics-reporting { background: #00897b; color: #fff; }
.module-tag-badge.tag-customer-favorite { background: #ffb300; color: #222; } 

/* Modules.css */
.modules-section {
    width: 100vw;
    background: var(--primary-white);
    padding: 0;
    margin: 0;
    min-height: 100vh;
    overflow-x: hidden;
}

.modules-flex {
    display: flex;
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0;
}

.modules-main {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2.5vw 4rem 2.5vw;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    min-height: 100vh;
    overflow: visible;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    flex-shrink: 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-teal);
    margin-bottom: 0.5rem;
}

.section-subtitle {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
    color: var(--primary-blue);
}

.filter-container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 3rem;
}

.filter-btn {
    background-color: var(--primary-white);
    color: var(--secondary-teal-dark);
    border: 1px solid var(--secondary-gray-light);
    padding: 0.6rem 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    border-color: var(--primary-teal);
}

.filter-btn.active {
    background-color: var(--primary-teal);
    color: var(--primary-white);
    border-color: var(--primary-teal);
    box-shadow: 0 4px 15px rgba(46, 192, 203, 0.3);
}

.grid-center-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
}

.modules-grid {
    /* flex: 1 1 0; */
    /* height: 100%; */
    min-height: 0;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    grid-auto-rows: min-content;
    gap: 2.5rem;
    max-width: 100%;
    width: 100%;
    scrollbar-width: none;         /* Firefox */
    -ms-overflow-style: none;      /* IE 10+ */
    justify-items: center;
}

.modules-grid::-webkit-scrollbar {
    display: none;                 /* Chrome, Safari, Opera */
}

@media (min-width: 1400px) {
    .modules-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (min-width: 1800px) {
    .modules-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
@media (max-width: 900px) {
    .modules-grid {
        max-width: 100vw;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

.modules-grid.expanded {
    height: 100%;
    overflow-y: auto;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}
.modules-grid.expanded::-webkit-scrollbar {
    display: none !important;
}

.modules-grid.expanded::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modules-grid.expanded::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modules-grid.expanded::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.modules-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    gap: 2rem;
    flex-shrink: 0;
}

.filter-toggle-btn {
    background: var(--primary-teal);
    color: var(--primary-white);
    border: none;
    border-radius: 50px;
    padding: 0.7rem 1.6rem;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(46,192,203,0.13);
    transition: background 0.2s, transform 0.2s;
}

.filter-toggle-btn:hover {
    background: var(--primary-teal-dark);
    transform: translateY(-2px);
}

.view-more-row {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    flex-shrink: 0;
}

.view-more-btn {
    background: var(--primary-blue);
    color: var(--primary-white);
    border: none;
    border-radius: 50px;
    padding: 0.85rem 2.2rem;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(15,95,220,0.13);
    transition: background 0.2s, transform 0.2s;
}

.view-more-btn:hover {
    background: var(--primary-teal-dark);
    transform: translateY(-2px);
}

@media (max-width: 900px) {
    .modules-main {
        padding: 0 2vw 2rem 2vw;
        min-height: 100vh;
    }
    .modules-grid {
        gap: 1.2rem;
    }
    .modules-grid.expanded {
        max-height: calc(100vh - 150px);
    }
}

.solutions-main-flex {
    display: flex;
    height: 100vh;
    min-height: 0;
    align-items: stretch;
    overflow: hidden;
}

.sidebar-advanced-filter {
    height: 100%;
    min-height: 0;
    overflow-y: auto;
    flex-shrink: 0;
    scrollbar-width: none;         /* Firefox */
    -ms-overflow-style: none;      /* IE 10+ */
}

.sidebar-advanced-filter::-webkit-scrollbar {
    display: none;                 /* Chrome, Safari, Opera */
}

/* Keep custom scrollbar styles if desired */
.modules-grid::-webkit-scrollbar {
    width: 8px;
}
.modules-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}
.modules-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}
.modules-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.modules-grid.single-card {
    display: flex !important;
    justify-content: center;
    align-items: flex-start;
    max-width: 400px;
    margin: 0 auto;
}

.module-card {
    background-color: var(--primary-white);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--tertiary-gray-light);
    position: relative;
    max-width: 370px;
    min-width: 280px;
    margin: 0 auto;
} 

/* Partners.css */
.partners-section {
  padding: 4rem 0;
  background: linear-gradient(90deg, var(--primary-white) 60%, #e6fafd 100%);
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 1.2rem 1.5rem;
  align-items: center;
  justify-items: center;
  padding: 2.5rem 0 1.5rem 0;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.partners-row { display: none; }

.partner-logo-wrap {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
  max-width: 180px;
  height: 72px;
  background: var(--primary-white);
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(46,192,203,0.07);
  border: 1.5px solid var(--secondary-gray-light);
  transition: box-shadow 0.25s, transform 0.25s, background 0.2s, border 0.2s;
  cursor: pointer;
  opacity: 0;
  animation: logoFadeIn 0.7s cubic-bezier(.4,1.4,.6,1) forwards;
  scroll-snap-align: center;
  padding: 0.7rem 1.2rem;
  overflow: visible;
}
.partner-logo-wrap:nth-child(1) { animation-delay: 0.1s; }
.partner-logo-wrap:nth-child(2) { animation-delay: 0.18s; }
.partner-logo-wrap:nth-child(3) { animation-delay: 0.26s; }
.partner-logo-wrap:nth-child(4) { animation-delay: 0.34s; }
.partner-logo-wrap:nth-child(5) { animation-delay: 0.42s; }
.partner-logo-wrap:nth-child(6) { animation-delay: 0.50s; }
.partner-logo-wrap:nth-child(7) { animation-delay: 0.58s; }
.partner-logo-wrap:nth-child(8) { animation-delay: 0.66s; }
.partner-logo-wrap:nth-child(9) { animation-delay: 0.74s; }
.partner-logo-wrap:nth-child(10) { animation-delay: 0.82s; }
.partner-logo-wrap:nth-child(11) { animation-delay: 0.90s; }
.partner-logo-wrap:nth-child(12) { animation-delay: 0.98s; }
.partner-logo-wrap:nth-child(13) { animation-delay: 1.06s; }
.partner-logo-wrap:nth-child(14) { animation-delay: 1.14s; }
.partner-logo-wrap:nth-child(15) { animation-delay: 1.22s; }
.partner-logo-wrap:nth-child(16) { animation-delay: 1.30s; }
.partner-logo-wrap:nth-child(17) { animation-delay: 1.38s; }
.partner-logo-wrap:nth-child(18) { animation-delay: 1.46s; }
.partner-logo-wrap:nth-child(19) { animation-delay: 1.54s; }
.partner-logo-wrap:nth-child(20) { animation-delay: 1.62s; }
.partner-logo-wrap:nth-child(21) { animation-delay: 1.70s; }
.partner-logo-wrap:nth-child(22) { animation-delay: 1.78s; }
.partner-logo-wrap:nth-child(23) { animation-delay: 1.86s; }
@keyframes logoFadeIn {
  to {
    opacity: 1;
  }
}
.partner-logo-wrap:hover {
  box-shadow: 0 8px 32px rgba(46,192,203,0.18);
  transform: scale(1.10) translateY(-4px);
  background: var(--primary-white);
  border: 1.5px solid var(--primary-teal);
  z-index: 10;
}
.partner-logo {
  max-width: 110px;
  max-height: 54px;
  margin: 0 auto;
  display: block;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.04));
  transition: filter 0.2s;
  object-fit: contain;
}
.partner-logo-wrap:hover .partner-logo {
  filter: drop-shadow(0 4px 12px rgba(46,192,203,0.18));
}
.partner-tooltip {
  position: absolute;
  left: 50%;
  bottom: 110%;
  transform: translateX(-50%) scale(0.95);
  background: var(--primary-teal);
  color: var(--primary-white);
  padding: 0.4rem 1rem;
  border-radius: 6px;
  font-size: 0.95rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s, transform 0.2s;
  box-shadow: 0 2px 8px rgba(46,192,203,0.12);
  z-index: 10;
}
.partner-logo-wrap:hover .partner-tooltip {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}
.partner-testimonial {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-50%, 16px);
  min-width: 260px;
  max-width: 320px;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 8px 32px rgba(46,192,203,0.18), 0 2px 8px rgba(0,0,0,0.07);
  padding: 1.2rem 1.3rem 1rem 1.3rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.25s, transform 0.25s;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
  z-index: 20;
}
.partner-logo-wrap:hover .partner-testimonial {
  opacity: 1;
  pointer-events: auto;
  transform: translate(-50%, 0);
}
.testimonial-quote-icon {
  color: var(--primary-teal);
  font-size: 1.3rem;
  margin-bottom: 0.2rem;
}
.testimonial-quote {
  font-size: 1.08rem;
  font-style: italic;
  color: #222;
  margin-bottom: 0.2rem;
}
.testimonial-author {
  font-weight: 700;
  color: var(--primary-blue);
  font-size: 1.01rem;
}
.testimonial-title {
  font-size: 0.97rem;
  color: #888;
  margin-top: -0.2rem;
}
@media (max-width: 1200px) {
  .partners-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 900px) {
  .partners-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem 0.7rem;
  }
  .partner-logo-wrap {
    min-width: 100px;
    max-width: 120px;
    height: 44px;
    padding: 0.4rem 0.7rem;
  }
  .partner-logo {
    max-width: 70px;
    max-height: 32px;
  }
}
@media (max-width: 600px) {
  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.7rem 0.5rem;
  }
}

.section-title {
  color: var(--primary-teal);
}

.section-subtitle {
  color: var(--primary-blue);
} 

/* Pricing.css */
.pricing-section {
    padding: 5rem 0 2rem 0;
   /* background-color: var(--tertiary-gray-light);*/
}

.billing-toggle {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-bottom: 3rem;
}

.toggle-label {
    font-size: 1rem;
    font-weight: 500;
    color: var(--secondary-teal-dark);
    transition: color 0.3s ease;
}

.toggle-label.active {
    color: var(--tertiary-navy);
    font-weight: 700;
}

.save-badge {
    background-color: #ffd700;
    color: var(--tertiary-navy);
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin-left: 0.5rem;
}

/* The switch - the box around the slider */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

/* Hide default HTML checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* The slider */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--secondary-gray-light);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-teal);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.pricing-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
    justify-items: center;
    align-items: stretch;
    padding: 0 4.5rem 2rem 4.5rem;
    margin-bottom: 2.5rem;
    width: 100%;
    overflow-x: visible;
}

.pricing-grid {
    display: contents;
}

@media (max-width: 900px) {
    .pricing-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
        padding: 0 1rem 2rem 1rem;
    }
}

@media (max-width: 600px) {
    .pricing-row {
        grid-template-columns: 1fr;
        gap: 1.1rem;
        padding: 0 0.7rem 2rem 0.7rem;
    }
}

.feature-comparison-section {
    background: var(--primary-white);
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(46,192,203,0.08);
    margin: 0 auto;
    max-width: 1400px;
    padding: 2.5rem 3.5rem 2.5rem 3.5rem;
    overflow-x: auto;
}

.feature-comparison-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--tertiary-navy);
    margin-bottom: 1.5rem;
    text-align: left;
}

.feature-comparison-table-wrapper {
    overflow-x: auto;
}

.feature-comparison-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    min-width: 700px;
    background: transparent;
    text-align: left;
}

.feature-comparison-table th,
.feature-comparison-table td {
    padding: 1rem 1.5rem;
    text-align: left;
    font-size: 1rem;
    border-bottom: 1px solid var(--tertiary-gray-light);
    background: var(--primary-white);
}

/* Left-align User Limit values */
.feature-comparison-table td.user-limit-cell {
    text-align: left !important;
    font-weight: 600;
    color: var(--tertiary-navy);
}

.feature-comparison-table th {
    position: sticky;
    top: 0;
    background: var(--primary-white);
    z-index: 2;
    font-weight: 700;
    color: var(--primary-teal);
    border-bottom: 2px solid var(--primary-teal);
    text-align: left;
}

.feature-comparison-table td {
    text-align: left;
    vertical-align: middle;
}

.feature-comparison-table tr:last-child td {
    border-bottom: none;
}

@media (max-width: 700px) {
    .feature-comparison-table th,
    .feature-comparison-table td {
        padding: 0.7rem 0.5rem;
        font-size: 0.95rem;
    }
    .feature-comparison-title {
        font-size: 1.1rem;
    }
}

.container-fluid {
    width: 100vw;
    max-width: 100vw;
    margin: 0;
    padding: 0 0 0 0;
}

.feature-comparison-section.full-width {
    max-width: 100vw;
    border-radius: 0;
    margin: 0;
    padding: 1.5rem 4.5rem 1.5rem 4.5rem;
}

.feature-comparison-accordion {
    width: 100%;
    margin: 0 auto;
    background: var(--primary-white);
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(46,192,203,0.08);
    overflow: hidden;
}

.feature-category {
    border-bottom: 1.5px solid var(--tertiary-gray-light);
}

.feature-category-header {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--tertiary-navy);
    background: #f3f8fc;
    border: none;
    border-radius: 18px 18px 0 0;
    padding: 1.2rem 1.5rem 1.2rem 1.5rem;
    margin-bottom: 0;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background 0.18s, color 0.18s;
    box-shadow: 0 2px 8px rgba(46,192,203,0.04);
}

.feature-category-header span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-category-header:hover {
    background: var(--secondary-teal-extra-light);
}

.feature-category-header.open {
    background: var(--primary-teal);
    color: #fff;
}

.feature-category-header.open span {
    color: #fff;
}

.feature-category-body {
    background: var(--primary-white);
    padding: 0 2rem 2rem 2rem;
    animation: fadeInAccordion 0.3s;
}

@keyframes fadeInAccordion {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: none; }
}

@media (max-width: 900px) {
    .container-fluid, .feature-comparison-section.full-width {
        padding: 0 0.5rem 2rem 0.5rem;
    }
    .feature-category-header {
        font-size: 1rem;
        padding: 1rem 1.2rem 1rem 1rem;
    }
    .feature-category-body {
        padding: 0 0.5rem 1.2rem 0.5rem;
    }
}

.plan-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 0.7rem;
    min-height: 2.2rem;
}

.plan-name-with-icon {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 1.22rem;
    font-weight: 800;
    color: var(--primary-teal);
}

@media (max-width: 700px) {
    .plan-header-row {
        min-height: 1.7rem;
        margin-bottom: 0.5rem;
    }
    .plan-name-with-icon {
        font-size: 1.05rem;
        gap: 0.4rem;
    }
}

.section-title {
  color: var(--primary-teal);
}

.section-subtitle {
  color: var(--primary-blue);
} 

/* PricingCard.css */
.pricing-card {
    background-color: var(--primary-white);
    border: 1px solid var(--secondary-gray-light);
    border-radius: 12px;
    padding: 1.5rem 1.2rem 1.2rem 1.2rem;
    display: flex;
    flex-direction: column;
    min-width: 260px;
    max-width: 320px;
    min-height: 420px;
    height: 420px;
    justify-content: flex-start;
    align-items: stretch;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    box-shadow: 0 2px 12px rgba(46,192,203,0.07);
}

.pricing-card.popular {
    border-color: var(--primary-teal);
    transform: scale(1.05);
    box-shadow: 0 10px 40px rgba(46, 192, 203, 0.2);
    z-index: 10;
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-teal);
    color: var(--primary-white);
    padding: 0.4rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 700;
}

.pricing-card-header {
    text-align: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--tertiary-gray-light);
}

.plan-name {
    font-size: 1.15rem;
    font-weight: 700;
    color: var(--primary-teal);
    margin-bottom: 0.7rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: 800;
    color: var(--tertiary-navy);
    margin-bottom: 0.1rem;
    text-align: left;
    min-height: 3.2rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.price-amount {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--tertiary-navy);
}

.price-period, .price-contact {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--secondary-teal-dark);
    margin-left: 0.4rem;
}

.price-contact {
    font-size: 1.1rem;
    font-weight: 700;
}

.features-list {
    list-style: none;
    margin: 1rem 0 0.5rem 0;
    flex-grow: 1;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.7rem;
    font-size: 0.93rem;
}

.feature-icon {
    color: var(--primary-teal);
    font-size: 1.1rem;
    margin-right: 0.6rem;
}

.pricing-card-footer {
    text-align: center;
    margin-top: auto;
    padding-top: 0.7rem;
}

.button-pricing {
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    border: 2px solid var(--primary-teal);
}

/* Re-using button styles where appropriate */
.button-pricing.button-primary {
    background-color: var(--primary-teal);
    color: var(--primary-white);
}

.button-pricing.button-secondary {
    background-color: transparent;
    color: var(--primary-teal);
}

.button-pricing.button-secondary:hover {
    background-color: var(--primary-teal);
    color: var(--primary-white);
}

.plan-quickview {
    list-style: none;
    margin: 1rem 0 0.7rem 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.plan-quickview-item {
    display: flex;
    align-items: center;
    font-size: 1.01rem;
    color: var(--tertiary-navy);
    gap: 0.4rem;
    font-weight: 500;
}

.plan-quickview-label {
    margin-right: 0.3rem;
    font-weight: 500;
    color: var(--tertiary-navy);
}

.plan-quickview-value {
    font-weight: 700;
    color: #181818;
    margin-left: 0.1rem;
}

.pricing-summary-card {
    background: #fff;
    border-radius: 10px;
    border: 1.5px solid var(--secondary-gray-light);
    padding: 1.3rem 1.3rem 1.3rem 1.3rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    max-width: 340px;
    min-width: 220px;
    min-height: 380px;
    height: 100%;
    margin: 0 0.5rem;
    position: relative;
    transition: border 0.18s, background 0.18s;
    box-shadow: none;
    overflow: visible;
    word-break: break-word;
    box-sizing: border-box;
}

.pricing-summary-card.popular {
    background: #e6fafd;
    border: 2px solid var(--primary-teal);
}

.most-popular-badge {
    position: absolute;
    top: 0;
    right: 0;
    left: auto;
    transform: none;
    background: #FFD700;
    color: var(--tertiary-navy);
    font-size: 0.95rem;
    font-weight: 700;
    padding: 0.45rem 1.2rem 0.45rem 1.2rem;
    border-radius: 0 7px 0 7px;
    letter-spacing: 0.01em;
    text-transform: uppercase;
    z-index: 3;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(46,192,203,0.10);
}

.plan-name {
    font-size: 1.22rem;
    font-weight: 800;
    color: var(--primary-teal);
    margin-bottom: 0.3rem;
    margin-top: 0.2rem;
    text-align: left;
}

.plan-summary {
    font-size: 1.01rem;
    color: #333;
    margin-bottom: 0.7rem;
    text-align: left;
    min-height: 38px;
}

.plan-quickview {
    list-style: none;
    margin: 0.7rem 0 0.7rem 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.plan-quickview-item {
    display: flex;
    align-items: center;
    font-size: 1.01rem;
    color: var(--tertiary-navy);
    gap: 0.4rem;
    font-weight: 500;
    text-align: left;
}

.plan-quickview-label {
    margin-right: 0.3rem;
    font-weight: 500;
    color: var(--tertiary-navy);
}

.plan-quickview-value {
    font-weight: 700;
    color: #181818;
    margin-left: 0.1rem;
}

.button-pricing.button-primary {
    margin-top: auto;
    background: var(--primary-teal);
    color: #fff;
    font-weight: 700;
    border-radius: 8px;
    font-size: 1.08rem;
    border: none;
    width: 100%;
    padding: 0.8rem 0;
    transition: background 0.18s, color 0.18s;
    box-shadow: none;
}

.button-pricing.button-primary:hover {
    background: var(--primary-teal);
    color: #fff;
}

@media (max-width: 700px) {
    .plan-header-row {
        margin-top: 0.1rem;
    }
}

.plan-separator {
    border: none;
    border-top: 1.5px solid var(--secondary-gray-light);
    margin: 1rem 0 0.7rem 0;
    width: 100%;
}

@media (max-width: 900px) {
    .pricing-summary-card {
        width: 100%;
        min-width: 160px;
        max-width: 98vw;
        min-height: 380px;
        padding: 1.1rem 0.7rem 1.1rem 0.7rem;
    }
    .plan-price {
        font-size: 1.3rem;
        min-height: 2.2rem;
    }
}

@media (max-width: 600px) {
    .pricing-summary-card {
        width: 100%;
        min-width: 120px;
        max-width: 99vw;
        min-height: 380px;
        padding: 0.7rem 0.3rem 0.7rem 0.3rem;
    }
    .plan-summary, .plan-header-row, .plan-price, .plan-quickview, .button-pricing {
        word-break: break-word;
        font-size: 0.97rem;
    }
} 

/* SidebarAdvancedfilter.css */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 32, 64, 0.18);
  z-index: 1001;
  transition: opacity 0.3s cubic-bezier(.4,0,.2,1);
  opacity: 1;
}

.sidebar-advanced-filter {
  background: #fff;
  min-width: 220px;
  max-width: 270px;
  width: 100%;
  box-shadow: 0 2px 8px 0 rgba(0,64,128,0.04);
  border-radius: 14px;
  padding: 18px 14px 12px 14px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  transition: transform 0.3s cubic-bezier(.4,0,.2,1), box-shadow 0.3s;
  z-index: 1002;
  height: 100%;
  border: 1px solid #e0e6ed;
}

.sidebar-advanced-filter.desktop {
  position: sticky;
  top: 80px;
  height: calc(100vh - 80px);
}

.sidebar-advanced-filter.mobile {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  min-width: 80vw;
  max-width: 90vw;
  border-radius: 0 22px 22px 0;
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
  transform: translateX(-110%);
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
}

.sidebar-advanced-filter.open {
  transform: translateX(0) !important;
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
}

.sidebar-advanced-filter.open.desktop {
  position: sticky !important;
  top: 0px !important;
  height: 100% !important;
  z-index: 1002;
}

/*.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}*/

.sidebar-header h3 {
  font-size: 1.05rem;
  font-weight: 700;
  color: #006080;
  display: flex;
  align-items: center;
  gap: 6px;
  letter-spacing: 0;
}

.sidebar-close-btn {
  background: none;
  border: none;
  font-size: 1.3rem;
  color: #0080c6;
  cursor: pointer;
  margin-left: 4px;
  line-height: 1;
  padding: 0 2px;
  transition: color 0.2s;
}
.sidebar-close-btn:hover {
  color: #00b6e6;
}

.sidebar-content {
  flex: 1 1 auto;
  overflow-y: auto;
  height: 100%;
  max-height: 100%;
  padding-bottom: 4px;
}

.filter-group {
  margin-bottom: 16px;
  padding-bottom: 0;
  border-bottom: none;
}
.filter-group:not(:last-child) {
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 10px;
}
.filter-label {
  font-size: 0.98rem;
  font-weight: 700;
  color: #006080;
  margin-bottom: 6px;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}
.filter-icon {
  font-size: 1.1em;
  color: #00b6e6;
  margin-right: 2px;
}
.filter-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  background: none;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;
  box-shadow: none;
}
.filter-pill {
  font-size: 0.97rem;
  font-weight: 500;
  color: #0080c6;
  background: #f2f8fa;
  border: none;
  border-radius: 12px;
  padding: 6px 14px;
  margin: 1px 2px 1px 0;
  transition: background 0.2s, color 0.2s;
  min-width: 0;
}
.filter-pill.selected {
  background: #00b6e6;
  color: #fff;
}
.filter-search {
  width: 100%;
  padding: 7px 10px;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  font-size: 0.97rem;
  margin-bottom: 4px;
  background: #fff;
  font-weight: 400;
}

.filter-link {
  background: none;
  border: none;
  color: #222;
  font-size: 0.97rem;
  font-weight: 500;
  text-align: left;
  padding: 4px 10px;
  margin: 0 0 2px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s, color 0.2s;
  width: 100%;
  display: block;
}
.filter-link.selected,
.filter-link:focus,
.filter-link:hover {
  background: #00b6e6;
  color: #fff;
}

.industry-image-thumb {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  max-width: 40px;
  max-height: 40px;
  display: inline-block;
  margin-right: 12px;
  vertical-align: middle;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  background-color: #f3f8fc;
  box-sizing: border-box;
}

.industry-checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.97rem;
  font-weight: 500;
  color: #222;
  margin-bottom: 6px;
  cursor: pointer;
  padding: 4px 0;
  min-height: 48px;
  width: 100%;
}
.industry-checkbox-label input[type="checkbox"],
.industry-image-thumb {
  flex-shrink: 0;
}
.industry-label-text {
  white-space: normal;
  word-break: break-word;
  flex: 1 1 0%;
  min-width: 0;
  display: block;
}
.industry-checkbox-label input[type="checkbox"] {
  accent-color: #00b6e6;
  margin-right: 2px;
}
.industry-checkbox-label:hover {
  background: #e0f4fa;
  color: #00b6e6;
  border-radius: 6px;
}
.industry-checkbox-label input[type="checkbox"]:checked + .industry-icon,
.industry-checkbox-label input[type="checkbox"]:checked ~ span {
  color: #00b6e6;
}

.solution-type-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.solution-type-radio-label {
  display: flex;
  align-items: center;
  gap: 7px;
  font-size: 0.97rem;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
  cursor: pointer;
  padding: 2px 0;
}
.solution-type-radio-label input[type="radio"] {
  accent-color: #00b6e6;
  margin-right: 2px;
}
.solution-type-radio-label:hover {
  background: #e0f4fa;
  color: #00b6e6;
  border-radius: 6px;
}
.solution-type-radio-label input[type="radio"]:checked + span {
  color: #00b6e6;
}

.filter-divider {
  border: none;
  border-top: 1px solid #f0f2f5;
  margin: 10px 0 10px 0;
}

.sidebar-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 18px;
  margin-top: 16px;
}
.clear-filters-btn {
  color: #e53935;
  font-weight: 600;
  background: none;
  border: none;
  padding: 0;
  font-size: 0.97rem;
  cursor: pointer;
  transition: color 0.2s;
  text-align: center;
}
.clear-filters-btn:hover {
  text-decoration: underline;
  color: #b71c1c;
}
.result-count {
  font-size: 0.97rem;
  color: #0080c6;
  font-weight: 500;
  text-align: center;
}

/* Compact checkbox style for industries */
.industry-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
  object-fit: contain;
  display: inline-block;
}

/* Industry search */
.industry-search-box {
  width: 100%;
  padding: 7px 10px;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  font-size: 0.97rem;
  margin-bottom: 6px;
  background: #fff;
  font-weight: 400;
}

.industry-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 245px;
  overflow-y: auto;
}

.industry-pill {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f2f8fa;
  color: #0080c6;
  border: none;
  border-radius: 12px;
  padding: 6px 10px;
  font-size: 0.97rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.industry-pill.selected {
  background: #00b6e6;
  color: #fff;
}

@media (max-width: 900px) {
  .sidebar-advanced-filter.desktop {
    display: none !important;
  }
  .sidebar-advanced-filter.mobile {
    display: block;
    min-width: 70vw;
    max-width: 90vw;
    border-radius: 0 14px 14px 0;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
    height: 100%;
    padding: 14px 6px 10px 10px;
  }
}

@media (min-width: 901px) {
  .solutions-main-flex {
    align-items: stretch !important;
  }
  .sidebar-advanced-filter.desktop {
    position: static !important;
    top: auto !important;
    height: auto !important;
    min-height: 0 !important;
    align-self: stretch !important;
    display: flex;
    flex-direction: column;
    flex: 0 0 270px;
    max-width: 270px;
    width: 100%;
  }
  .sidebar-content {
    flex: 1 1 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .sidebar-advanced-filter.mobile {
    display: none !important;
  }
}
@media (min-width: 901px) {
    .sidebar-advanced-filter.desktop {
        position: sticky !important;
        top: 0px !important;
        /* height: calc(100vh - 100px) !important; */
        min-height: 400px;
        z-index: 1002;
        height: 100% !important;
    }
}
/* Grid expands when sidebar is hidden */
.grid-full {
  width: 100% !important;
  max-width: 100vw !important;
  margin-left: 0 !important;
}

/* Prevent horizontal scroll */
body, html, .solutions-section, .solutions-main-flex, .solutions-content {
  overflow-x: hidden !important;
} 

/* Suites.css */
.suites-section {
  width: 100vw;
  background: var(--primary-white);
  padding: 0;
  margin: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

.suites-flex {
  display: flex;
  width: 100vw;
  max-width: 100vw;
  margin: 0;
  padding: 0;
}

.suites-main {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2.5vw 4rem 2.5vw;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 100vh;
  overflow: visible;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
  flex-shrink: 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-teal);
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
  color: var(--primary-blue);
}

.suites-grid {
  /* flex: 1 1 0; */
  /* height: 100%; */
  min-height: 0;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  grid-auto-rows: min-content;
  gap: 2.5rem;
  max-width: 100%;
  width: 100%;
  scrollbar-width: none;         /* Firefox */
  -ms-overflow-style: none;      /* IE 10+ */
}

.suites-grid.expanded {
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
.suites-grid.expanded::-webkit-scrollbar {
  display: none !important;
}

.suites-grid.expanded::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.suites-grid.expanded::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.suites-grid.expanded::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.suites-grid.single-card {
  display: flex !important;
  justify-content: center;
  align-items: flex-start;
  max-width: 400px;
  margin: 0 auto;
}

.suite-card,
.suite-card.module-card,
.suite-card.module-card.left-align {
  position: relative;
}

.suite-card {
  position: relative;
  float: none;
  height: auto;
  min-height: unset;
  background: var(--tertiary-gray-light);
  border-radius: 18px;
  box-shadow: 0 6px 32px rgba(46,192,203,0.08);
  padding: 2.5rem 2rem 2rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  transition: box-shadow 0.3s, transform 0.3s;
}
.suite-card:hover {
  box-shadow: 0 12px 48px rgba(46,192,203,0.18);
  transform: translateY(-6px) scale(1.03);
}
.suite-icon {
  font-size: 2.8rem;
  color: var(--primary-teal);
  margin-bottom: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.suite-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--tertiary-navy);
  margin-bottom: 0.5rem;
}
.suite-desc {
  font-size: 1.05rem;
  color: var(--secondary-teal-dark);
  margin-bottom: 1.2rem;
}
.suite-modules {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  width: 100%;
}
.suite-modules li {
  font-size: 1rem;
  color: var(--tertiary-navy);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}
.suite-module-icon {
  color: var(--primary-teal);
  font-size: 1.1rem;
  margin-right: 0.5rem;
}
.suite-cta {
  align-self: flex-end;
  margin-top: auto;
  font-size: 1.1rem;
  padding: 0.8rem 2rem;
  border-radius: 50px;
  font-weight: 700;
  box-shadow: 0 4px 16px rgba(46,192,203,0.12);
  transition: background 0.2s, color 0.2s, transform 0.2s;
}
.suite-cta:hover {
  background: var(--primary-blue);
  color: var(--primary-white);
  transform: scale(1.05);
}
.suite-tag {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
  color: var(--primary-white);
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.35rem 1.1rem;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(46,192,203,0.10);
  z-index: 2;
  letter-spacing: 0.5px;
}
.suite-actions {
  display: flex;
  gap: 0.7rem;
  margin-top: auto;
  align-self: flex-end;
}
.suite-cart-btn, .suite-try-btn {
  background-color: var(--secondary-gray-light);
  color: var(--primary-teal);
  border: none;
  padding: 0.6rem 1.1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.98rem;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}
.suite-cart-btn:hover {
  background: var(--primary-teal);
  color: var(--primary-white);
  transform: translateY(-2px);
}
.suite-try-btn {
  background: var(--primary-blue);
  color: var(--primary-white);
}
.suite-try-btn:hover {
  background: var(--primary-teal-dark);
  color: var(--primary-white);
  transform: translateY(-2px);
}
@media (max-width: 900px) {
  .suites-main {
    padding: 0 2vw 2rem 2vw;
    min-height: 100vh;
  }
  .suites-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 6px 24px 6px;
  }
  .suites-grid.expanded {
    max-height: calc(100vh - 150px);
  }
}

.suite-tags {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}
.suite-tag {
  background: linear-gradient(90deg, var(--primary-teal), var(--primary-blue));
  color: var(--primary-white);
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.35rem 1.1rem;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(46,192,203,0.10);
  letter-spacing: 0.5px;
  margin: 0;
  min-width: 110px;
  text-align: center;
  white-space: nowrap;
  transition: background 0.2s;
}
.suite-tag:not(:last-child) {
  margin-bottom: 0.2rem;
}
.suite-tag.tag-best-seller { background: #7c3aed; color: #fff; }
.suite-tag.tag-top-rated { background: #ffb300; color: #222; }
.suite-tag.tag-recently-launched { background: #8bc34a; color: #fff; }
.suite-tag.tag-oem-focused { background: #222; color: #fff; }
.suite-tag.tag-included-in-suite { background: #5e35b1; color: #fff; }
.suite-tag.tag-mobile-ready-so-and-so { background: #0F5FDC; color: #fff; }

.suites-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  gap: 2rem;
  flex-shrink: 0;
}

.filter-toggle-btn {
  background: var(--primary-teal);
  color: var(--primary-white);
  border: none;
  border-radius: 50px;
  padding: 0.7rem 1.6rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(46,192,203,0.13);
  transition: background 0.2s, transform 0.2s;
}
.filter-toggle-btn:hover {
  background: var(--primary-teal-dark);
  transform: translateY(-2px);
}

.view-more-row {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
  flex-shrink: 0;
}

.view-more-btn {
  background: var(--primary-blue);
  color: var(--primary-white);
  border: none;
  border-radius: 50px;
  padding: 0.85rem 2.2rem;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(15,95,220,0.13);
  transition: background 0.2s, transform 0.2s;
}
.view-more-btn:hover {
  background: var(--primary-teal-dark);
  transform: translateY(-2px);
}

.solutions-section {
  width: 100vw;
  background: var(--primary-white);
  padding: 0;
  margin: 0;
}

.solutions-main {
  width: 100vw;
  max-width: 100vw;
  margin: 0 auto;
  padding: 0 2vw 4rem 2vw;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.solutions-header-row {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-bottom: 2.5rem;
  gap: 2rem;
}

.solutions-tabs-row.center-align {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  margin: 0 auto 24px auto;
  width: 100%;
  max-width: 900px;
}

.solutions-tabs {
  display: flex;
  gap: 8px;
}

.solutions-tab {
  background: #f2f8fa;
  color: #0080c6;
  border: none;
  border-radius: 22px;
  padding: 10px 28px;
  font-size: 1.08rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.solutions-tab.active {
  background: #00b6e6;
  color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
}

@media (max-width: 900px) {
  .solutions-main {
    padding: 0 0.5vw 2rem 0.5vw;
  }
  .solutions-header-row {
    flex-direction: column;
    align-items: stretch;
    gap: 1.2rem;
  }
  .solutions-tabs {
    justify-content: center;
    gap: 0.5rem;
    padding: 0.2rem 0.2rem;
  }
  .solutions-tab {
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
  }
}

.solutions-filter-card {
  width: 100%;
  background: var(--primary-white);
  border-radius: 22px;
  box-shadow: 0 6px 32px rgba(46,192,203,0.10);
  margin-bottom: 2.5rem;
  padding: 2.2rem 2vw 1.2rem 2vw;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 1.2rem;
}

.solutions-filter-header {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.2rem;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
}

.filter-toggle-btn {
  background: var(--primary-teal);
  color: var(--primary-white);
  border: none;
  border-radius: 50px;
  padding: 0.6rem 1.5rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(46,192,203,0.13);
  transition: background 0.2s, transform 0.2s;
}
.filter-toggle-btn:hover {
  background: var(--primary-teal-dark);
  transform: translateY(-2px);
}

.solutions-result-count {
  font-size: 1.08rem;
  color: var(--primary-teal);
  font-weight: 600;
  margin: 0 0.5rem;
}

.clear-filters-btn {
  background: var(--secondary-gray-light);
  color: var(--tertiary-navy);
  border: none;
  border-radius: 50px;
  padding: 0.6rem 1.5rem;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.clear-filters-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.clear-filters-btn:not(:disabled):hover {
  background: var(--primary-teal);
  color: var(--primary-white);
}

@media (max-width: 900px) {
  .solutions-filter-card {
    padding: 1.2rem 1vw 0.7rem 1vw;
    border-radius: 16px;
    gap: 0.7rem;
  }
  .solutions-filter-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
}

.solutions-main-flex {
  display: flex;
  height: 100vh;
  min-height: 0;
  align-items: stretch;
  overflow: hidden;
}

.solutions-content {
  flex: 1 1 0;
  min-width: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.modern-toggle {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  margin: 0 auto 32px auto;
  width: 100%;
  max-width: 900px;
}
.toggle-group {
  display: flex;
  background: #f2f8fa;
  border-radius: 32px;
  box-shadow: 0 2px 8px rgba(0,128,198,0.04);
  padding: 4px;
  gap: 0;
}
.toggle-btn {
  background: none;
  border: none;
  color: #0080c6;
  font-size: 1.08rem;
  font-weight: 600;
  border-radius: 28px;
  padding: 10px 32px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  margin: 0 2px;
}
.toggle-btn.active {
  background: #00b6e6;
  color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
}
.sidebar-toggle-btn {
  display: flex;
  align-items: center;
  background: #00b6e6;
  color: #fff;
  border: none;
  border-radius: 22px;
  padding: 10px 24px;
  font-size: 1.05rem;
  font-weight: 600;
  cursor: pointer;
  margin-left: 12px;
  box-shadow: 0 2px 12px 0 rgba(0,128,198,0.08);
  transition: background 0.2s, color 0.2s;
  gap: 6px;
}
.sidebar-toggle-btn:hover {
  background: #0080c6;
}

@media (max-width: 900px) {
  .modern-toggle {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 18px;
  }
  .toggle-btn {
    padding: 10px 18px;
    font-size: 1rem;
  }
}
/* Sticky sidebar for desktop */
@media (min-width: 901px) {
  .sidebar-advanced-filter.desktop {
    position: sticky !important;
    top: 100px !important;
    height: calc(100vh - 100px) !important;
    min-height: 400px;
    z-index: 1002;
  }
}

.suites-grid,
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  transition: max-width 0.3s, padding 0.3s;
  padding: 0 16px 32px 16px;
  scrollbar-width: none;         /* Firefox */
  -ms-overflow-style: none;      /* IE 10+ */
}

.suites-grid.grid-expanded,
.modules-grid.grid-expanded {
  display: grid;
  grid-template-columns: repeat(4, minmax(260px, 1fr));
  width: 100vw !important;
  max-width: 100vw !important;
  margin: 0 !important;
  padding: 0 16px 32px 16px;
  background: none;
}

@media (max-width: 1200px) {
  .suites-grid.grid-expanded, .modules-grid.grid-expanded {
    grid-template-columns: repeat(2, minmax(260px, 1fr));
  }
}
@media (max-width: 700px) {
  .suites-grid.grid-expanded, .modules-grid.grid-expanded {
    grid-template-columns: 1fr;
  }
}

.industry-icon {
  width: 32px;
  height: 32px;
  margin-right: 10px;
  vertical-align: middle;
  object-fit: contain;
  display: inline-block;
}

.suite-card.module-card {
  background: var(--primary-white);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--tertiary-gray-light);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 420px;
  position: relative;
  transition: box-shadow 0.3s, transform 0.3s;
  margin: 0;
  gap: 0;
}
.suite-card.module-card:hover {
  box-shadow: 0 6px 32px 0 rgba(0,128,198,0.13);
  transform: translateY(-4px) scale(1.02);
}
.suite-card.module-card .card-tags {
  position: absolute;
  top: 0.8rem;
  right: 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
  z-index: 3;
}
.suite-card.module-card .card-tag {

  font-size: 0.85rem;
  font-weight: 700;
  padding: 0.32rem 1.1rem;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(46,192,203,0.10);
  letter-spacing: 0.5px;
  margin: 0;
  min-width: 110px;
  text-align: center;
  white-space: nowrap;
  transition: background 0.2s;
}
.card-tag.tag-best\ seller { background: #ff9800; color: #fff; }
.card-tag.tag-recently\ launched { background: #8bc34a; color: #fff; }
.card-tag.tag-top\ rated { background: #ffd600; color: #222; }
.card-tag.tag-oem\ focused { background: #222; color: #fff; }

.suite-card.module-card .card-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 1.2rem;
  width: 100%;
  /*gap: 16px;*/
}
.suite-card.module-card .card-icon-container {
  background-color: var(--primary-teal);
  color: var(--primary-white);
  border-radius: 8px;
  padding: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.2rem;
  margin: 0 auto;
}
.suite-card.module-card .card-title-container {
  width: 100%;
  text-align: center;
  margin-bottom: 0.5rem;
}
.suite-card.module-card .card-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.1rem;
  color: var(--tertiary-navy);
}
.suite-card.module-card .card-description {
  font-size: 1.05rem;
  color: var(--secondary-teal-dark);
  margin-bottom: 0.5rem;
  text-align: left;
}
.suite-includes {
  font-size: 0.98rem;
  color: #222;
  margin-bottom: 1.2rem;
  margin-top: 0.2rem;
  text-align: left;
  width: 100%;
}
.suite-includes-label {
  color: #0080c6;
  font-weight: 700;
  margin-right: 0.2rem;
}
.suite-card.module-card .card-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid var(--tertiary-gray-light);
  width: 100%;
  margin-top: auto;
}
.suite-card.module-card .card-actions {
  display: flex;
  gap: 0.7rem;
  width: 100%;
  justify-content: center;
}
.suite-card.module-card .add-to-cart-btn, .suite-card.module-card .try-now-btn {
  background-color: var(--secondary-gray-light);
  color: var(--primary-teal);
  border: 1px solid #b2e0ea;
  padding: 0.6rem 1.1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.98rem;
  transition: background 0.2s, color 0.2s, transform 0.2s;
}
.suite-card.module-card .add-to-cart-btn:hover {
  background: var(--primary-teal);
  color: var(--primary-white);
  transform: translateY(-2px);
}
.suite-card.module-card .try-now-btn {
  background: var(--primary-blue);
  color: var(--primary-white);
  border: none;
}
.suite-card.module-card .try-now-btn:hover {
  background: var(--primary-teal-dark);
  color: var(--primary-white);
  transform: translateY(-2px);
}

@media (max-width: 900px) {
  .solutions-tabs-row.center-align {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 18px;
  }
  .suites-grid,
  .modules-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 6px 24px 6px;
  }
  .suites-grid.grid-expanded,
  .modules-grid.grid-expanded {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.solutions-centered-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 16px 0 16px; 
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-header.centered {
  text-align: center;
  margin-bottom: 24px;
}

.section-header.centered .section-title {
  font-size: 2.6rem;
  font-weight: 800;
  margin-bottom: 8px;
  letter-spacing: -1px;
}

.section-header.centered .section-subtitle {
  font-size: 1.18rem;
  color: #0080c6;
  font-weight: 400;
  margin-bottom: 0;
}

.solutions-tabs-row.center-align {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  margin: 0 auto 32px auto;
  width: 100%;
  max-width: 900px;
}

@media (max-width: 900px) {
  .solutions-centered-container {
    padding: 32px 6px 0 6px;
  }
  .section-header.centered .section-title {
    font-size: 2rem;
  }
  .solutions-tabs-row.center-align {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 18px;
  }
}

/* Advanced filter: visually differentiate group titles and options */
.filter-label {
  font-size: 1.18rem;
  font-weight: 800;
  color: #0080c6;
  margin-bottom: 12px;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 0 #e6f3fa;
}
.filter-pills {
  background: #f7fbfd;
  border-radius: 12px;
  padding: 8px 6px 4px 6px;
  margin-bottom: 2px;
  box-shadow: 0 1px 4px 0 rgba(0,128,198,0.03);
}
.filter-pill {
  font-size: 1.02rem;
  font-weight: 600;
  color: #0080c6;
  background: #e6f3fa;
  border: none;
  border-radius: 16px;
  padding: 8px 18px;
  margin: 2px 4px 2px 0;
  transition: background 0.2s, color 0.2s;
}
.filter-pill.selected {
  background: #0080c6;
  color: #fff;
}

.suite-card.module-card.left-align {
  align-items: flex-start;
  text-align: left;
}
.suite-card.module-card.left-align .card-header {
  justify-content: flex-start;
}
.suite-card.module-card.left-align .card-title-container {
  text-align: left;
}
.suite-card.module-card.left-align .card-description,
.suite-card.module-card.left-align .suite-includes {
  text-align: left;
}

.sidebar-footer {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 16px;
}
.clear-filters-btn {
  color: #e53935;
  font-weight: 600;
  background: none;
  border: none;
  padding: 0;
  font-size: 0.97rem;
  cursor: pointer;
  transition: color 0.2s;
  text-align: left;
  text-decoration: underline;
}
.clear-filters-btn:hover {
  color: #b71c1c;
}
.result-count {
  font-size: 0.97rem;
  color: #0080c6;
  font-weight: 500;
  text-align: left;
}

.solutions-content.scrollable {
  overflow-y: auto;
  height: 100%;
  max-height: none;
  scroll-behavior: smooth;
}

.sidebar-advanced-filter {
  overflow-y: auto !important;
  scrollbar-width: none !important;         /* Firefox */
  -ms-overflow-style: none !important;      /* IE 10+ */
}
.sidebar-advanced-filter::-webkit-scrollbar {
  width: 0 !important;
  background: transparent !important;
  display: none !important;                 /* Chrome, Safari, Opera */
}

.suites-grid::-webkit-scrollbar {
  display: none;                 /* Chrome, Safari, Opera */
}

.grid-center-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
}



@media (min-width: 1400px) {
  .suites-main {
    max-width: 1440px;
    padding: 0 2vw 4rem 2vw;
  }
  .suites-grid {
    max-width: 100%;
    width: 100%;
  }
}

.advanced-filter {
  box-sizing: border-box;
}

.grid-center-wrapper {
  box-sizing: border-box;
}

.suites-main {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2.5vw 4rem 2.5vw;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 100vh;
  overflow: visible;
}

.suites-grid {
  max-width: 100%;
  width: 100%;
}

.suites-grid.expanded {
  padding-right: 0;
}

 
/* Index.css-Vibha */
:root {
  /* Font Family */
  /* --font-primary: 'HCLTechRoobert', sans-serif; */

  /* Primary Colors */
  --primary-teal: #2EC0CB;
  --primary-teal-dark: #23A3AD;
  --primary-blue: #0F5FDC;
  --primary-white: #FFFFFF;

  /* Secondary Colors */
  --secondary-teal-dark: #17707F;
  --secondary-teal-light: #36D6D9;
  --secondary-teal-extra-light: #A4F4FF;
  --secondary-blue-light: #3C91FF;
  --secondary-blue-extra-light: #8CC8FA;
  --secondary-gray-light: #DCE6F0;

  /* Tertiary Colors */
  --tertiary-navy: #000032;
  --tertiary-gray-light: #ECF3F8;
  --tertiary-black: #000000;

  /* font-family: "HCL Roobert", Inter, system-ui, Avenir, Helvetica, Arial, sans-serif; */
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  /* font-family: var(--font-primary); */
  background-color: var(--primary-white);
  color: var(--tertiary-navy);
  line-height: 1.6;
  font-weight: 400; /* Regular */
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

img, picture, svg, video {
    display: block;
    max-width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700; /* Bold */
  line-height: 1.2;
}

.container {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding-left: 3.5vw;
  padding-right: 3.5vw;
}

@media (max-width: 900px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

.about-section {
  width: 100vw;
  background: var(--primary-white);
  padding: 5rem 0 3rem 0;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.about-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.about-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-teal);
  margin-bottom: 1.5rem;
  /* font-family: 'HCLTechRoobert', sans-serif; */
}

.about-description {
  font-size: 1.25rem;
  color: var(--tertiary-navy);
  /* font-family: 'HCLTechRoobert', sans-serif; */
  line-height: 1.7;
}

/* Custom thin dark teal scrollbar for all scrollable areas */
::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-teal-dark, #008080);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #006666;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-teal-dark, #008080) transparent;
}

/* Global Button Styles */
.btn {
  display: inline-block;
  /* font-family: inherit; */
  font-size: 1rem;
  font-weight: 600;
  border-radius: 50px;
  padding: 0.7rem 1.6rem;
  border: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
  box-shadow: 0 2px 10px rgba(46,192,203,0.13);
}

.btn-primary {
  background: var(--primary-teal);
  color: var(--primary-white);
}

.btn-primary:hover, .btn-primary:focus {
  background: var(--primary-teal-dark);
  color: var(--primary-white);
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--primary-white);
  color: var(--primary-teal);
  border: 2px solid var(--primary-teal);
}

.btn-secondary:hover, .btn-secondary:focus {
  background: var(--primary-teal);
  color: var(--primary-white);
  border-color: var(--primary-teal-dark);
  transform: translateY(-2px);
}

/* Small Button Modifier */
.btn-sm {
  padding: 0.45rem 1rem;
  font-size: 0.95rem;
  border-radius: 24px;
  box-shadow: none;
}

/* SidebarAdvanced.css */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 32, 64, 0.18);
  z-index: 1001;
  transition: opacity 0.3s cubic-bezier(.4,0,.2,1);
  opacity: 1;
}

.sidebar-advanced-filter {
  background: #fff;
  min-width: 220px;
  max-width: 270px;
  width: 100%;
  box-shadow: 0 2px 8px 0 rgba(0,64,128,0.04);
  border-radius: 14px;
  padding: 18px 14px 12px 14px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  transition: transform 0.3s cubic-bezier(.4,0,.2,1), box-shadow 0.3s;
  z-index: 1002;
  height: 100%;
  border: 1px solid #e0e6ed;
}

.sidebar-advanced-filter.desktop {
  position: sticky;
  top: 80px;
  height: calc(100vh - 80px);
}

.sidebar-advanced-filter.mobile {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  min-width: 80vw;
  max-width: 90vw;
  border-radius: 0 22px 22px 0;
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
  transform: translateX(-110%);
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
}

.sidebar-advanced-filter.open {
  transform: translateX(0) !important;
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.18);
}

.sidebar-advanced-filter.open.desktop {
  position: sticky !important;
  top: 0px !important;
  height: 100% !important;
  z-index: 1002;
}

/*.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}*/

.sidebar-header h3 {
  font-size: 1.05rem;
  font-weight: 700;
  color: #006080;
  display: flex;
  align-items: center;
  gap: 6px;
  letter-spacing: 0;
}

.sidebar-close-btn {
  background: none;
  border: none;
  font-size: 1.3rem;
  color: #0080c6;
  cursor: pointer;
  margin-left: 4px;
  line-height: 1;
  padding: 0 2px;
  transition: color 0.2s;
}
.sidebar-close-btn:hover {
  color: #00b6e6;
}

.sidebar-content {
  flex: 1 1 auto;
  overflow-y: auto;
  height: 100%;
  max-height: 100%;
  padding-bottom: 4px;
}

.filter-group {
  margin-bottom: 16px;
  padding-bottom: 0;
  border-bottom: none;
}
.filter-group:not(:last-child) {
  border-bottom: 1px solid #f0f2f5;
  padding-bottom: 10px;
}
.filter-label {
  font-size: 0.98rem;
  font-weight: 700;
  color: #006080;
  margin-bottom: 6px;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}
.filter-icon {
  font-size: 1.1em;
  color: #00b6e6;
  margin-right: 2px;
}
.filter-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  background: none;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;
  box-shadow: none;
}
.filter-pill {
  font-size: 0.97rem;
  font-weight: 500;
  color: #0080c6;
  background: #f2f8fa;
  border: none;
  border-radius: 12px;
  padding: 6px 14px;
  margin: 1px 2px 1px 0;
  transition: background 0.2s, color 0.2s;
  min-width: 0;
}
.filter-pill.selected {
  background: #00b6e6;
  color: #fff;
}
.filter-search {
  width: 100%;
  padding: 7px 10px;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  font-size: 0.97rem;
  margin-bottom: 4px;
  background: #fff;
  font-weight: 400;
}

.filter-link {
  background: none;
  border: none;
  color: #222;
  font-size: 0.97rem;
  font-weight: 500;
  text-align: left;
  padding: 4px 10px;
  margin: 0 0 2px 0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s, color 0.2s;
  width: 100%;
  display: block;
}
.filter-link.selected,
.filter-link:focus,
.filter-link:hover {
  background: #00b6e6;
  color: #fff;
}

.industry-image-thumb {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  max-width: 40px;
  max-height: 40px;
  display: inline-block;
  margin-right: 12px;
  vertical-align: middle;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  background-color: #f3f8fc;
  box-sizing: border-box;
}

.industry-checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.97rem;
  font-weight: 500;
  color: #222;
  margin-bottom: 6px;
  cursor: pointer;
  padding: 4px 0;
  min-height: 48px;
  width: 100%;
}
.industry-checkbox-label input[type="checkbox"],
.industry-image-thumb {
  flex-shrink: 0;
}
.industry-label-text {
  white-space: normal;
  word-break: break-word;
  flex: 1 1 0%;
  min-width: 0;
  display: block;
}
.industry-checkbox-label input[type="checkbox"] {
  accent-color: #00b6e6;
  margin-right: 2px;
}
.industry-checkbox-label:hover {
  background: #e0f4fa;
  color: #00b6e6;
  border-radius: 6px;
}
.industry-checkbox-label input[type="checkbox"]:checked + .industry-icon,
.industry-checkbox-label input[type="checkbox"]:checked ~ span {
  color: #00b6e6;
}

.solution-type-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.solution-type-radio-label {
  display: flex;
  align-items: center;
  gap: 7px;
  font-size: 0.97rem;
  font-weight: 500;
  color: #222;
  margin-bottom: 2px;
  cursor: pointer;
  padding: 2px 0;
}
.solution-type-radio-label input[type="radio"] {
  accent-color: #00b6e6;
  margin-right: 2px;
}
.solution-type-radio-label:hover {
  background: #e0f4fa;
  color: #00b6e6;
  border-radius: 6px;
}
.solution-type-radio-label input[type="radio"]:checked + span {
  color: #00b6e6;
}

.filter-divider {
  border: none;
  border-top: 1px solid #f0f2f5;
  margin: 10px 0 10px 0;
}

.sidebar-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 18px;
  margin-top: 16px;
}
.clear-filters-btn {
  color: #e53935;
  font-weight: 600;
  background: none;
  border: none;
  padding: 0;
  font-size: 0.97rem;
  cursor: pointer;
  transition: color 0.2s;
  text-align: center;
}
.clear-filters-btn:hover {
  text-decoration: underline;
  color: #b71c1c;
}
.result-count {
  font-size: 0.97rem;
  color: #0080c6;
  font-weight: 500;
  text-align: center;
}

/* Compact checkbox style for industries */
.industry-icon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
  object-fit: contain;
  display: inline-block;
}

/* Industry search */
.industry-search-box {
  width: 100%;
  padding: 7px 10px;
  border-radius: 8px;
  border: 1px solid #e0e6ed;
  font-size: 0.97rem;
  margin-bottom: 6px;
  background: #fff;
  font-weight: 400;
}

.industry-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 245px;
  overflow-y: auto;
}

.industry-pill {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f2f8fa;
  color: #0080c6;
  border: none;
  border-radius: 12px;
  padding: 6px 10px;
  font-size: 0.97rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.industry-pill.selected {
  background: #00b6e6;
  color: #fff;
}

@media (max-width: 900px) {
  .sidebar-advanced-filter.desktop {
    display: none !important;
  }
  .sidebar-advanced-filter.mobile {
    display: block;
    min-width: 70vw;
    max-width: 90vw;
    border-radius: 0 14px 14px 0;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
    height: 100%;
    padding: 14px 6px 10px 10px;
  }
}

@media (min-width: 901px) {
  .solutions-main-flex {
    align-items: stretch !important;
  }
  .sidebar-advanced-filter.desktop {
    position: static !important;
    top: auto !important;
    height: auto !important;
    min-height: 0 !important;
    align-self: stretch !important;
    display: flex;
    flex-direction: column;
    flex: 0 0 270px;
    max-width: 270px;
    width: 100%;
  }
  .sidebar-content {
    flex: 1 1 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .sidebar-advanced-filter.mobile {
    display: none !important;
  }
}
@media (min-width: 901px) {
    .sidebar-advanced-filter.desktop {
        position: sticky !important;
        top: 0px !important;
        /* height: calc(100vh - 100px) !important; */
        min-height: 400px;
        z-index: 1002;
        height: 100% !important;
    }
}
/* Grid expands when sidebar is hidden */
.grid-full {
  width: 100% !important;
  max-width: 100vw !important;
  margin-left: 0 !important;
}

/* Prevent horizontal scroll */
body, html, .solutions-section, .solutions-main-flex, .solutions-content {
  overflow-x: hidden !important;
} 

/* FilterSidebar.css */
.filter-sidebar {
  width: 270px;
  min-width: 220px;
  background: var(--primary-white);
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(46,192,203,0.08);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  margin-right: 2rem;
  transition: transform 0.35s cubic-bezier(0.4,0,0.2,1), opacity 0.25s;
  transform: translateX(-120%);
  opacity: 0;
  position: relative;
  z-index: 20;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
}

.filter-sidebar.open {
  transform: translateX(0);
  opacity: 1;
}

.filter-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 2rem;
  color: var(--primary-teal);
  cursor: pointer;
  z-index: 30;
  transition: color 0.2s;
}
.filter-close-btn:hover {
  color: var(--primary-teal-dark);
}

.filter-search {
  margin-bottom: 1.5rem;
}
.filter-search input {
  width: 100%;
  padding: 0.7rem 1rem;
  border-radius: 8px;
  border: 1px solid var(--secondary-gray-light);
  font-size: 1rem;
}
.filter-section {
  margin-bottom: 1.5rem;
}
.filter-title {
  font-size: 1.05rem;
  font-weight: 700;
  color: var(--primary-teal);
  margin-bottom: 0.7rem;
}
.filter-tags, .filter-industries {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.filter-tag, .filter-industry {
  font-size: 0.97rem;
  color: var(--tertiary-navy);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}
.filter-tag input, .filter-industry input {
  accent-color: var(--primary-teal);
}
@media (max-width: 900px) {
  .filter-sidebar {
    width: 90vw;
    min-width: 0;
    margin-right: 0.5rem;
    padding: 1.2rem 0.7rem 1.2rem 0.7rem;
  }
}

.advanced-filter {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 2rem;
  background: var(--primary-white);
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(46,192,203,0.08);
  padding: 1.5rem 2.5vw;
  margin-bottom: 1.2rem;
  margin-top: 0.5rem;
  position: relative;
  z-index: 10;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  flex-wrap: wrap;
}

.filter-label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-teal);
  margin-right: 0.5rem;
}

.filter-pill {
  background: var(--tertiary-gray-light);
  color: var(--tertiary-navy);
  border-radius: 50px;
  padding: 0.32rem 1.1rem;
  font-size: 0.98rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  cursor: pointer;
  border: 1.5px solid transparent;
  transition: background 0.2s, border 0.2s;
}
.filter-pill input[type="checkbox"] {
  accent-color: var(--primary-teal);
  margin-right: 0.3rem;
}
.filter-pill:hover, .filter-pill input[type="checkbox"]:checked + span {
  background: var(--primary-teal);
  color: var(--primary-white);
  border: 1.5px solid var(--primary-teal);
}

.filter-search input[type="text"] {
  padding: 0.7rem 1.2rem;
  border-radius: 50px;
  border: 1.5px solid var(--secondary-gray-light);
  font-size: 1.05rem;
  min-width: 220px;
  background: var(--primary-white);
  color: var(--tertiary-navy);
  outline: none;
  transition: border 0.2s;
}
.filter-search input[type="text"]:focus {
  border: 1.5px solid var(--primary-teal);
}

@media (max-width: 900px) {
  .advanced-filter {
    flex-direction: column;
    align-items: stretch;
    gap: 1.2rem;
    padding: 1.2rem 1vw;
  }
  .filter-group {
    gap: 0.4rem;
  }
  .filter-search input[type="text"] {
    min-width: 0;
    width: 100%;
  }
}

.advanced-filter-ui {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 1280px;
  margin: 0 auto;
}

.filter-group-row {
  display: flex;
  gap: 0.7rem;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 100%;
}

.filter-group-col {
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
  min-width: 180px;
  flex: 1 1 0;
  max-width: 320px;
}

.filter-search-col {
  min-width: 220px;
  max-width: 260px;
}

.filter-group-label {
  font-size: 1.05rem;
  font-weight: 700;
  color: var(--primary-teal);
  margin-bottom: 0.2rem;
  letter-spacing: 0.2px;
}

.filter-pills-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
}

.filter-search-input {
  padding: 0.7rem 1.2rem;
  border-radius: 50px;
  border: 1.5px solid var(--secondary-gray-light);
  font-size: 1.05rem;
  background: var(--primary-white);
  color: var(--tertiary-navy);
  outline: none;
  transition: border 0.2s;
  width: 100%;
}
.filter-search-input:focus {
  border: 1.5px solid var(--primary-teal);
}

@media (max-width: 900px) {
  .filter-group-row {
    flex-direction: column;
    gap: 1.2rem;
  }
  .filter-group-col {
    max-width: 100%;
    min-width: 0;
  }
  .filter-pills-wrap {
    gap: 0.4rem;
  }
}

@media (min-width: 1400px) {
  .advanced-filter-ui {
    max-width: 1440px;
    gap: 0.3rem;
  }
  .filter-group-row {
    gap: 0.5rem;
  }
}

.sidebar-footer {
  margin-top: auto;
} 

/* Card.css - AdminComponents/shared */
.modern-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.07), 0 1.5px 4px 0 rgba(0,0,0,0.03);
  padding: 1.5rem 1.5rem 1.25rem 1.5rem;
  margin: 0.5rem 0;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.18s;
  position: relative;
  min-width: 0;
}

.modern-card:hover {
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.13), 0 2px 8px 0 rgba(0,0,0,0.06);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.card-icon {
  font-size: 2rem;
  margin-right: 1rem;
  color: #0e7490;
  flex-shrink: 0;
}

.card-header-text {
  flex: 1 1 auto;
  min-width: 0;
}

.card-title {
  font-size: 1.15rem;
  font-weight: 700;
  color: #0f172a;
  letter-spacing: -0.5px;
  white-space: normal;
}

.card-subtitle {
  font-size: 0.98rem;
  color: #64748b;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-status {
  margin-left: 1rem;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0.25em 0.8em;
  border-radius: 1em;
  background: #f1f5f9;
  color: #0891b2;
  white-space: nowrap;
}

.card-content {
  flex: 1 1 auto;
  font-size: 1.01rem;
  color: #334155;
  margin-bottom: 0.5rem;
  min-height: 32px;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  justify-content: flex-end;
}

@media (max-width: 600px) {
  .modern-card {
    padding: 1rem 0.7rem 1rem 0.7rem;
  }
  .card-title {
    font-size: 1rem;
  }
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  .card-status {
    margin-left: 0;
    margin-top: 0.5rem;
  }
} 

/* FloatingQuickAction.css - AdminComponents/shared*/
.floating-quick-action {
  position: fixed;
  right: 32px;
  bottom: 32px;
  z-index: 2000;
  background: var(--accent-primary);
  color: #fff;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
}
.floating-quick-action:hover {
  background: var(--accent-primary-hover);
  box-shadow: 0 8px 24px rgba(0,0,0,0.16);
}
.quick-action-icon {
  font-size: 32px;
}
.quick-action-label {
  display: none;
} 

/* Modal.css - AdminComponents/shared*/
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(17, 24, 39, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 24px;
  border-radius: 12px;
  width: min(900px, 98vw);
  max-width: 98vw;
  margin: auto;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.modal form {
  max-height: 90vh;
  overflow-y: auto;
}

.modal h3 {
  margin-top: 0;
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
}

.modal-field {
  margin-bottom: 16px;
}

.modal-field label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.modal-field input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box; /* Important for padding and width to work together */
}

.modal-field input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(45, 212, 191, 0.2);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
  width: 100%;
  padding-right: 4px;
  box-sizing: border-box;
}

.btn-primary {
    background-color: #18bdce;
    color: #fff;
    border: none;
    font-weight: 700;
    border-radius: 12px;
    padding: 10px 32px;
    font-size: 16px;
    box-shadow: none;
    margin-left: 8px;
    transition: background 0.2s;
}

.btn-primary:hover {
    background-color: #13a3b8;
}

.btn-secondary {
    background-color: #fff;
    color: #374151;
    border: 1.5px solid #b0b7c3;
    font-weight: 700;
    border-radius: 12px;
    padding: 10px 28px;
    font-size: 16px;
    box-shadow: none;
    transition: border-color 0.2s, color 0.2s;
}

.btn-secondary:hover {
    border-color: #10bcd4;
    color: #10bcd4;
}

.btn-reject {
    background-color: #e11d48;
    color: #fff;
    border: none;
    font-weight: 700;
    border-radius: 12px;
    padding: 10px 32px;
    font-size: 16px;
    box-shadow: none;
    margin-left: 8px;
    transition: background 0.2s;
}

.btn-reject:hover {
    background-color: #be123c;
}

.modal-actions .btn-renew {
    margin-right: auto;
    background-color: #10b981;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
}

.modal-section-header {
  font-size: 16px;
  font-weight: 700;
  margin: 18px 0 8px 0;
  color: #18bdce;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 4px;
}

.modal-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 24px;
  margin-bottom: 8px;
}

@media (max-width: 700px) {
  .modal {
    max-width: 98vw;
    padding: 18px;
  }
  .modal-grid {
    grid-template-columns: 1fr;
    gap: 14px 0;
  }
}

.modal-label {
  display: flex;
  align-items: center;
  gap: 7px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}
.modal-label-icon {
  font-size: 16px;
  color: #18bdce;
}
.modal-required {
  color: #e11d48;
  margin-left: 2px;
  font-size: 15px;
}
.modal-tooltip {
  display: inline-block;
  background: #e0f7fa;
  color: #18bdce;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  text-align: center;
  font-size: 13px;
  font-weight: 700;
  line-height: 18px;
  margin-left: 5px;
  cursor: pointer;
  border: 1px solid #b2ebf2;
}
.modal-field-full {
  grid-column: 1 / -1;
}

.modal-radio-group-row {
  display: flex;
  align-items: center;
  gap: 24px;
  gap: 8px;
  margin-bottom: 0;
  flex-wrap: wrap;
}
.modal-radio-inline {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 15px;
  font-weight: 500;
  margin-right: 18px;
  cursor: pointer;
}
.modal-radio-label {
  font-weight: 600;
  min-width: 210px;
  color: #374151;
  font-size: 15px;
}
.modal-flex-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0;
  flex-wrap: wrap;
}
.travel-section-bg {
  background: #f8fafc;
  border: 1.5px solid #d1d5db;
  border-radius: 12px;
  padding: 6px 10px 6px 10px;
  margin: 0;
}
@media (max-width: 700px) {
  .modal-radio-group-row, .modal-flex-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  .modal-radio-label {
    min-width: 0;
  }
} 

/* Toast.css - AdminComponents/shared*/
.toast {
  position: fixed;
  bottom: 32px;
  right: 32px;
  background: var(--teal-30);
  color: #fff;
  padding: 16px 32px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(30, 182, 192, 0.15);
  z-index: 2000;
  font-size: 1.1em;
} 
/* 01-07-2025 inline-V */
.assistant-fab-icon {
  width: 36px;
  height: 36px;
}
.auth-modal-absolute-content {
  position: absolute;
  width: 100%;
}
.fab-menu .fab-actions .fab-action {
  transition: opacity 0.3s, transform 0.3s;
  transition-delay: var(--fab-delay, 0ms);
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.fab-menu.open .fab-actions .fab-action {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}
.auth-modal-switch-link-mt {
  margin-top: 1.2rem;
}
.password-strength-bar {
  width: var(--password-strength-width, 0%);
  transition: width 0.3s;
}
.auth-link-text-spaced {
  margin-left: 1.1rem;
  margin-right: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  color: var(--primary-color, #0056d6);
}

.header-cart-icon-wrap-pointer {
  cursor: pointer;
}

.sidebar-cart-link {
  font-weight: 500;
  color: #005e6a;
  cursor: pointer;
}
.logo-stacked {
  display: flex;
  flex-direction: column;
}

.logo-align-left {
  align-items: flex-start;
  text-align: left;
}

.logo-align-center {
  align-items: center;
  text-align: center;
}

.logo-color-default .logo-main {
  color: var(--primary-color, #00b6b6);
}
.logo-color-default .logo-sub {
  color: #1a237e;
}

.logo-color-white .logo-main,
.logo-color-white .logo-sub {
  color: #fff;
}

.logo-main {
  font-weight: 700;
  font-size: 1.6rem;
  letter-spacing: 0.01em;
}

.logo-sub {
  font-weight: 600;
  font-size: 1.1rem;
  letter-spacing: 0.08em;
  margin-top: -0.2em;
}
.cart-industry-icon {
  background-image: var(--industry-icon-url);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.cart-count-item-pointer {
  cursor: pointer;
}

.cart-add-new-icon {
  margin-right: 6px;
}

.cart-empty-icon-svg {
  font-size: 4rem;
  opacity: 0.5;
}

.cart-subscription-icon-wrap {
  display: flex;
  align-items: center;
  gap: 6px;
}
.hero-btn {
  border-radius: 50px !important;
  font-weight: 700 !important;
}

.hero-btn-margin {
  margin-right: 16px !important; /* MUI default spacing unit is 8px, so mr:2 = 16px */
}
.section-header-no-margin {
  margin-bottom: 0;
}

.section-subtitle-no-margin {
  margin: 0;
}

.industries-toggle-icon {
  margin-right: 6px;
}

.industry-card-body {
  background-image: var(--industry-icon-url);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 140px;
  border-radius: 0 0 16px 16px;
  /* background-color: var(--secondary-teal-extra-light);  */
  flex: 1 1 auto;
}
.module-btn {
  border-radius: 50px !important;
  font-weight: 700 !important;
}
.plan-icon {
  font-size: 1.3em;
  margin-right: 8px;
  vertical-align: middle;
}
.plan-icon-leaf { color: #23A3AD; }
.plan-icon-medal-silver { color: #b0b0b0; }
.plan-icon-medal-gold { color: #FFD700; }
.plan-icon-gem { color: #0F5FDC; }

.plan-quickview-icon {
  font-size: 1.1em;
  vertical-align: middle;
  margin-right: 6px;
}
.plan-quickview-icon-check { color: var(--primary-teal); }
.plan-quickview-icon-cancel { color: #e57373; }

.feature-comparison-title {
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2.2rem;
}

.feature-category-icon {
  margin-right: 8px;
  color: #181818;
}
.feature-category-icon.open {
  color: #fff;
}

.feature-category-body {
  display: none;
}
.feature-category-body.open {
  display: block;
}

.feature-comparison-center {
  text-align: center;
}

.feature-comparison-icon {
  font-size: 1.3rem;
  vertical-align: middle;
}
.feature-comparison-icon-check { color: var(--primary-teal); }
.feature-comparison-icon-cancel { color: #e57373; }
.pricing-card-btn {
  border-radius: 16px !important; /* MUI borderRadius: 2 = 16px (theme spacing unit) */
  font-weight: 700 !important;
}
/* Sidebar positioning and transitions */
.sidebar-advanced-filter.mobile {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1002;
  height: 100%;
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
  box-shadow: none;
  transform: translateX(-110%);
}
.sidebar-advanced-filter.mobile.open {
  transform: translateX(0);
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.08);
}
.sidebar-advanced-filter.desktop {
  align-self: stretch;
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
  transform: translateX(0);
  box-shadow: 0 0 32px 0 rgba(0,0,0,0.08);
}

/* Header icon */
.sidebar-header-icon {
  margin-right: 8px;
}

/* Industry image thumb */
.industry-image-thumb {
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 10px;
  vertical-align: middle;
  background-image: var(--industry-thumb-url);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 8px;
  background-color: #f3f8fc;
}

/* Clear filters button */
.clear-filters-btn {
  color: #e53935;
  font-weight: 600;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}
.section-header-margin-top {
  margin-top: 2.5rem;
}

.sidebar-toggle-icon {
  margin-right: 6px;
  font-size: 1.2em;
  vertical-align: middle;
}

.suite-card-gap {
  width: 16px;
  display: inline-block;
}

.solutions-btn {
  border-radius: 50px !important;
  font-weight: 700 !important;
}

.solutions-btn-margin {
  margin-right: 8px !important; /* for mr: 1 */
  margin-top: 16px !important;  /* for mt: 2, adjust as needed */
}

/* 01-07-2025 Inline -A */
.admin-panel-settings {
  padding: 32px 0;
  text-align: center;
}
.grid-view-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.billing-amount {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 8px;
}
.section-header-actions-relative {
  position: relative;
}

.columns-btn-margin {
  margin-right: 8px;
}

.grid-view-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.contract-card-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1.2rem 2.5rem;
  margin-bottom: 8px;
}

.table-empty-row {
  text-align: center;
  padding: 32px;
}

.status-expired {
  color: #e11d48;
  font-weight: 600;
}
.status-expiring {
  color: #f59e42;
  font-weight: 600;
}
.status-active {
  color: #059669;
  font-weight: 600;
}

.status-pill {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 2px 14px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 15px;
  border: 1.5px solid;
}
.status-expired {
  background: #f3f4f6;
  border-color: #e5e7eb;
  color: #e11d48;
}
.status-expiring {
  background: #fff7ed;
  border-color: #fdba74;
  color: #ea580c;
}
.status-active {
  background: #e6faf4;
  border-color: #a7f3d0;
  color: #059669;
}
.status-pill-icon {
  font-size: 18px;
}
.status-expired-icon { color: #e11d48; }
.status-expiring-icon { color: #ea580c; }
.status-active-icon { color: #059669; }

.modal-backdrop-z {
  z-index: 1000;
}
.modal-custom {
  min-width: 340px;
  max-width: 400px;
  margin: auto;
  position: relative;
}
.modal-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}
.modal-columns-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 18px;
}
.modal-columns-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 15px;
}
.modal-close-btn {
  background: none;
  border: none;
  font-size: 22px;
  cursor: pointer;
}
.modal-footer-flex {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.contract-detail-message {
  padding: 32px;
}

.contract-detail-container {
  max-width: 900px;
  margin: 32px auto;
}

.contract-detail-back-btn {
  margin-bottom: 24px;
  background: none;
  border: none;
  color: #0891b2;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.contract-detail-header-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.contract-detail-header-icon {
  font-size: 32px;
}

.contract-detail-header-title {
  font-size: 32px;
  margin: 0;
}

.contract-detail-status {
  background: #10b981;
  color: #fff;
  border-radius: 12px;
  padding: 2px 12px;
  font-weight: 600;
  font-size: 14px;
}

.contract-detail-info-row {
  display: flex;
  gap: 32px;
  margin-bottom: 32px;
}

.contract-detail-info-col {
  flex: 1;
}

.contract-detail-info-item {
  margin-bottom: 12px;
}

.contract-detail-info-icon {
  margin-right: 6px;
}

.contract-detail-edit-btn {
  margin-top: 12px;
}

.contract-detail-edit-icon {
  margin-right: 6px;
}
.customer-detail-container { max-width: 1100px; margin: 32px auto; }
.customer-detail-header-row { display: flex; align-items: flex-start; justify-content: space-between; gap: 32px; margin-bottom: 32px; }
.customer-detail-name { font-size: 36px; margin-bottom: 8px; }
.customer-detail-pills-row { display: flex; gap: 8px; flex-wrap: wrap; margin-bottom: 12px; }
.customer-detail-pill { border-radius: 12px; padding: 2px 12px; font-weight: 600; font-size: 14px; color: #fff; }
.customer-detail-pill-status { background: #10b981; }
.customer-detail-pill-type { background: #0891b2; }
.customer-detail-pill-industry { background: #f59e42; }
.customer-detail-pill-tag { background: #e0f2fe; color: #0369a1; }
.customer-detail-pill-icon { margin-right: 4px; }
.customer-detail-contact-row { display: flex; align-items: center; gap: 18px; margin-bottom: 8px; }
.customer-detail-contact-icon { margin-right: 4px; }
.customer-detail-header-actions { display: flex; gap: 12px; }
.customer-detail-metrics-row { display: flex; gap: 24px; margin-bottom: 32px; }
.customer-detail-metric-card { background: #f0fdfa; border-radius: 12px; padding: 24px 32px; flex: 1; min-width: 220px; }
.customer-detail-metric-label { font-size: 14px; margin-bottom: 6px; }
.customer-detail-metric-label-blue { color: #0891b2; }
.customer-detail-metric-label-green { color: #10b981; }
.customer-detail-metric-label-purple { color: #6366f1; }
.customer-detail-metric-value { font-size: 28px; font-weight: 700; }
.customer-detail-validity-section { margin-bottom: 32px; background: #fff; border-radius: 16px; padding: 32px 0 24px 0; box-shadow: 0 2px 8px #0001; }
.customer-detail-validity-title { text-align: center; color: #223488; margin-bottom: 24px; }
.customer-detail-validity-row { display: flex; justify-content: center; align-items: center; gap: 60px; }
.customer-detail-validity-col { text-align: center; }
.customer-detail-validity-label { color: #64748b; margin-bottom: 4px; }
.customer-detail-validity-value { font-size: 22px; font-weight: 600; }
.customer-detail-renewal-col { text-align: center; margin-left: 60px; }
.customer-detail-renewal-title { font-weight: 600; font-size: 20px; margin-bottom: 4px; }
.customer-detail-renewal-value { font-size: 28px; font-weight: 700; color: #f59e42; margin-bottom: 4px; }
.customer-detail-renewal-days { color: #10b981; font-weight: 600; margin-bottom: 8px; }
.customer-detail-renewal-notice { background: #f0f6ff; color: #223488; border-radius: 8px; padding: 8px 18px; font-size: 15px; display: inline-block; }
.customer-detail-associated-contracts { margin-bottom: 32px; }
.customer-detail-associated-title { margin: 0 0 16px 0; }
.customer-detail-contract-row { cursor: pointer; }
.customer-detail-contract-id { color: #0891b2; font-weight: 600; }
.customer-detail-contract-id-icon { margin-right: 4px; }
.customer-detail-empty-contracts { margin-bottom: 32px; text-align: center; padding: 48px 0; }
.customer-detail-empty-title { margin: 0 0 16px 0; }
.customer-detail-empty-message { font-size: 18px; color: #64748b; margin-bottom: 24px; }
.customer-detail-add-first-btn { font-size: 18px; padding: 12px 36px; }
.customer-detail-add-first-icon { margin-right: 8px; }
.customer-detail-notes-input-row { margin-bottom: 18px; }
.customer-detail-notes-input { padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 8px; width: 60%; margin-right: 8px; }
.customer-detail-notes-timeline { border-left: 3px solid #e0e7ef; padding-left: 18px; }
.customer-detail-note { margin-bottom: 18px; }
.customer-detail-note-date { font-size: 13px; color: #64748b; margin-bottom: 2px; }
.customer-detail-note-text { font-size: 16px; }
.customer-detail-empty-notes { color: #888; padding: 16px 0; }
.customers-container { max-width: 100%; padding: 0; }
.customers-type-cards-row { display: flex; gap: 16px; margin-bottom: 18px; margin-top: 0; flex-wrap: wrap; align-items: center; }
.customers-type-card { flex: 0 1 220px; min-width: 100px; max-width: 240px; background: #f0fdfa; border-radius: 10px; padding: 14px 18px; display: flex; align-items: center; gap: 12px; box-shadow: 0 1px 4px #0001; }
.customers-type-card-ee .customers-type-card-icon { color: #0891b2; }
.customers-type-card-en .customers-type-card-icon { color: #6366f1; }
.customers-type-card-nn .customers-type-card-icon { color: #10b981; }
.customers-type-card-icon { font-size: 28px; }
.customers-type-card-count { font-weight: 700; font-size: 18px; }
.customers-type-card-label { font-size: 14px; }
.customers-type-card-label-ee { color: #0891b2; }
.customers-type-card-label-en { color: #6366f1; }
.customers-type-card-label-nn { color: #10b981; }
.customers-header-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
.customers-header-title { font-size: 2rem; font-weight: 700; margin: 0; letter-spacing: -1px; }
.customers-header-icon { vertical-align: -3px; margin-right: 8px; }
.customers-add-btn { padding: 10px 32px; font-size: 16px; display: flex; align-items: center; gap: 8px; }
.customers-content-card { padding: 0; border-radius: 10px; }
.customers-view-toggle-row { display: flex; justify-content: flex-end; align-items: center; gap: 8px; padding: 16px 24px 0 24px; }
.customers-loading-row { padding: 32px 0; text-align: center; }
.customers-status-pill { border-radius: 12px; padding: 2px 12px; font-weight: 600; font-size: 14px; display: inline-block; min-width: 70px; text-align: center; }
.customers-status-pill.active { background: #10b981; color: #fff; }
.customers-status-pill.inactive { background: #e5e7eb; color: #e11d48; }
.customers-name-link { color: #0891b2; font-weight: 600; }
.customers-contact-icon { margin-right: 4px; }
.customers-action-btn { background: none; border: none; cursor: pointer; font-size: 18px; margin-right: 8px; display: inline-flex; align-items: center; }
.customers-grid-view-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(270px, 1fr)); gap: 1.5rem; margin-top: 1.5rem; padding: 0 24px 24px 24px; }
.customers-grid-info-row { margin-bottom: 8px; }
.customers-grid-contact-info { font-size: 13px; }
/* Home.css */

/* Layouts */

.dashboard-spacer { height: 16px; }
.dashboard-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}
.dashboard-card-link { cursor: pointer; }
.dashboard-charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* ProgressBar */
.progress-bar-container {
  width: 100%;
  background-color: #f1f5f9;
  border-radius: 10px;
  overflow: hidden;
  height: 8px;
}
.progress-bar-fill {
  height: 8px;
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* MiniChart */
.mini-chart {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  height: 60px;
  margin-top: 16px;
}
.mini-chart-bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.mini-chart-bar {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: height 0.3s ease;
}
.mini-chart-label {
  font-size: 0.7rem;
  color: #64748b;
  margin-top: 4px;
}

/* Metric values */
.dashboard-metric-value {
  font-size: 2rem;
  font-weight: bold;
}
.dashboard-customers-value { color: #0e7490; }
.dashboard-contracts-value { color: #10b981; }
.dashboard-revenue-value { color: #059669; }
.dashboard-licenses-value { color: #7c3aed; }
.dashboard-travel-value { color: #f59e0b; }
.dashboard-purchase-value { color: #dc2626; }
.dashboard-professional-value { color: #0891b2; }
.dashboard-support-value { color: #ea580c; }

.dashboard-revenue-change {
  font-size: 0.9rem;
  color: #10b981;
  margin-top: 4px;
}
.dashboard-support-response {
  font-size: 0.9rem;
  color: #ea580c;
  margin-top: 4px;
}

/* Status Row/Items */
.dashboard-status-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.dashboard-status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.dashboard-status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
.dashboard-status-dot-active { background-color: #10b981; }
.dashboard-status-dot-inactive { background-color: #ef4444; }
.dashboard-status-dot-expiring { background-color: #f59e0b; }
.dashboard-status-dot-expired { background-color: #ef4444; }
.dashboard-status-label {
  font-size: 0.9rem;
}

/* Revenue Row */
.dashboard-revenue-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.dashboard-revenue-total {
  font-size: 1.2rem;
  font-weight: bold;
  color: #059669;
}
.dashboard-revenue-yoy {
  font-size: 0.9rem;
  color: #10b981;
}

/* System Overview */
.dashboard-system-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}
.dashboard-system-item {
  text-align: center;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 8px;
}
.dashboard-system-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #0e7490;
}
.dashboard-system-label {
  font-size: 0.9rem;
  color: #64748b;
}

/* Quick Actions */
.dashboard-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}
.dashboard-action-btn {
  padding: 12px 16px;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background 0.2s;
}
.dashboard-action-btn-customer { background-color: #0e7490; }
.dashboard-action-btn-contract { background-color: #10b981; }
.dashboard-action-btn-travel { background-color: #f59e0b; }
.dashboard-action-btn-report { background-color: #7c3aed; }
.dashboard-action-btn:hover { opacity: 0.9; }
/* License.css */

.license-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.grid-view-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}

.license-card-expiry {
  margin-bottom: 8px;
}

.no-licenses-found {
  text-align: center;
  padding: 32px;
}
.onboarding-center-content {
  padding: 32px 0;
  text-align: center;
}
.professional-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.professional-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}

.professional-card-date {
  margin-bottom: 8px;
}

/* Ownership Section */
.ownership-section {
  margin-top: 32px;
}
.ownership-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 16px;
}
.ownership-tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}
.ownership-tab-btn {
  padding: 8px 18px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  color: #222;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.ownership-tab-btn.active {
  background: #d1fafd;
}
.ownership-form {
  margin-bottom: 24px;
  background: #f9fafb;
  padding: 16px;
  border-radius: 8px;
}
.ownership-form-fields {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  align-items: center;
}
.ownership-form-select,
.ownership-form-input {
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}
.ownership-form-btn {
  background: #d1fafd;
  color: #222;
  border: none;
  border-radius: 6px;
  padding: 8px 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.ownership-form-btn.cancel {
  background: #f3f4f6;
}
.ownership-add-btn {
  margin-bottom: 24px;
  background: #1db6c0;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 28px;
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.2s;
}
.ownership-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
}
.ownership-card-no-owner {
  color: #64748b;
}
.ownership-timeline {
  overflow-x: auto;
  padding: 16px 0;
}
.ownership-timeline-row {
  display: flex;
  align-items: center;
  gap: 32px;
  min-width: 900px;
}
.ownership-timeline-card {
  position: relative;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 18px 28px;
  min-width: 200px;
}
.ownership-timeline-date {
  font-size: 0.98rem;
  color: #64748b;
}
.ownership-timeline-connector {
  position: absolute;
  right: -16px;
  top: 50%;
  width: 32px;
  height: 2px;
  background: #e5e7eb;
  z-index: 0;
}
.ownership-accordion {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.ownership-accordion-item {
  border-bottom: 1px solid #e5e7eb;
  background: #fff;
}
.ownership-accordion-item.open {
  background: #f3f4f6;
}
.ownership-accordion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 22px;
  cursor: pointer;
}
.ownership-accordion-title {
  font-weight: 600;
}
.ownership-accordion-content {
  box-shadow: none;
  background: none;
  margin: 0;
  padding: 0;
}
.purchase-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.purchase-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}

.purchase-card-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1.2rem 2.5rem;
  margin-bottom: 8px;
}

/* Modal Styles */
.purchase-modal {
  max-width: 520px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  background: #fff;
  border-radius: 12px;
  margin: 40px auto;
  position: relative;
}
.purchase-modal-form {
  padding: 32px 32px 24px 32px;
}
.purchase-modal-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #059669;
}
.purchase-modal-title.reject {
  color: #e11d48;
}
.purchase-modal-title.approve {
  color: #059669;
}
.purchase-modal-confirm-text {
  margin: 24px 0 12px 0;
  font-weight: 500;
  font-size: 16px;
}
.purchase-modal-label {
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 4px;
  display: block;
  color: #222;
}
.purchase-modal-label.reject {
  color: #e11d48;
}
.purchase-modal-textarea {
  width: 100%;
  min-height: 70px;
  border-radius: 8px;
  border: 1px solid #D1D5DB;
  padding: 12px;
  font-size: 15px;
  margin-top: 4px;
  margin-bottom: 4px;
  background: #fff;
  resize: vertical;
}
.purchase-modal-textarea.reject {
  background: #fff6f7;
}
.purchase-modal-textarea.error {
  border: 1.5px solid #e11d48;
}
.purchase-modal-error {
  color: #e11d48;
  font-size: 13px;
  margin-top: 4px;
}
.renewals-kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}
.renewals-kpi-title {
  white-space: normal;
  word-break: break-word;
}
.renewals-kpi-value {
  font-size: 1.5rem;
  font-weight: 700;
}
.renewals-kpi-value-secondary {
  font-size: 1rem;
  font-weight: 400;
}
.renewals-kpi-filtered {
  margin-top: 8px;
  font-size: 0.95rem;
  color: #0ea5e9;
  font-weight: 500;
}
.renewals-showall-btn-wrapper {
  margin-bottom: 1rem;
}
.renewals-showall-btn {
  background: #f1f5f9;
  color: #0ea5e9;
  border: none;
  border-radius: 6px;
  padding: 6px 18px;
  font-weight: 600;
  cursor: pointer;
}
.renewals-table-urgency {
  font-weight: 600;
}
.renewals-status-select {
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #ddd;
}
.renewals-table-empty {
  text-align: center;
  padding: 32px;
}
.server-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.server-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}

.server-card-status {
  margin-bottom: 8px;
}

.server-table-empty {
  text-align: center;
  padding: 32px;
}
.error-boundary {
  padding: 20px;
  text-align: center;
  color: #666;
}

.error-boundary-btn {
  padding: 10px 20px;
  background-color: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  margin-top: 16px;
  transition: background 0.2s;
}

.error-boundary-btn:hover {
  background-color: #005bb5;
}
.modal-select-field {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.modal-label-select {
  margin-bottom: 8px;
}

.modal-select-options {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24px;
}

.modal-select-option {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  margin-right: 18px;
}

.modal-select-radio {
  margin-right: 6px;
  margin-bottom: 2px;
}
.sidebar-hamburger {
  position: fixed;
  top: 18px;
  left: 18px;
  z-index: 1200;
  background: #fff;
  border: 1.5px solid #e5e7eb;
  border-radius: 8px;
  padding: 8px;
  font-size: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 4px #0001;
  cursor: pointer;
}

.sidebar[data-mobile-overlay] {
  z-index: 1100;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  box-shadow: 0 0 0 100vw #0003;
}
.expandable-searchbar {
  position: relative;
  display: inline-block;
  margin-right: 0;
  vertical-align: middle;
  height: 40px;
}

.expandable-searchbar-icon-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
}

.expandable-searchbar-input {
  width: 0;
  opacity: 0;
  padding: 8px 0;
  border: none;
  border-radius: 24px;
  background: #fff;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  transition: width 0.22s cubic-bezier(.4,0,.2,1), opacity 0.18s;
  z-index: 2;
  font-size: 16px;
  color: #222;
  box-shadow: none;
  outline: none;
  pointer-events: none;
  height: 40px;
}
.expandable-searchbar-input.expanded {
  width: 220px;
  opacity: 1;
  padding: 8px 8px 8px 36px;
  border: 2px solid #222;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  pointer-events: auto;
}

.expandable-searchbar-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.top-bar-search-rel {
  position: relative;
}

.search-results-dropdown {
  min-width: 220px;
  max-width: 320px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: absolute;
  left: 0;
  top: 40px;
  z-index: 10;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  padding: 6px 0;
  margin: 0;
  list-style: none;
}

.search-results-item {
  padding: 8px 18px;
  cursor: pointer;
  font-size: 15px;
  color: #222;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background 0.15s;
}
.search-results-item:hover {
  background: #f1f5f9;
}

.icon-button-logout {
  margin-left: 6px;
}
.sla-kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}
.sla-kpi-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sla-kpi-card-value {
  font-size: 2rem;
  font-weight: 700;
}
.sla-kpi-card-target {
  color: #64748b;
  font-size: 1rem;
  margin-bottom: 4px;
}
.sla-kpi-card-progress-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 4px;
  margin: 8px 0;
  width: 100%;
  overflow: hidden;
}
.sla-kpi-card-progress {
  height: 6px;
  border-radius: 4px;
  transition: width 0.3s;
}
.sla-kpi-card-percent {
  font-size: 0.95rem;
  color: #64748b;
}

.sla-severity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}
.sla-severity-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sla-severity-card-desc {
  color: #64748b;
  margin-bottom: 8px;
}
.sla-severity-card-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}
.sla-severity-card-open {
  font-weight: 600;
}
.sla-severity-card-resolved {
  color: #22c55e;
  font-weight: 600;
}
.sla-severity-card-target {
  font-size: 0.98rem;
  color: #64748b;
}

.sla-monthly-consumption {
  margin-bottom: 2rem;
  max-width: 600px;
}
.sla-monthly-title {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sla-monthly-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 1rem;
}
.sla-monthly-tr-header {
  background: #f3f4f6;
}
.sla-monthly-th,
.sla-monthly-td {
  padding: 8px;
}
.sla-monthly-th-month,
.sla-monthly-td {
  text-align: left;
}
.sla-monthly-th-consumed,
.sla-monthly-th-remaining,
.sla-monthly-th-used,
.sla-monthly-td-right {
  text-align: right;
}

.sla-table-header {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sla-table-title {
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: 8px;
}
.sla-table-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
.sla-export-btn {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 6px 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}
.sla-columns-btn {
  background: #18bdce;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 6px 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.sla-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 8px 8px 8px;
}
.sla-card-section-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}
.sla-card-section-priority {
  background: #64748b;
  color: #fff;
  border-radius: 8px;
  padding: 2px 10px;
  font-weight: 600;
  font-size: 0.95rem;
  margin-left: 8px;
}
.sla-card-section-status {
  font-weight: 600;
}
.sla-card-section-slatime {
  font-weight: 600;
}

.sla-col-modal {
  z-index: 1000;
}
.sla-col-modal-content {
  min-width: 340px;
  max-width: 400px;
  margin: auto;
  position: relative;
}
.sla-col-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.sla-col-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}
.sla-col-modal-close {
  background: none;
  border: none;
  font-size: 22px;
  cursor: pointer;
}
.sla-col-modal-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 18px;
}
.sla-col-modal-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 15px;
}
.sla-col-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
.support-kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}
.support-kpi-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
}
.support-kpi-card-value {
  font-size: 2.2rem;
  font-weight: 700;
}
.support-kpi-card-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin-top: 2px;
}

.support-category-section {
  margin: 1.5rem 0;
}
.support-category-title {
  font-size: 1.15rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}
.support-category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}
.support-category-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
}
.support-category-card-desc {
  color: #222;
  font-weight: 500;
  font-size: 1.08rem;
  margin-bottom: 4px;
}
.support-category-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #14b8a6;
  margin-top: 18px;
}

.support-config-section {
  margin: 2.5rem 0 1.5rem 0;
}
.support-config-title {
  font-size: 1.35rem;
  font-weight: 700;
  margin-bottom: 24px;
}

.support-config-cards {
  display: flex;
  gap: 2.5rem;
  flex-wrap: wrap;
  margin-bottom: 32px;
}
.support-config-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 2rem 1.5rem;
  min-width: 260px;
  flex: 1 1 320px;
  max-width: 340px;
}
.support-config-card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.support-config-card-title {
  font-weight: 600;
  font-size: 1.08rem;
}
.support-config-card-desc {
  color: #64748b;
  font-size: 0.98rem;
  margin-bottom: 10px;
}
.support-config-days-list,
.support-config-mode-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.support-config-day-item,
.support-config-mode-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 15px;
}
.support-config-hours-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
}
.support-config-hours-label {
  font-size: 15px;
}
.support-config-hours-input,
.support-config-hours-select {
  margin-left: 8px;
  padding: 4px 10px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}
.support-config-hours-time {
  margin-left: 8px;
  color: #64748b;
  font-size: 14px;
}
.support-config-save-btn-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}
.support-config-save-btn {
  background: #0ea5e9;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-weight: 700;
  font-size: 1.08rem;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(14,165,233,0.08);
}

.support-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.support-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}
.support-card-contact {
  margin-bottom: 8px;
}

.support-table-empty {
  text-align: center;
  padding: 32px;
}
.travel-summary-cards {
  display: flex;
  gap: 24px;
  margin: 24px 0;
}
.travel-summary-card {
  flex: 1;
  background: #f8fafc;
  border-radius: 12px;
  padding: 18px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 4px #0001;
}
.travel-summary-card-icon {
  font-size: 28px;
}
.travel-summary-card-value {
  font-weight: 700;
  font-size: 20px;
}
.travel-summary-card-label {
  font-size: 15px;
  color: #555;
}

.travel-billable-table-title {
  margin: 0 0 16px 0;
}
.travel-billable-table-empty {
  text-align: center;
  padding: 32px;
}

.travel-toolbar-flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 16px 24px 0 24px;
}

.travel-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(270px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
  padding: 0 24px 24px 24px;
}
.travel-card-date {
  margin-bottom: 8px;
}
.travel-table-empty {
  text-align: center;
  padding: 32px;
}

/* Travel Expense Modal */
.travel-expense-modal {
  z-index: 1000;
}
.travel-expense-modal-content {
  min-width: 340px;
  max-width: 420px;
  margin: auto;
  position: relative;
}
.travel-expense-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.travel-expense-modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}
.travel-expense-modal-close {
  background: none;
  border: none;
  font-size: 22px;
  cursor: pointer;
}

.travel-expense-modal-select,
.travel-expense-modal-input {
  width: 100%;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  margin-bottom: 0;
}
.travel-expense-modal-input-readonly {
  background: #f3f4f6;
}
.travel-expense-modal-dates {
  display: flex;
  gap: 12px;
}
.travel-expense-modal-date {
  flex: 1;
}
.travel-expense-modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 18px;
}
.travel-history-container {
  max-width: 900px;
  margin: 32px auto;
}
.travel-history-title {
  margin-bottom: 16px;
}
.travel-history-summary {
  background: #f8fafc;
  border-radius: 10px;
  padding: 18px 24px;
  margin-bottom: 24px;
}
.travel-history-table-title {
  margin: 0 0 16px 0;
}
.travel-history-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  margin-right: 4px;
}
.travel-history-action-btn:last-child {
  margin-right: 0;
}
.travel-history-table-empty,
.travel-history-notfound {
  text-align: center;
  padding: 32px;
}
.user-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.user-metrics-value {
  font-size: 2rem;
  font-weight: bold;
}

.user-metrics-total {
  color: #0ea5e9;
}
.user-metrics-active {
  color: #10b981;
}
.user-metrics-inactive {
  color: #ef4444;
}
.user-metrics-admin {
  color: #6366f1;
}
.user-metrics-read {
  color: #0891b2;
}
.user-metrics-readwrite {
  color: #f59e42;
}