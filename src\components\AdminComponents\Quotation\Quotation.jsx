import React, { useState, useMemo } from 'react';
import {
    Box, Paper, Typography, Button, TextField, IconButton, Checkbox,
    Table, TableBody, TableHead, TableRow, TableSortLabel, TableCell,
    Drawer, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
    ToggleButton, Avatar,
    List, ListItemText, FormControlLabel, FormControl, InputLabel, Select, MenuItem, Chip
} from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import {
    Add, Edit, Delete, Visibility, Search, FilterAlt, ViewModule, GridView, Apps, ViewList,
    BarChart, Schedule, People, CheckCircle, Cancel, ShowChart, PieChart, DonutLarge
} from '@mui/icons-material';
import { theme, AdminComponents } from '../../../styles/theme';
import Chart from 'chart.js/auto';

// --- MOCK DATA ---
const initialQuotations = [
    { id: 1, quotationId: 'QT-2024-001', customer: 'Prevost-DMS', name: 'License Quotation', type: 'LICENSE', value: '$100,000', status: 'Active', startDate: '2024-06-01', endDate: '2025-06-01', owner: '<PERSON>' },
    { id: 2, quotationId: 'QT-2024-002', customer: 'Reman', name: 'Support Quotation', type: 'SUPPORT', value: '$50,000', status: 'Pending', startDate: '2024-07-01', endDate: '2025-07-01', owner: 'IT Department' },
    { id: 3, quotationId: 'QT-2024-003', customer: 'Wirtgen-FSM', name: 'Service Quotation', type: 'SERVICE', value: '$75,000', status: 'Expired', startDate: '2023-05-01', endDate: '2024-05-01', owner: 'Legal Team' },
];

const ALL_COLUMNS = [
    { key: 'quotationId', label: 'Quotation ID', type: 'string' },
    { key: 'customer', label: 'Customer', type: 'string' },
    { key: 'name', label: 'Name', type: 'string' },
    { key: 'type', label: 'Type', type: 'string' },
    { key: 'value', label: 'Value', type: 'string' },
    { key: 'status', label: 'Status', type: 'string' },
    { key: 'startDate', label: 'Start Date', type: 'string' },
    { key: 'endDate', label: 'End Date', type: 'string' },
    { key: 'owner', label: 'Owner', type: 'string' },
];

const FILTER_OPERATORS = ['Equals', 'Not Equals', 'Contains', 'Starts With', 'Ends With'];

const ActionButtons = ({ quotation, onView, onEdit, onDelete }) => (
    <Box onClick={e => e.stopPropagation()}>
        <IconButton size="small" onClick={() => onView(quotation, false)} title="View Details"><Visibility fontSize="small" /></IconButton>
        <IconButton size="small" onClick={() => onEdit(quotation, true)} title="Edit"><Edit fontSize="small" /></IconButton>
        <IconButton size="small" color="error" onClick={() => onDelete([quotation.id])} title="Delete"><Delete fontSize="small" /></IconButton>
    </Box>
);

const QuotationTable = ({ quotations, onRowClick, onHeaderClick, sortColumn, sortDirection, selectedId, selectedIds, onSelectAll, onSelectOne, columnOrder, setColumnOrder, onDelete, onEdit, onView }) => {
    const dragItemIndex = React.useRef(null);
    const dragOverItemIndex = React.useRef(null);

    const handleDrop = () => {
        if (dragItemIndex.current === null || dragOverItemIndex.current === null) return;
        const newColumnOrder = [...columnOrder];
        const [draggedItem] = newColumnOrder.splice(dragItemIndex.current, 1);
        newColumnOrder.splice(dragOverItemIndex.current, 0, draggedItem);
        setColumnOrder(newColumnOrder);
        dragItemIndex.current = null;
        dragOverItemIndex.current = null;
    };

    return (
        <AdminComponents.TableViewContainer component={Paper}>
            <Table stickyHeader>
                <TableHead>
                    <TableRow>
                        <TableCell padding="checkbox"><Checkbox indeterminate={selectedIds.length > 0 && selectedIds.length < quotations.length} checked={quotations.length > 0 && selectedIds.length === quotations.length} onChange={onSelectAll} /></TableCell>
                        {columnOrder.map((colKey, idx) => (
                            <TableCell
                                key={colKey}
                                draggable
                                onDragStart={() => { dragItemIndex.current = idx; }}
                                onDragEnter={() => { dragOverItemIndex.current = idx; }}
                                onDragEnd={handleDrop}
                                onDragOver={e => e.preventDefault()}
                                style={{ cursor: 'move' }}
                            >
                                <TableSortLabel active={sortColumn === colKey} direction={sortDirection} onClick={() => onHeaderClick(colKey)}>
                                    {ALL_COLUMNS.find(c => c.key === colKey)?.label}
                                </TableSortLabel>
                            </TableCell>
                        ))}
                        <TableCell align="center">Actions</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {quotations.map(q => (
                        <TableRow key={q.id} hover selected={selectedId === q.id} onClick={() => onRowClick(q)}>
                            <TableCell padding="checkbox"><Checkbox checked={selectedIds.includes(q.id)} onChange={() => onSelectOne(q.id)} onClick={e => e.stopPropagation()} /></TableCell>
                            {columnOrder.map(colKey => (
                                <TableCell key={colKey}>{q[colKey]}</TableCell>
                            ))}
                            <TableCell align="center">
                                <ActionButtons quotation={q} onView={onView} onEdit={onEdit} onDelete={onDelete} />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </AdminComponents.TableViewContainer>
    );
};

const QuotationCard = ({ quotation, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox
            className="card-checkbox"
            checked={isChecked}
            onChange={() => onSelect(quotation.id)}
            onClick={e => e.stopPropagation()}
        />
        <AdminComponents.CardActionContainer>
            <ActionButtons quotation={quotation} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.PaddedCardContent>
            <Typography variant="h6" component="div" noWrap>{quotation.name}</Typography>
            <AdminComponents.IndustryTypography color="text.secondary">{quotation.customer}</AdminComponents.IndustryTypography>
            <AdminComponents.StatusBadge ownerState={{ status: quotation.status }} label={quotation.status} size="small" />
            <AdminComponents.CardDivider />
            <Typography variant="body2"><strong>Type:</strong> {quotation.type}</Typography>
            <Typography variant="body2"><strong>Value:</strong> {quotation.value}</Typography>
            <Typography variant="body2"><strong>Owner:</strong> {quotation.owner}</Typography>
        </AdminComponents.PaddedCardContent>
    </AdminComponents.CardBase>
);

const QuotationCompactCard = ({ quotation, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.CardBase isSelected={isSelected} onClick={onClick}>
        <Checkbox className="card-checkbox" checked={isChecked} onChange={() => onSelect(quotation.id)} onClick={e => e.stopPropagation()} />
        <AdminComponents.CardActionContainer>
            <ActionButtons quotation={quotation} onView={onView} onEdit={onEdit} onDelete={onDelete} />
        </AdminComponents.CardActionContainer>
        <AdminComponents.CompactCardContent>
            <div>
                <Typography variant="subtitle1" fontWeight="bold" noWrap>{quotation.name}</Typography>
                <Typography variant="caption" color="text.secondary">{quotation.customer}</Typography>
            </div>
            <AdminComponents.CompactCardFooter>
                <Typography variant="body2" fontWeight="500">{quotation.type}</Typography>
                <AdminComponents.StatusBadge ownerState={{ status: quotation.status }} label={quotation.status} size="small" />
            </AdminComponents.CompactCardFooter>
        </AdminComponents.CompactCardContent>
    </AdminComponents.CardBase>
);

const QuotationListItem = ({ quotation, onClick, isSelected, onSelect, isChecked, onDelete, onEdit, onView }) => (
    <AdminComponents.ListItemCard isSelected={isSelected} onClick={onClick}>
        <AdminComponents.ListItemGrid>
            <Checkbox checked={isChecked} onChange={() => onSelect(quotation.id)} />
            <Box>
                <Typography fontWeight="bold">{quotation.name}</Typography>
                <Typography variant="body2" color="text.secondary">{quotation.customer}</Typography>
            </Box>
            <Typography variant="body2">{quotation.owner}</Typography>
            <Typography variant="body2">{quotation.type}</Typography>
            <AdminComponents.StatusBadge ownerState={{ status: quotation.status }} label={quotation.status} size="small" />
            <AdminComponents.ListItemActions>
                <ActionButtons quotation={quotation} onView={onView} onEdit={onEdit} onDelete={onDelete} />
            </AdminComponents.ListItemActions>
        </AdminComponents.ListItemGrid>
    </AdminComponents.ListItemCard>
);

const QuotationGraph = ({ quotation, chartType }) => {
    const chartRef = React.useRef(null);
    const chartInstance = React.useRef(null);

    React.useEffect(() => {
        if (chartInstance.current) chartInstance.current.destroy();
        if (chartRef.current && quotation) {
            const ctx = chartRef.current.getContext('2d');
            chartInstance.current = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: ['Value'],
                    datasets: [{
                        label: 'Quotation Value',
                        data: [parseInt(quotation.value.replace(/[^0-9]/g, ''))],
                        backgroundColor: ['pie', 'doughnut'].includes(chartType) ? theme.palette.chart.backgrounds : theme.palette.primary.main,
                        borderColor: theme.palette.primary.dark,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { title: { display: true, text: `${quotation.name} - Value` } },
                }
            });
        }
        return () => { if (chartInstance.current) chartInstance.current.destroy(); };
    }, [quotation, chartType]);

    return (
        <>
            {quotation ? <AdminComponents.GraphContainer><canvas ref={chartRef}></canvas></AdminComponents.GraphContainer> : <AdminComponents.CenteredMessage><Typography>Select a quotation to see graph</Typography></AdminComponents.CenteredMessage>}
        </>
    );
};

const ActivityLog = ({ logs }) => (
    <AdminComponents.ActivityLogPaper>
        <AdminComponents.ActivityLogTitle variant="h6" gutterBottom>
            Activity Log
        </AdminComponents.ActivityLogTitle>
        <AdminComponents.ActivityLogListContainer>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                {logs.map((log, index) => (
                    <AdminComponents.ActivityLogListItem key={index} disableGutters>
                        <AdminComponents.ActivityLogIconContainer>
                            <AdminComponents.ActivityLogAvatar>
                                <Schedule fontSize="small" sx={AdminComponents.ActivityLogAvatarIcon} />
                            </AdminComponents.ActivityLogAvatar>
                        </AdminComponents.ActivityLogIconContainer>
                        <Box>
                            <Typography variant="body2" component="span" color="text.secondary">
                                <Typography component="span" fontWeight="bold" color="text.primary">{log.user}</Typography>
                                {' '}{log.action}{' '}
                                {log.target && <AdminComponents.ActivityLogLink component="a" href="#">{log.target}</AdminComponents.ActivityLogLink>}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                                {log.timestamp}
                            </Typography>
                        </Box>
                    </AdminComponents.ActivityLogListItem>
                ))}
            </Box>
        </AdminComponents.ActivityLogListContainer>
    </AdminComponents.ActivityLogPaper>
);

const Quotation = () => {
    const [quotations, setQuotations] = useState(initialQuotations);
    const [selectedQuotation, setSelectedQuotation] = useState(null);
    const [sortColumn, setSortColumn] = useState('quotationId');
    const [sortDirection, setSortDirection] = useState('asc');
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedIds, setSelectedIds] = useState([]);
    const [deleteConfirmation, setDeleteConfirmation] = useState({ isOpen: false, idsToDelete: [] });
    const [columnOrder, setColumnOrder] = useState(ALL_COLUMNS.map(c => c.key));
    const [filter, setFilter] = useState('all');
    const [viewMode, setViewMode] = useState('cards');
    const [isGraphVisible, setIsGraphVisible] = useState(true);
    const [activityLog, setActivityLog] = useState([
        { user: 'Admin', action: 'Created a new quotation', target: 'QT-2024-001', timestamp: '3/15/2023, 10:25:00 AM' },
        { user: 'Manager', action: 'Approved quotation', target: 'QT-2024-002', timestamp: '3/16/2023, 9:00:00 AM' },
        { user: 'Finance', action: 'Marked as expired', target: 'QT-2024-003', timestamp: '3/17/2023, 1:45:00 PM' },
    ]);
    const [chartType, setChartType] = useState('bar');
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [sidebarMode, setSidebarMode] = useState('search');
    const [stagedFilters, setStagedFilters] = useState([]);
    const [activeFilters, setActiveFilters] = useState([]);
    const [filterBuilder, setFilterBuilder] = useState({ field: '', operator: '', value: '' });
    const [groupByKeys, setGroupByKeys] = useState([]);
    // Quick Filter options (status and type)
    const quickFilterOptions = useMemo(() => {
        const statuses = [...new Set(quotations.map(q => q.status))];
        const types = [...new Set(quotations.map(q => q.type))];
        return [...statuses, ...types];
    }, [quotations]);
    const handleAddQuickFilter = (value) => {
        const statusValues = ['Active', 'Pending', 'Expired'];
        const field = statusValues.includes(value) ? 'status' : 'type';
        const newFilter = { field, operator: 'Equals', value, id: Date.now() };
        setStagedFilters([...stagedFilters, newFilter]);
    };
    // Advanced Search Handlers
    const handleAddStagedFilter = () => {
        if (filterBuilder.field && filterBuilder.operator && filterBuilder.value) {
            setStagedFilters([...stagedFilters, { ...filterBuilder, id: Date.now() }]);
            setFilterBuilder({ field: '', operator: '', value: '' });
        }
    };
    const handleApplyFilters = () => {
        setActiveFilters([...activeFilters, ...stagedFilters]);
        setStagedFilters([]);
    };
    const handleResetFilters = () => {
        setStagedFilters([]);
        setActiveFilters([]);
    };
    const handleToggleSidebar = (mode) => {
        const willBeOpen = sidebarMode === mode ? !isSidebarOpen : true;
        setIsSidebarOpen(willBeOpen);
        setSidebarMode(mode);
    };
    const handleColumnVisibilityChange = (columnKey) => {
        const isVisible = columnOrder.includes(columnKey);
        let newOrder;
        if (isVisible) {
            if (columnOrder.length > 1) {
                newOrder = columnOrder.filter(key => key !== columnKey);
            } else {
                return;
            }
        } else {
            const originalKeys = ALL_COLUMNS.map(c => c.key);
            newOrder = originalKeys.filter(key => columnOrder.includes(key) || key === columnKey);
        }
        setColumnOrder(newOrder);
    };
    const handleGroupByChange = (key) => {
        setGroupByKeys(prev =>
            prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
        );
    };

    const processedQuotations = useMemo(() => {
        let current = quotations;
        if (filter !== 'all') current = current.filter(q => q.status.toLowerCase() === filter);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            current = current.filter(q => q.quotationId.toLowerCase().includes(term) || q.customer.toLowerCase().includes(term) || q.name.toLowerCase().includes(term));
        }
        // Advanced filters
        if (activeFilters.length > 0) {
            current = current.filter(quotation => {
                return activeFilters.every(filter => {
                    const { field, operator, value } = filter;
                    const quotationValue = String(quotation[field]).toLowerCase();
                    const filterValue = String(value).toLowerCase();
                    switch (operator) {
                        case 'Equals': return quotationValue === filterValue;
                        case 'Not Equals': return quotationValue !== filterValue;
                        case 'Contains': return quotationValue.includes(filterValue);
                        case 'Starts With': return quotationValue.startsWith(filterValue);
                        case 'Ends With': return quotationValue.endsWith(filterValue);
                        default: return true;
                    }
                });
            });
        }
        // Sorting
        return [...current].sort((a, b) => {
            const valA = a[sortColumn], valB = b[sortColumn];
            if (valA === valB) return 0;
            if (typeof valA === 'string') return sortDirection === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
            return sortDirection === 'asc' ? valA - valB : valB - valA;
        });
    }, [quotations, filter, searchTerm, sortColumn, sortDirection, activeFilters]);

    const summaryStats = useMemo(() => ({
        total: quotations.length,
        active: quotations.filter(q => q.status === 'Active').length,
        expired: quotations.filter(q => q.status === 'Expired').length,
        pending: quotations.filter(q => q.status === 'Pending').length,
    }), [quotations]);

    const handleDeleteRequest = (ids) => setDeleteConfirmation({ isOpen: true, idsToDelete: ids });
    const handleShowDetails = (quotation) => setSelectedQuotation(quotation);
    const handleSelectAll = (e) => setSelectedIds(e.target.checked ? processedQuotations.map(q => q.id) : []);
    const handleSelectOne = (id) => setSelectedIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);

    const displayQuotation = useMemo(() => {
        const isSelectedVisible = processedQuotations.some(q => q.id === selectedQuotation?.id);
        if (isSelectedVisible) return selectedQuotation;
        return processedQuotations.length > 0 ? processedQuotations[0] : null;
    }, [processedQuotations, selectedQuotation]);

    const renderCurrentView = () => (
        <AdminComponents.ViewContainer>
            {processedQuotations.length > 0 ? (
                <>
                    {viewMode === 'cards' && <AdminComponents.GridView>{processedQuotations.map(q => <QuotationCard key={q.id} quotation={q} onClick={() => setSelectedQuotation(q)} isSelected={displayQuotation?.id === q.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(q.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.GridView>}
                    {viewMode === 'grid' && (
                        <QuotationTable
                            quotations={processedQuotations}
                            onRowClick={setSelectedQuotation}
                            onHeaderClick={(col) => { setSortColumn(col); setSortDirection(d => d === 'asc' ? 'desc' : 'asc') }}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            selectedId={displayQuotation?.id}
                            selectedIds={selectedIds}
                            onSelectAll={handleSelectAll}
                            onSelectOne={handleSelectOne}
                            columnOrder={columnOrder}
                            setColumnOrder={setColumnOrder}
                            onDelete={handleDeleteRequest}
                            onEdit={handleShowDetails}
                            onView={handleShowDetails}
                        />
                    )}
                    {viewMode === 'compact' && <AdminComponents.CompactView>{processedQuotations.map(q => <QuotationCompactCard key={q.id} quotation={q} onClick={() => setSelectedQuotation(q)} isSelected={displayQuotation?.id === q.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(q.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.CompactView>}
                    {viewMode === 'list' && <AdminComponents.ListView>{processedQuotations.map(q => <QuotationListItem key={q.id} quotation={q} onClick={() => setSelectedQuotation(q)} isSelected={displayQuotation?.id === q.id} onSelect={handleSelectOne} isChecked={selectedIds.includes(q.id)} onDelete={handleDeleteRequest} onEdit={handleShowDetails} onView={handleShowDetails} />)}</AdminComponents.ListView>}
                </>
            ) : (
                <AdminComponents.CenteredMessage component={Paper}>
                    <AdminComponents.LargeIcon color="disabled" />
                    <Typography variant="h6">No Matching Quotations</Typography>
                    <Typography color="text.secondary">Try adjusting your search term or filters.</Typography>
                </AdminComponents.CenteredMessage>
            )}
        </AdminComponents.ViewContainer>
    );

    return (
        <ThemeProvider theme={theme}>
            <AdminComponents.AppContainer>
                <AdminComponents.AppBody isSidebarOpen={isSidebarOpen}>
                    <AdminComponents.MainContentArea isSidebarOpen={isSidebarOpen}>
                        <AdminComponents.TopSectionWrapper>
                            <AdminComponents.TopSectionContent>
                                <AdminComponents.SummaryCardsContainer>
                                    <AdminComponents.SummaryCard isActive={filter === 'all'} onClick={() => setFilter('all')}>
                                        <AdminComponents.SummaryAvatar variant="total"><People /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.total}</Typography><Typography variant="body2">Total Quotations</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'active'} onClick={() => setFilter('active')}>
                                        <AdminComponents.SummaryAvatar variant="active"><CheckCircle /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.active}</Typography><Typography variant="body2">Active</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'pending'} onClick={() => setFilter('pending')}>
                                        <AdminComponents.SummaryAvatar variant="warning"><Schedule /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.pending}</Typography><Typography variant="body2">Pending</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                    <AdminComponents.SummaryCard isActive={filter === 'expired'} onClick={() => setFilter('expired')}>
                                        <AdminComponents.SummaryAvatar variant="inactive"><Cancel /></AdminComponents.SummaryAvatar>
                                        <Box><Typography variant="h6">{summaryStats.expired}</Typography><Typography variant="body2">Expired</Typography></Box>
                                    </AdminComponents.SummaryCard>
                                </AdminComponents.SummaryCardsContainer>
                                <AdminComponents.TopSectionActions>
                                    <Button variant="contained" startIcon={<Add />}>Add Quotation</Button>
                                    <Button variant="outlined" startIcon={<BarChart />} onClick={() => setIsGraphVisible(v => !v)}>Graphs</Button>
                                </AdminComponents.TopSectionActions>
                            </AdminComponents.TopSectionContent>
                        </AdminComponents.TopSectionWrapper>
                        <AdminComponents.ControlsSection>
                            <AdminComponents.ControlsGroup>
                                <TextField variant="outlined" size="small" placeholder="Search..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} InputProps={{ startAdornment: <Search color="disabled" /> }} />
                                <FormControlLabel control={<Checkbox onChange={handleSelectAll} checked={processedQuotations.length > 0 && selectedIds.length === processedQuotations.length} indeterminate={selectedIds.length > 0 && selectedIds.length < processedQuotations.length} />} label="Select All" />
                                {selectedIds.length > 0 && <Button variant="outlined" color="error" startIcon={<Delete />} onClick={() => handleDeleteRequest(selectedIds)}>Delete ({selectedIds.length})</Button>}
                            </AdminComponents.ControlsGroup>
                            <AdminComponents.ControlsGroup>
                                <Button variant="outlined" startIcon={<FilterAlt />} onClick={() => handleToggleSidebar('search')}>Advanced Search</Button>
                                <Button variant="outlined" startIcon={<GridView />} onClick={() => handleToggleSidebar('grid')}>Table Settings</Button>
                                <AdminComponents.StyledToggleButtonGroup
                                    size="small"
                                    value={viewMode}
                                    exclusive
                                    onChange={(e, v) => v && setViewMode(v)}
                                >
                                    <ToggleButton value="cards" title="Card View"><ViewModule />Card</ToggleButton>
                                    <ToggleButton value="compact" title="Compact View"><Apps />Compact</ToggleButton>
                                    <ToggleButton value="list" title="List View"><ViewList />List</ToggleButton>
                                    <ToggleButton value="grid" title="Table View"><GridView />Table</ToggleButton>
                                </AdminComponents.StyledToggleButtonGroup>
                            </AdminComponents.ControlsGroup>
                        </AdminComponents.ControlsSection>
                        <AdminComponents.ContentBody>
                            <AdminComponents.MainLeftPane>{renderCurrentView()}</AdminComponents.MainLeftPane>
                            <AdminComponents.DetailsPane isCollapsed={!isGraphVisible}>
                                <AdminComponents.ChartTypeSelectorContainer>
                                    <AdminComponents.StyledToggleButtonGroup
                                        value={chartType}
                                        exclusive
                                        onChange={(e, newType) => newType && setChartType(newType)}
                                        size="small"
                                        fullWidth
                                    >
                                        <ToggleButton value="bar" title="Bar Chart"><BarChart />Bar</ToggleButton>
                                        <ToggleButton value="line" title="Line Chart"><ShowChart />Line</ToggleButton>
                                        <ToggleButton value="pie" title="Pie Chart"><PieChart />Pie</ToggleButton>
                                        <ToggleButton value="doughnut" title="Doughnut Chart"><DonutLarge />Donut</ToggleButton>
                                    </AdminComponents.StyledToggleButtonGroup>
                                </AdminComponents.ChartTypeSelectorContainer>
                                <QuotationGraph quotation={displayQuotation} chartType={chartType} />
                            </AdminComponents.DetailsPane>
                        </AdminComponents.ContentBody>
                        <ActivityLog logs={activityLog} />
                    </AdminComponents.MainContentArea>
                    {/* Sidebar Drawer for Advanced Search and Table Settings */}
                    <Drawer
                        variant="persistent"
                        anchor="right"
                        open={isSidebarOpen}
                    >
                        <AdminComponents.SidebarContainer>
                            <AdminComponents.SidebarHeader>
                                <Typography variant="h6">
                                    {sidebarMode === 'search' ? 'Advanced Search' : 'Table Settings'}
                                </Typography>
                                <IconButton onClick={() => setIsSidebarOpen(false)}>
                                    <Cancel />
                                </IconButton>
                            </AdminComponents.SidebarHeader>
                            <AdminComponents.SidebarContent>
                                {sidebarMode === 'search' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Quick Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.QuickFilterContainer>
                                                {quickFilterOptions.map(opt => (
                                                    <Chip key={opt} label={opt} onClick={() => handleAddQuickFilter(opt)} />
                                                ))}
                                            </AdminComponents.QuickFilterContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Filter Builder</AdminComponents.SidebarSectionTitle>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Field</InputLabel>
                                                <Select value={filterBuilder.field} label="Field" onChange={e => setFilterBuilder(prev => ({ ...prev, field: e.target.value }))}>
                                                    {ALL_COLUMNS.map(col => <MenuItem key={col.key} value={col.key}>{col.label}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <FormControl fullWidth size="small">
                                                <InputLabel>Operator</InputLabel>
                                                <Select value={filterBuilder.operator} label="Operator" onChange={e => setFilterBuilder(prev => ({ ...prev, operator: e.target.value }))}>
                                                    {FILTER_OPERATORS.map(op => <MenuItem key={op} value={op}>{op}</MenuItem>)}
                                                </Select>
                                            </FormControl>
                                            <TextField label="Value" variant="outlined" size="small" fullWidth value={filterBuilder.value} onChange={e => setFilterBuilder(prev => ({ ...prev, value: e.target.value }))}/>
                                            <Button variant="outlined" fullWidth onClick={handleAddStagedFilter}>Add Filter</Button>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Staged Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {stagedFilters.length > 0 ? stagedFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setStagedFilters(stagedFilters.filter(sf => sf.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters staged.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Active Filters</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {activeFilters.length > 0 ? activeFilters.map(f => (
                                                    <Chip key={f.id} label={`${ALL_COLUMNS.find(c=>c.key === f.field)?.label} ${f.operator} "${f.value}"`} onDelete={() => setActiveFilters(activeFilters.filter(af => af.id !== f.id))} />
                                                )) : <Typography variant="body2" color="text.secondary">No filters active.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Visible Columns</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.ColumnActionContainer>
                                                <Button size="small" onClick={() => setColumnOrder(ALL_COLUMNS.map(c => c.key))}>Select All</Button>
                                                <Button size="small" onClick={() => setColumnOrder(columnOrder.length > 1 ? [columnOrder[0]] : columnOrder)}>Deselect All</Button>
                                            </AdminComponents.ColumnActionContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={<Checkbox checked={columnOrder.includes(col.key)} onChange={() => handleColumnVisibilityChange(col.key)} name={col.key} />}
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                        <AdminComponents.SidebarSection>
                                            <AdminComponents.SidebarSectionTitle>Group By</AdminComponents.SidebarSectionTitle>
                                            <AdminComponents.FilterChipContainer>
                                                {groupByKeys.length > 0 ? groupByKeys.map(key => (
                                                    <Chip
                                                        key={key}
                                                        label={ALL_COLUMNS.find(c => c.key === key)?.label}
                                                        onDelete={() => handleGroupByChange(key)}
                                                    />
                                                )) : <Typography variant="body2" color="text.secondary">None selected.</Typography>}
                                            </AdminComponents.FilterChipContainer>
                                            <AdminComponents.ColumnVisibilityContainer>
                                                {ALL_COLUMNS.map(col => (
                                                    <FormControlLabel
                                                        key={col.key}
                                                        control={
                                                            <Checkbox
                                                                checked={groupByKeys.includes(col.key)}
                                                                onChange={() => handleGroupByChange(col.key)}
                                                            />
                                                        }
                                                        label={col.label}
                                                    />
                                                ))}
                                            </AdminComponents.ColumnVisibilityContainer>
                                        </AdminComponents.SidebarSection>
                                    </>
                                )}
                            </AdminComponents.SidebarContent>
                            <AdminComponents.SidebarFooter>
                                {sidebarMode === 'search' && (
                                    <>
                                        <Button variant="outlined" onClick={handleResetFilters}>Reset</Button>
                                        <Button variant="contained" color="primary" onClick={handleApplyFilters}>Apply</Button>
                                    </>
                                )}
                                {sidebarMode === 'grid' && (
                                    <Button variant="contained" fullWidth onClick={() => setIsSidebarOpen(false)}>Close</Button>
                                )}
                            </AdminComponents.SidebarFooter>
                        </AdminComponents.SidebarContainer>
                    </Drawer>
                </AdminComponents.AppBody>
            </AdminComponents.AppContainer>
        </ThemeProvider>
    );
};

export default Quotation;
